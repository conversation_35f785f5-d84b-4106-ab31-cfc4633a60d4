import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

// Chart libraries - مكتبات الرسوم البيانية
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../utils/responsive_helper.dart';
import '../../services/api/system_statistics_api_service.dart';

// تعليق استيراد المكتبات المستخدمة سابقاً
// import 'package:fl_chart/fl_chart.dart';

/// نموذج بيانات للرسوم البيانية
class ChartData {
  final String category;
  final double value;
  final Color color;

  ChartData(this.category, this.value, this.color);
}

/// تبويب التحليلات
///
/// يوفر واجهة لعرض إحصائيات وتحليلات النظام
class AnalyticsTab extends StatefulWidget {
  const AnalyticsTab({super.key});

  @override
  State<AnalyticsTab> createState() => _AnalyticsTabState();
}

class _AnalyticsTabState extends State<AnalyticsTab> {

  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();

  // خدمة الإحصائيات من API
  final SystemStatisticsApiService _statisticsApiService = SystemStatisticsApiService();

  // بيانات الإحصائيات من API
  final RxMap<String, dynamic> _systemStatistics = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> _tasksStatistics = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> _usersStatistics = <String, dynamic>{}.obs;
  final RxBool _isLoadingStatistics = false.obs;

  @override
  void initState() {
    super.initState();

    // تحميل البيانات
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    try {
      _isLoadingStatistics.value = true;

      // تحميل البيانات من API والكونترولرز بشكل متوازي
      await Future.wait([
        _loadStatisticsFromApi(),
        _taskController.loadAllTasks(),
        _userController.loadAllUsers(),
      ]);
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات التحليلات: $e');
    } finally {
      _isLoadingStatistics.value = false;
    }
  }

  /// تحميل الإحصائيات من API
  Future<void> _loadStatisticsFromApi() async {
    try {
      // جلب الإحصائيات من API
      final systemStats = await _statisticsApiService.getSystemStatistics();
      final tasksStats = await _statisticsApiService.getTasksStatistics();
      final usersStats = await _statisticsApiService.getUsersStatistics();

      _systemStatistics.value = systemStats;
      _tasksStatistics.value = tasksStats;
      _usersStatistics.value = usersStats;
    } catch (e) {
      debugPrint('خطأ في تحميل الإحصائيات من API: $e');
      // في حالة الخطأ، استخدم البيانات المحلية
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          Expanded(
            child: _buildAnalyticsContent(),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'التحليلات والإحصائيات',
          style: AppStyles.titleLarge,
        ),
        ElevatedButton.icon(
          onPressed: _loadData,
          icon: const Icon(Icons.refresh),
          label: const Text('تحديث البيانات'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  /// بناء محتوى التحليلات
  Widget _buildAnalyticsContent() {
    return Obx(() {
      if (_taskController.isLoading || _userController.isLoading || _isLoadingStatistics.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      // استخدام ListView بدلاً من SingleChildScrollView مع Column
      // Using ListView instead of SingleChildScrollView with Column
      return ListView(
        children: [
          ResponsiveHelper.isDesktop(context)
              ? _buildDesktopLayout()
              : _buildMobileLayout(),
        ],
      );
    });
  }

  /// بناء تخطيط سطح المكتب
  Widget _buildDesktopLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // استخدام الحد الأدنى من المساحة
      children: [
        // صف الإحصائيات العامة - استخدام بيانات API مع fallback للبيانات المحلية
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي المستخدمين',
                (_systemStatistics['totalUsers'] ?? _userController.users.length).toString(),
                Icons.people,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'إجمالي المهام',
                (_systemStatistics['totalTasks'] ?? _taskController.allTasks.length).toString(),
                Icons.task,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'المهام المكتملة',
                (_systemStatistics['completedTasks'] ?? _countCompletedTasks()).toString(),
                Icons.check_circle,
                Colors.teal,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'المهام المتأخرة',
                (_systemStatistics['overdueTasks'] ?? _countOverdueTasks()).toString(),
                Icons.warning,
                Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // صف الرسوم البيانية
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسم بياني دائري لحالة المهام - Syncfusion Charts
            Expanded(
              flex: 1,
              child: SizedBox(
                height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
                child: _buildCard(
                  'توزيع المهام حسب الحالة',
                  _buildSyncfusionTaskStatusPieChart(),
                ),
              ),
            ),
            const SizedBox(width: 16),

            // رسم بياني شريطي للمهام حسب الشهر - Syncfusion Charts
            Expanded(
              flex: 2,
              child: SizedBox(
                height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
                child: _buildCard(
                  'المهام حسب الشهر',
                  _buildSyncfusionTasksByMonthBarChart(),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // صف الرسوم البيانية الإضافية
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسم بياني للمستخدمين النشطين - Syncfusion Charts
            Expanded(
              child: SizedBox(
                height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
                child: _buildCard(
                  'المستخدمين النشطين وغير النشطين',
                  _buildSyncfusionActiveUsersChart(),
                ),
              ),
            ),
            const SizedBox(width: 16),

            // رسم بياني للمهام حسب الأولوية - Syncfusion Charts
            Expanded(
              child: SizedBox(
                height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
                child: _buildCard(
                  'توزيع المهام حسب الأولوية',
                  _buildSyncfusionTasksByPriorityChart(),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // جدول أحدث النشاطات
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'أحدث النشاطات',
            _buildRecentActivitiesTable(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
          ),
        ),
      ],
    );
  }

  /// بناء تخطيط الجوال
  Widget _buildMobileLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // استخدام الحد الأدنى من المساحة
      children: [
        // الإحصائيات العامة - استخدام بيانات API مع fallback للبيانات المحلية
        _buildStatCard(
          'إجمالي المستخدمين',
          (_systemStatistics['totalUsers'] ?? _userController.users.length).toString(),
          Icons.people,
          Colors.blue,
        ),
        const SizedBox(height: 16),
        _buildStatCard(
          'إجمالي المهام',
          (_systemStatistics['totalTasks'] ?? _taskController.allTasks.length).toString(),
          Icons.task,
          Colors.green,
        ),
        const SizedBox(height: 16),
        _buildStatCard(
          'المهام المكتملة',
          (_systemStatistics['completedTasks'] ?? _countCompletedTasks()).toString(),
          Icons.check_circle,
          Colors.teal,
        ),
        const SizedBox(height: 16),
        _buildStatCard(
          'المهام المتأخرة',
          (_systemStatistics['overdueTasks'] ?? _countOverdueTasks()).toString(),
          Icons.warning,
          Colors.red,
        ),
        const SizedBox(height: 24),

        // رسم بياني دائري لحالة المهام - Syncfusion Charts
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'توزيع المهام حسب الحالة',
            _buildSyncfusionTaskStatusPieChart(),
          ),
        ),
        const SizedBox(height: 24),

        // رسم بياني شريطي للمهام حسب الشهر - Syncfusion Charts
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'المهام حسب الشهر',
            _buildSyncfusionTasksByMonthBarChart(),
          ),
        ),
        const SizedBox(height: 24),

        // رسم بياني للمستخدمين النشطين - Syncfusion Charts
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'المستخدمين النشطين وغير النشطين',
            _buildSyncfusionActiveUsersChart(),
          ),
        ),
        const SizedBox(height: 24),

        // رسم بياني للمهام حسب الأولوية - Syncfusion Charts
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'توزيع المهام حسب الأولوية',
            _buildSyncfusionTasksByPriorityChart(),
          ),
        ),
        const SizedBox(height: 24),

        // جدول أحدث النشاطات
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'أحدث النشاطات',
            _buildRecentActivitiesTable(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppStyles.titleSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              value,
              style: AppStyles.titleLarge.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة
  Widget _buildCard(String title, Widget content) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // استخدام الحد الأدنى من المساحة
          children: [
            Text(
              title,
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 8),
            // استخدام Expanded بدلاً من Flexible لضمان أخذ المساحة المتاحة
            Expanded(
              child: content,
            ),
          ],
        ),
      ),
    );
  }

  // ===== Chart Widgets - الرسوم البيانية الجديدة =====

  /// بناء رسم بياني دائري لحالة المهام باستخدام Syncfusion Charts
  Widget _buildSyncfusionTaskStatusPieChart() {
    // استخدام بيانات API أولاً، ثم البيانات المحلية كـ fallback
    List<dynamic> tasksByStatus = [];

    if (_systemStatistics.isNotEmpty && _systemStatistics['tasksByStatus'] != null) {
      tasksByStatus = _systemStatistics['tasksByStatus'];
    } else if (_tasksStatistics.isNotEmpty && _tasksStatistics['tasksByStatus'] != null) {
      tasksByStatus = _tasksStatistics['tasksByStatus'];
    } else {
      // استخدام البيانات المحلية كـ fallback
      tasksByStatus = [
        {'status': 'قيد الانتظار', 'count': _countTasksByStatusId(1)},
        {'status': 'قيد التنفيذ', 'count': _countTasksByStatusId(2)},
        {'status': 'في انتظار المراجعة', 'count': _countTasksByStatusId(3)},
        {'status': 'مكتملة', 'count': _countTasksByStatusId(4)},
      ];
    }

    // التحقق من وجود بيانات
    if (tasksByStatus.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات لعرضها'),
      );
    }

    // تحويل البيانات إلى نموذج مناسب لـ Syncfusion
    final List<ChartData> chartData = tasksByStatus.map((item) {
      final status = item['status'] ?? item['Status'] ?? 'غير محدد';
      final count = (item['count'] ?? item['Count'] ?? 0).toDouble();

      // تحديد اللون حسب الحالة
      Color color = AppColors.primary;
      if (status.contains('انتظار') || status.toLowerCase().contains('pending')) {
        color = AppColors.statusPending;
      } else if (status.contains('تنفيذ') || status.toLowerCase().contains('progress')) {
        color = AppColors.statusInProgress;
      } else if (status.contains('مراجعة') || status.toLowerCase().contains('waiting')) {
        color = AppColors.statusWaitingForInfo;
      } else if (status.contains('مكتمل') || status.toLowerCase().contains('completed')) {
        color = AppColors.statusCompleted;
      }

      return ChartData(status, count, color);
    }).toList();

    return SfCircularChart(
      // العنوان
      title: ChartTitle(
        text: '',
        textStyle: AppStyles.titleSmall,
      ),

      // الأسطورة (Legend)
      legend: Legend(
        isVisible: true,
        position: LegendPosition.right,
        textStyle: AppStyles.bodyMedium,
        overflowMode: LegendItemOverflowMode.wrap,
      ),

      // تفعيل التفاعل
      enableMultiSelection: false,

      // السلسلة
      series: <CircularSeries>[
        PieSeries<ChartData, String>(
          dataSource: chartData,
          xValueMapper: (ChartData data, _) => data.category,
          yValueMapper: (ChartData data, _) => data.value,
          pointColorMapper: (ChartData data, _) => data.color,

          // عرض التسميات
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            useSeriesColor: true,
            textStyle: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),

          // تفعيل الانتقاء
          selectionBehavior: SelectionBehavior(
            enable: true,
            selectedColor: Colors.grey.shade300,
            unselectedColor: Colors.grey.shade600,
          ),

          // تفعيل التفجير عند النقر
          explode: true,
          explodeIndex: 0,
          explodeOffset: '10%',

          // نصف القطر
          radius: '80%',

          // تفعيل الرسوم المتحركة
          animationDuration: 1000,
        ),
      ],

      // تفعيل التلميحات
      tooltipBehavior: TooltipBehavior(
        enable: true,
        format: 'point.x: point.y مهمة',
        textStyle: AppStyles.bodySmall.copyWith(color: Colors.white),
        color: Colors.black87,
      ),
    );
  }

  // تعليق الدالة القديمة
  /*
  /// بناء رسم بياني دائري لحالة المهام (الطريقة القديمة - fl_chart)
  Widget _buildTaskStatusPieChart() {
    // التحقق من وجود مهام
    if (_taskController.allTasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام لعرضها'),
      );
    }

    // حساب عدد المهام لكل حالة (استخدام أرقام الحالات)
    final pendingCount = _countTasksByStatusId(1); // Pending
    final inProgressCount = _countTasksByStatusId(2); // In Progress
    final waitingCount = _countTasksByStatusId(3); // Waiting for Info
    final completedCount = _countTasksByStatusId(4); // Completed

    // التحقق من وجود بيانات للعرض
    if (pendingCount == 0 && inProgressCount == 0 && waitingCount == 0 && completedCount == 0) {
      return const Center(
        child: Text('لا توجد بيانات كافية لعرض الرسم البياني'),
      );
    }

    // استخدام SizedBox لتحديد ارتفاع ثابت للرسم البياني
    // Using SizedBox to set a fixed height for the chart
    return SizedBox(
      height: 250, // ارتفاع ثابت للرسم البياني
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: AspectRatio(
              aspectRatio: 1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على شكل دائري
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: pendingCount.toDouble(),
                      title: '$pendingCount',
                      color: AppColors.statusPending,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: inProgressCount.toDouble(),
                      title: '$inProgressCount',
                      color: AppColors.statusInProgress,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: waitingCount.toDouble(),
                      title: '$waitingCount',
                      color: AppColors.statusWaitingForInfo,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: completedCount.toDouble(),
                      title: '$completedCount',
                      color: AppColors.statusCompleted,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                  startDegreeOffset: 180,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLegendItem('قيد الانتظار', AppColors.statusPending, pendingCount),
              const SizedBox(height: 8),
              _buildLegendItem('قيد التنفيذ', AppColors.statusInProgress, inProgressCount),
              const SizedBox(height: 8),
              _buildLegendItem('في انتظار المراجعة', AppColors.statusWaitingForInfo, waitingCount),
              const SizedBox(height: 8),
              _buildLegendItem('مكتملة', AppColors.statusCompleted, completedCount),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر وسيلة الإيضاح
  Widget _buildLegendItem(String label, Color color, int count) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          color: color,
        ),
        const SizedBox(width: 8),
        Text('$label ($count)'),
      ],
    );
  }

  /// بناء رسم بياني شريطي للمهام حسب الشهر
  Widget _buildTasksByMonthBarChart() {
    final tasks = _taskController.allTasks;

    // التحقق من وجود مهام
    if (tasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام لعرضها'),
      );
    }

    // تجميع المهام حسب الشهر
    final Map<int, int> tasksByMonth = {};

    // تهيئة الأشهر
    for (int i = 1; i <= 12; i++) {
      tasksByMonth[i] = 0;
    }

    // حساب عدد المهام لكل شهر
    for (final task in tasks) {
      final month = task.createdAtDateTime.month;
      tasksByMonth[month] = (tasksByMonth[month] ?? 0) + 1;
    }

    // حساب القيمة القصوى للمحور Y مع التأكد من أنها ليست صفرًا
    // Calculate the maximum Y value ensuring it's not zero
    final maxValue = tasksByMonth.values.isEmpty
        ? 10.0 // قيمة افتراضية إذا كانت القائمة فارغة
        : tasksByMonth.values.reduce((a, b) => a > b ? a : b).toDouble();

    // التأكد من أن القيمة القصوى ليست صفرًا أو قيمة غير محددة لتجنب مشاكل العرض
    // Ensure the maximum value is not zero, NaN, or Infinity to avoid display issues
    double safeMaxY;
    if (maxValue <= 0 || maxValue.isNaN || maxValue.isInfinite) {
      safeMaxY = 10.0; // قيمة افتراضية آمنة
    } else {
      safeMaxY = maxValue * 1.2;
    }

    // استخدام AspectRatio لضمان نسبة عرض إلى ارتفاع ثابتة للرسم البياني
    return AspectRatio(
      aspectRatio: 1.5, // نسبة العرض إلى الارتفاع 1.5:1
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: safeMaxY,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final month = group.x + 1;
                final monthName = DateFormat('MMMM').format(DateTime(DateTime.now().year, month));
                // التأكد من أن القيمة ليست NaN أو Infinity قبل تحويلها إلى عدد صحيح
                final value = rod.toY.isFinite ? rod.toY.toInt() : 0;
                return BarTooltipItem(
                  '$monthName: $value',
                  const TextStyle(color: Colors.white),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final month = value.toInt() + 1;
                  return SideTitleWidget(
                    meta: meta,
                    child: Text(
                      DateFormat('MMM').format(DateTime(DateTime.now().year, month)),
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                },
                reservedSize: 30,
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  if (value == 0) return const SizedBox.shrink();
                  // التأكد من أن القيمة ليست NaN أو Infinity قبل تحويلها إلى عدد صحيح
                  final intValue = value.isFinite ? value.toInt() : 0;
                  return SideTitleWidget(
                    meta: meta,
                    child: Text(
                      intValue.toString(),
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                },
                reservedSize: 30,
              ),
            ),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          barGroups: tasksByMonth.entries.map((entry) {
            return BarChartGroupData(
              x: entry.key - 1,
              barRods: [
                BarChartRodData(
                  toY: entry.value.toDouble(),
                  color: AppColors.primary,
                  width: 16,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  /// بناء رسم بياني للمستخدمين النشطين
  Widget _buildActiveUsersChart() {
    final users = _userController.users;

    // التحقق من وجود مستخدمين
    if (users.isEmpty) {
      return const Center(
        child: Text('لا يوجد مستخدمين لعرضهم'),
      );
    }

    // حساب عدد المستخدمين النشطين وغير النشطين
    final activeCount = users.where((user) => user.isActive).length;
    final inactiveCount = users.length - activeCount;

    // التحقق من وجود بيانات للعرض
    if (activeCount == 0 && inactiveCount == 0) {
      return const Center(
        child: Text('لا توجد بيانات كافية لعرض الرسم البياني'),
      );
    }

    // استخدام SizedBox لتحديد ارتفاع ثابت للرسم البياني
    // Using SizedBox to set a fixed height for the chart
    return SizedBox(
      height: 250, // ارتفاع ثابت للرسم البياني
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: AspectRatio(
              aspectRatio: 1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على شكل دائري
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: activeCount.toDouble(),
                      title: '$activeCount',
                      color: Colors.green,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: inactiveCount.toDouble(),
                      title: '$inactiveCount',
                      color: Colors.red,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                  startDegreeOffset: 180,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLegendItem('نشط', Colors.green, activeCount),
              const SizedBox(height: 8),
              _buildLegendItem('غير نشط', Colors.red, inactiveCount),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء رسم بياني للمهام حسب الأولوية
  Widget _buildTasksByPriorityChart() {
    final tasks = _taskController.allTasks;

    // التحقق من وجود مهام
    if (tasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام لعرضها'),
      );
    }

    // حساب عدد المهام لكل أولوية (استخدام أرقام الأولوية)
    final lowCount = tasks.where((task) => task.priority == 1).length; // Low
    final mediumCount = tasks.where((task) => task.priority == 2).length; // Medium
    final highCount = tasks.where((task) => task.priority == 3).length; // High
    final urgentCount = tasks.where((task) => task.priority == 4).length; // Urgent

    // التحقق من وجود بيانات للعرض
    if (lowCount == 0 && mediumCount == 0 && highCount == 0 && urgentCount == 0) {
      return const Center(
        child: Text('لا توجد بيانات كافية لعرض الرسم البياني'),
      );
    }

    // استخدام SizedBox لتحديد ارتفاع ثابت للرسم البياني
    // Using SizedBox to set a fixed height for the chart
    return SizedBox(
      height: 250, // ارتفاع ثابت للرسم البياني
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: AspectRatio(
              aspectRatio: 1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على شكل دائري
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: lowCount.toDouble(),
                      title: '$lowCount',
                      color: Colors.blue,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: mediumCount.toDouble(),
                      title: '$mediumCount',
                      color: Colors.green,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: highCount.toDouble(),
                      title: '$highCount',
                      color: Colors.orange,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: urgentCount.toDouble(),
                      title: '$urgentCount',
                      color: Colors.red,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                  startDegreeOffset: 180,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLegendItem('منخفضة', Colors.blue, lowCount),
              const SizedBox(height: 8),
              _buildLegendItem('متوسطة', Colors.green, mediumCount),
              const SizedBox(height: 8),
              _buildLegendItem('عالية', Colors.orange, highCount),
              const SizedBox(height: 8),
              _buildLegendItem('عاجلة', Colors.red, urgentCount),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء جدول أحدث النشاطات
  Widget _buildRecentActivitiesTable() {
    // استخدام قائمة فارغة مؤقتاً حتى يتم ربط AdminController
    final logs = <dynamic>[];

    if (logs.isEmpty) {
      return const Center(
        child: Text('لا توجد نشاطات حديثة'),
      );
    }

    // التأكد من وجود سجلات قبل إنشاء الجدول
    final logsToShow = logs.take(10).toList();
    if (logsToShow.isEmpty) {
      return const Center(
        child: Text('لا توجد نشاطات حديثة'),
      );
    }

    return SingleChildScrollView(
      child: DataTable(
        columns: const [
          DataColumn(label: Text('المستخدم')),
          DataColumn(label: Text('الإجراء')),
          DataColumn(label: Text('التفاصيل')),
          DataColumn(label: Text('التاريخ')),
        ],
        rows: logsToShow.map((log) {
          // محاولة العثور على المستخدم، أو استخدام مستخدم افتراضي إذا لم يتم العثور عليه
          final user = _userController.users.isNotEmpty
              ? _userController.users.firstWhere(
                  (user) => user.id == log.userId,
                  orElse: () => _userController.users.first,
                )
              : User(
                  id: 0,
                  name: 'مستخدم غير معروف',
                  email: '',
                  role: UserRole.user,
                  isActive: true,
                  createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
                );

          return DataRow(
            cells: [
              DataCell(Text(user.name)),
              DataCell(Text(log.action)),
              DataCell(Text(log.details)),
              DataCell(Text(_formatDateTime(log.timestamp))),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// حساب عدد المهام حسب معرف الحالة
  int _countTasksByStatusId(int statusId) {
    return _taskController.allTasks.where((task) => task.status == statusId).length;
  }

  /// حساب عدد المهام المكتملة
  int _countCompletedTasks() {
    return _taskController.allTasks.where((task) => task.status == 4).length; // 4 = Completed
  }

  /// حساب عدد المهام المتأخرة
  int _countOverdueTasks() {
    return _taskController.allTasks.where((task) => task.isOverdue()).length;
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }

  // إغلاق التعليق للكود القديم
  */

  // ===== الدوال المساعدة المفقودة =====

  /// بناء رسم بياني شريطي للمهام حسب الشهر باستخدام custom bar chart
  Widget _buildSyncfusionTasksByMonthBarChart() {
    final tasks = _taskController.allTasks;

    // التحقق من وجود مهام
    if (tasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام لعرضها'),
      );
    }

    // تجميع المهام حسب الشهر
    final Map<int, int> tasksByMonth = {};

    // تهيئة الأشهر
    for (int i = 1; i <= 12; i++) {
      tasksByMonth[i] = 0;
    }

    // حساب عدد المهام لكل شهر
    for (final task in tasks) {
      final month = task.createdAtDateTime.month;
      tasksByMonth[month] = (tasksByMonth[month] ?? 0) + 1;
    }

    // العثور على أقصى قيمة للتطبيع
    final maxValue = tasksByMonth.values.isEmpty ? 1 : tasksByMonth.values.reduce((a, b) => a > b ? a : b);

    return Column(
      children: [
        // عنوان الرسم البياني
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            'المهام حسب الشهر',
            style: AppStyles.titleSmall,
          ),
        ),
        const SizedBox(height: 16),

        // الرسم البياني الشريطي المخصص
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: tasksByMonth.entries.map((entry) {
                final monthName = DateFormat('MMM').format(DateTime(DateTime.now().year, entry.key));
                final value = entry.value;
                final normalizedHeight = maxValue > 0 ? (value / maxValue) * 200 : 0.0;

                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // القيمة فوق الشريط
                      if (value > 0)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            value.toString(),
                            style: AppStyles.bodySmall.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),

                      // الشريط
                      Container(
                        width: 30,
                        height: normalizedHeight,
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(4),
                            topRight: Radius.circular(4),
                          ),
                        ),
                      ),

                      const SizedBox(height: 8),

                      // اسم الشهر
                      Text(
                        monthName,
                        style: AppStyles.bodySmall,
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء رسم بياني للمستخدمين النشطين باستخدام Syncfusion Charts
  Widget _buildSyncfusionActiveUsersChart() {
    // استخدام بيانات API أولاً، ثم البيانات المحلية كـ fallback
    List<dynamic> usersByStatus = [];

    if (_systemStatistics.isNotEmpty && _systemStatistics['usersByStatus'] != null) {
      usersByStatus = _systemStatistics['usersByStatus'];
    } else if (_usersStatistics.isNotEmpty && _usersStatistics['usersByStatus'] != null) {
      usersByStatus = _usersStatistics['usersByStatus'];
    } else {
      // استخدام البيانات المحلية كـ fallback
      final users = _userController.users;
      final activeCount = users.where((user) => user.isActive).length;
      final inactiveCount = users.length - activeCount;

      usersByStatus = [
        {'status': 'نشط', 'count': activeCount},
        {'status': 'غير نشط', 'count': inactiveCount},
      ];
    }

    // التحقق من وجود بيانات
    if (usersByStatus.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات لعرضها'),
      );
    }

    // تحويل البيانات إلى نموذج مناسب لـ Syncfusion
    final List<ChartData> chartData = usersByStatus.map((item) {
      final status = item['status'] ?? item['Status'] ?? 'غير محدد';
      final count = (item['count'] ?? item['Count'] ?? 0).toDouble();

      // تحديد اللون حسب الحالة
      Color color = status.contains('نشط') || status.toLowerCase().contains('active')
          ? Colors.green
          : Colors.red;

      return ChartData(status, count, color);
    }).toList();

    return SfCircularChart(
      // العنوان
      title: ChartTitle(
        text: '',
        textStyle: AppStyles.titleSmall,
      ),

      // الأسطورة (Legend)
      legend: Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        textStyle: AppStyles.bodyMedium,
        overflowMode: LegendItemOverflowMode.wrap,
      ),

      // تفعيل التفاعل
      enableMultiSelection: false,

      // السلسلة
      series: <CircularSeries>[
        DoughnutSeries<ChartData, String>(
          dataSource: chartData,
          xValueMapper: (ChartData data, _) => data.category,
          yValueMapper: (ChartData data, _) => data.value,
          pointColorMapper: (ChartData data, _) => data.color,

          // عرض التسميات
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            useSeriesColor: true,
            textStyle: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),

          // تفعيل الانتقاء
          selectionBehavior: SelectionBehavior(
            enable: true,
            selectedColor: Colors.grey.shade300,
            unselectedColor: Colors.grey.shade600,
          ),

          // نصف القطر الداخلي والخارجي
          radius: '80%',
          innerRadius: '40%',

          // تفعيل الرسوم المتحركة
          animationDuration: 1000,
        ),
      ],

      // تفعيل التلميحات
      tooltipBehavior: TooltipBehavior(
        enable: true,
        format: 'point.x: point.y مستخدم',
        textStyle: AppStyles.bodySmall.copyWith(color: Colors.white),
        color: Colors.black87,
      ),
    );
  }

  /// بناء رسم بياني للمهام حسب الأولوية باستخدام custom horizontal bar chart
  Widget _buildSyncfusionTasksByPriorityChart() {
    // استخدام بيانات API أولاً، ثم البيانات المحلية كـ fallback
    List<dynamic> tasksByPriority = [];

    if (_systemStatistics.isNotEmpty && _systemStatistics['tasksByPriority'] != null) {
      tasksByPriority = _systemStatistics['tasksByPriority'];
    } else if (_tasksStatistics.isNotEmpty && _tasksStatistics['tasksByPriority'] != null) {
      tasksByPriority = _tasksStatistics['tasksByPriority'];
    } else {
      // استخدام البيانات المحلية كـ fallback
      final tasks = _taskController.allTasks;
      final lowCount = tasks.where((task) => task.priority == 1).length; // Low
      final mediumCount = tasks.where((task) => task.priority == 2).length; // Medium
      final highCount = tasks.where((task) => task.priority == 3).length; // High
      final urgentCount = tasks.where((task) => task.priority == 4).length; // Urgent

      tasksByPriority = [
        {'priority': 'منخفضة', 'count': lowCount},
        {'priority': 'متوسطة', 'count': mediumCount},
        {'priority': 'عالية', 'count': highCount},
        {'priority': 'عاجلة', 'count': urgentCount},
      ];
    }

    // التحقق من وجود بيانات
    if (tasksByPriority.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات لعرضها'),
      );
    }

    // العثور على أقصى قيمة للتطبيع
    final maxValue = tasksByPriority.map((item) => (item['count'] ?? item['Count'] ?? 0) as int)
        .fold(0, (prev, element) => element > prev ? element : prev);

    return Column(
      children: [
        // عنوان الرسم البياني
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            'المهام حسب الأولوية',
            style: AppStyles.titleSmall,
          ),
        ),
        const SizedBox(height: 16),

        // الرسم البياني الشريطي الأفقي المخصص
        Expanded(
          child: ListView.builder(
            itemCount: tasksByPriority.length,
            itemBuilder: (context, index) {
              final item = tasksByPriority[index];
              final priority = item['priority'] ?? item['Priority'] ?? 'غير محدد';
              final count = (item['count'] ?? item['Count'] ?? 0) as int;
              final normalizedWidth = maxValue > 0 ? (count / maxValue) * 200 : 0.0;

              // تحديد اللون حسب الأولوية
              Color color = Colors.blue;
              if (priority.contains('منخفض') || priority.toLowerCase().contains('low')) {
                color = Colors.blue;
              } else if (priority.contains('متوسط') || priority.toLowerCase().contains('medium')) {
                color = Colors.green;
              } else if (priority.contains('عالي') || priority.toLowerCase().contains('high')) {
                color = Colors.orange;
              } else if (priority.contains('عاجل') || priority.toLowerCase().contains('urgent')) {
                color = Colors.red;
              }

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                child: Row(
                  children: [
                    // اسم الأولوية
                    SizedBox(
                      width: 80,
                      child: Text(
                        priority,
                        style: AppStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // الشريط الأفقي
                    Expanded(
                      child: Row(
                        children: [
                          Container(
                            width: normalizedWidth,
                            height: 30,
                            decoration: BoxDecoration(
                              color: color,
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(4),
                                bottomRight: Radius.circular(4),
                              ),
                            ),
                            child: Center(
                              child: count > 0 ? Text(
                                count.toString(),
                                style: AppStyles.bodySmall.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ) : null,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء جدول أحدث النشاطات
  Widget _buildRecentActivitiesTable() {
    // استخدام قائمة فارغة مؤقتاً حتى يتم ربط ActivityLogsApiService
    final logs = <dynamic>[];

    if (logs.isEmpty) {
      return const Center(
        child: Text('لا توجد نشاطات حديثة'),
      );
    }

    return SingleChildScrollView(
      child: DataTable(
        columns: const [
          DataColumn(label: Text('المستخدم')),
          DataColumn(label: Text('الإجراء')),
          DataColumn(label: Text('التفاصيل')),
          DataColumn(label: Text('التاريخ')),
        ],
        rows: const [],
      ),
    );
  }

  // ===== الدوال المساعدة المفقودة =====

  /// حساب عدد المهام حسب معرف الحالة
  int _countTasksByStatusId(int statusId) {
    return _taskController.allTasks.where((task) => task.status == statusId).length;
  }

  /// حساب عدد المهام المكتملة
  int _countCompletedTasks() {
    return _taskController.allTasks.where((task) => task.status == 4).length; // 4 = Completed
  }

  /// حساب عدد المهام المتأخرة
  int _countOverdueTasks() {
    return _taskController.allTasks.where((task) => task.isOverdue()).length;
  }


}
