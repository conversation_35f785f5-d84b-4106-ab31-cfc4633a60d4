import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/archive_models.dart';
import 'package:get/get.dart';

import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/screens/widgets/custom_app_bar.dart';

import 'package:flutter_application_2/screens/widgets/common/loading_indicator.dart';

/// شاشة تعديل وثيقة أرشيف مبسطة
class EditDocumentScreen extends StatefulWidget {
  /// الوثيقة المراد تعديلها
  final ArchiveDocument document;

  const EditDocumentScreen({
    super.key,
    required this.document,
  });

  @override
  State<EditDocumentScreen> createState() => _EditDocumentScreenState();
}

class _EditDocumentScreenState extends State<EditDocumentScreen> {

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late TextEditingController _titleController;
  late TextEditingController _descriptionController;

  final bool _isLoading = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  /// تهيئة وحدات التحكم بالنصوص
  void _initializeControllers() {
    _titleController = TextEditingController(text: widget.document.title);
    _descriptionController = TextEditingController(text: widget.document.description ?? '');
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'تعديل وثيقة: ${widget.document.title}',
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات الوثيقة الأساسية
                    Card(
                      elevation: 2,
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'معلومات الوثيقة الأساسية',
                              style: AppStyles.headline6,
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _titleController,
                              decoration: const InputDecoration(
                                labelText: 'عنوان الوثيقة',
                                hintText: 'أدخل عنوان الوثيقة',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال عنوان الوثيقة';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _descriptionController,
                              decoration: const InputDecoration(
                                labelText: 'وصف الوثيقة',
                                hintText: 'أدخل وصف الوثيقة',
                                border: OutlineInputBorder(),
                              ),
                              maxLines: 3,
                            ),
                          ],
                        ),
                      ),
                    ),

                    // أزرار الإجراءات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Get.back(),
                          child: const Text('إلغاء'),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: _isSaving ? null : _saveDocument,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                          ),
                          child: _isSaving
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text('حفظ التغييرات'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  /// حفظ الوثيقة
  Future<void> _saveDocument() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSaving = true;
      });

      try {
        // محاكاة حفظ الوثيقة
        await Future.delayed(const Duration(seconds: 1));

        Get.back(result: true);
        Get.snackbar(
          'تم بنجاح',
          'تم تحديث الوثيقة بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        debugPrint('خطأ في حفظ الوثيقة: $e');
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء تحديث الوثيقة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } finally {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
