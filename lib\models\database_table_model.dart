/// نموذج جدول قاعدة البيانات
///
/// يمثل جدول في قاعدة البيانات مع أعمدته وخصائصه
class DatabaseTable {
  final String name;
  final String displayName;
  final List<DatabaseColumn> columns;
  final int recordCount;
  final DateTime? lastModified;
  final String? description;
  final bool isEditable;
  final bool isDeletable;
  final bool isCreatable;
  final bool isExportable;
  final bool isImportable;

  const DatabaseTable({
    required this.name,
    required this.displayName,
    required this.columns,
    this.recordCount = 0,
    this.lastModified,
    this.description,
    this.isEditable = true,
    this.isDeletable = true,
    this.isCreatable = true,
    this.isExportable = true,
    this.isImportable = true,
  });

  /// إنشاء نسخة من JSON
  factory DatabaseTable.fromJson(Map<String, dynamic> json) {
    return DatabaseTable(
      name: json['name'] ?? '',
      displayName: json['displayName'] ?? json['name'] ?? '',
      columns: (json['columns'] as List<dynamic>?)
          ?.map((col) => DatabaseColumn.fromJson(col))
          .toList() ?? [],
      recordCount: json['recordCount'] ?? 0,
      lastModified: json['lastModified'] != null
          ? DateTime.parse(json['lastModified'])
          : null,
      description: json['description'],
      isEditable: json['isEditable'] ?? true,
      isDeletable: json['isDeletable'] ?? true,
      isCreatable: json['isCreatable'] ?? true,
      isExportable: json['isExportable'] ?? true,
      isImportable: json['isImportable'] ?? true,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'displayName': displayName,
      'columns': columns.map((col) => col.toJson()).toList(),
      'recordCount': recordCount,
      'lastModified': lastModified?.toIso8601String(),
      'description': description,
      'isEditable': isEditable,
      'isDeletable': isDeletable,
      'isCreatable': isCreatable,
      'isExportable': isExportable,
      'isImportable': isImportable,
    };
  }

  /// إنشاء نسخة معدلة
  DatabaseTable copyWith({
    String? name,
    String? displayName,
    List<DatabaseColumn>? columns,
    int? recordCount,
    DateTime? lastModified,
    String? description,
    bool? isEditable,
    bool? isDeletable,
    bool? isCreatable,
    bool? isExportable,
    bool? isImportable,
  }) {
    return DatabaseTable(
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      columns: columns ?? this.columns,
      recordCount: recordCount ?? this.recordCount,
      lastModified: lastModified ?? this.lastModified,
      description: description ?? this.description,
      isEditable: isEditable ?? this.isEditable,
      isDeletable: isDeletable ?? this.isDeletable,
      isCreatable: isCreatable ?? this.isCreatable,
      isExportable: isExportable ?? this.isExportable,
      isImportable: isImportable ?? this.isImportable,
    );
  }

  /// الحصول على العمود الأساسي
  DatabaseColumn? get primaryKeyColumn {
    final primaryColumns = columns.where((col) => col.isPrimaryKey);
    return primaryColumns.isNotEmpty ? primaryColumns.first : null;
  }

  /// الحصول على الأعمدة المطلوبة
  List<DatabaseColumn> get requiredColumns {
    return columns.where((col) => col.isRequired).toList();
  }

  /// الحصول على الأعمدة القابلة للتحرير
  List<DatabaseColumn> get editableColumns {
    return columns.where((col) => !col.isPrimaryKey).toList();
  }

  @override
  String toString() {
    return 'DatabaseTable(name: $name, displayName: $displayName, columns: ${columns.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DatabaseTable && other.name == name;
  }

  @override
  int get hashCode => name.hashCode;
}

/// نموذج عمود قاعدة البيانات
///
/// يمثل عمود في جدول قاعدة البيانات
class DatabaseColumn {
  final String name;
  final String? displayName;
  final DatabaseColumnType type;
  final bool isNullable;
  final bool isPrimaryKey;
  final bool isRequired;
  final bool isAutoIncrement;
  final dynamic defaultValue;
  final int? maxLength;
  final String? description;
  final List<String>? enumValues;
  final bool isEditable;
  final bool isVisibleInList;
  final bool isSearchable;
  final bool isSortable;
  final List<dynamic>? allowedValues;
  final List<String>? allowedValueNames;
  final String? foreignKeyTable;
  final String? foreignKeyColumn;
  final String? foreignKeyDisplayColumn;

  const DatabaseColumn({
    required this.name,
    this.displayName,
    required this.type,
    this.isNullable = true,
    this.isPrimaryKey = false,
    this.isRequired = false,
    this.isAutoIncrement = false,
    this.defaultValue,
    this.maxLength,
    this.description,
    this.enumValues,
    this.isEditable = true,
    this.isVisibleInList = true,
    this.isSearchable = true,
    this.isSortable = true,
    this.allowedValues,
    this.allowedValueNames,
    this.foreignKeyTable,
    this.foreignKeyColumn,
    this.foreignKeyDisplayColumn,
  });

  /// إنشاء نسخة من JSON
  factory DatabaseColumn.fromJson(Map<String, dynamic> json) {
    return DatabaseColumn(
      name: json['name'] ?? '',
      displayName: json['displayName'],
      type: DatabaseColumnType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => DatabaseColumnType.text,
      ),
      isNullable: json['isNullable'] ?? true,
      isPrimaryKey: json['isPrimaryKey'] ?? false,
      isRequired: json['isRequired'] ?? false,
      isAutoIncrement: json['isAutoIncrement'] ?? false,
      defaultValue: json['defaultValue'],
      maxLength: json['maxLength'],
      description: json['description'],
      enumValues: (json['enumValues'] as List<dynamic>?)?.cast<String>(),
      isEditable: json['isEditable'] ?? true,
      isVisibleInList: json['isVisibleInList'] ?? true,
      isSearchable: json['isSearchable'] ?? true,
      isSortable: json['isSortable'] ?? true,
      allowedValues: json['allowedValues'] as List<dynamic>?,
      allowedValueNames: (json['allowedValueNames'] as List<dynamic>?)?.cast<String>(),
      foreignKeyTable: json['foreignKeyTable'],
      foreignKeyColumn: json['foreignKeyColumn'],
      foreignKeyDisplayColumn: json['foreignKeyDisplayColumn'],
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'displayName': displayName,
      'type': type.name,
      'isNullable': isNullable,
      'isPrimaryKey': isPrimaryKey,
      'isRequired': isRequired,
      'isAutoIncrement': isAutoIncrement,
      'defaultValue': defaultValue,
      'maxLength': maxLength,
      'description': description,
      'enumValues': enumValues,
      'isEditable': isEditable,
      'isVisibleInList': isVisibleInList,
      'isSearchable': isSearchable,
      'isSortable': isSortable,
      'allowedValues': allowedValues,
      'allowedValueNames': allowedValueNames,
      'foreignKeyTable': foreignKeyTable,
      'foreignKeyColumn': foreignKeyColumn,
      'foreignKeyDisplayColumn': foreignKeyDisplayColumn,
    };
  }

  /// إنشاء نسخة معدلة
  DatabaseColumn copyWith({
    String? name,
    String? displayName,
    DatabaseColumnType? type,
    bool? isNullable,
    bool? isPrimaryKey,
    bool? isRequired,
    bool? isAutoIncrement,
    dynamic defaultValue,
    int? maxLength,
    String? description,
    List<String>? enumValues,
    bool? isEditable,
    bool? isVisibleInList,
    bool? isSearchable,
    bool? isSortable,
    List<dynamic>? allowedValues,
    List<String>? allowedValueNames,
    String? foreignKeyTable,
    String? foreignKeyColumn,
    String? foreignKeyDisplayColumn,
  }) {
    return DatabaseColumn(
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      type: type ?? this.type,
      isNullable: isNullable ?? this.isNullable,
      isPrimaryKey: isPrimaryKey ?? this.isPrimaryKey,
      isRequired: isRequired ?? this.isRequired,
      isAutoIncrement: isAutoIncrement ?? this.isAutoIncrement,
      defaultValue: defaultValue ?? this.defaultValue,
      maxLength: maxLength ?? this.maxLength,
      description: description ?? this.description,
      enumValues: enumValues ?? this.enumValues,
      isEditable: isEditable ?? this.isEditable,
      isVisibleInList: isVisibleInList ?? this.isVisibleInList,
      isSearchable: isSearchable ?? this.isSearchable,
      isSortable: isSortable ?? this.isSortable,
      allowedValues: allowedValues ?? this.allowedValues,
      allowedValueNames: allowedValueNames ?? this.allowedValueNames,
      foreignKeyTable: foreignKeyTable ?? this.foreignKeyTable,
      foreignKeyColumn: foreignKeyColumn ?? this.foreignKeyColumn,
      foreignKeyDisplayColumn: foreignKeyDisplayColumn ?? this.foreignKeyDisplayColumn,
    );
  }

  /// الحصول على معرف العمود
  String get id => name;

  /// الحصول على الاسم المعروض
  String get effectiveDisplayName => displayName ?? _getDisplayNameFromFieldName(name);

  /// تحويل اسم الحقل إلى اسم معروض
  String _getDisplayNameFromFieldName(String fieldName) {
    switch (fieldName.toLowerCase()) {
      case 'id':
        return 'المعرف';
      case 'name':
        return 'الاسم';
      case 'firstname':
        return 'الاسم الأول';
      case 'lastname':
        return 'الاسم الأخير';
      case 'email':
        return 'البريد الإلكتروني';
      case 'username':
        return 'اسم المستخدم';
      case 'password':
        return 'كلمة المرور';
      case 'role':
        return 'الدور';
      case 'departmentid':
        return 'القسم';
      case 'isactive':
        return 'نشط';
      case 'isdeleted':
        return 'محذوف';
      case 'createdat':
        return 'تاريخ الإنشاء';
      case 'updatedat':
        return 'تاريخ التحديث';
      case 'title':
        return 'العنوان';
      case 'description':
        return 'الوصف';
      case 'status':
        return 'الحالة';
      case 'priority':
        return 'الأولوية';
      case 'startdate':
        return 'تاريخ البداية';
      case 'duedate':
        return 'تاريخ الاستحقاق';
      case 'completionpercentage':
        return 'نسبة الإنجاز';
      case 'estimatedtime':
        return 'الوقت المقدر';
      case 'creatorid':
        return 'المنشئ';
      case 'assigneeid':
        return 'المكلف';
      default:
        return fieldName;
    }
  }

  @override
  String toString() {
    return 'DatabaseColumn(name: $name, type: $type, isPrimaryKey: $isPrimaryKey)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DatabaseColumn && other.name == name;
  }

  @override
  int get hashCode => name.hashCode;
}

/// أنواع أعمدة قاعدة البيانات
enum DatabaseColumnType {
  text,
  integer,
  decimal,
  boolean,
  datetime,
  date,
  time,
  email,
  url,
  phone,
  json,
  binary,
  uuid,
  password,
  real,
  color,
  enumType,
  foreignKey,
  markdown,
  richText,
  code,
  image,
  file,
}

/// امتداد لأنواع الأعمدة
extension DatabaseColumnTypeExtension on DatabaseColumnType {
  /// الحصول على الاسم المعروض
  String get displayName {
    switch (this) {
      case DatabaseColumnType.text:
        return 'نص';
      case DatabaseColumnType.integer:
        return 'رقم صحيح';
      case DatabaseColumnType.decimal:
        return 'رقم عشري';
      case DatabaseColumnType.boolean:
        return 'منطقي';
      case DatabaseColumnType.datetime:
        return 'تاريخ ووقت';
      case DatabaseColumnType.date:
        return 'تاريخ';
      case DatabaseColumnType.time:
        return 'وقت';
      case DatabaseColumnType.email:
        return 'بريد إلكتروني';
      case DatabaseColumnType.url:
        return 'رابط';
      case DatabaseColumnType.phone:
        return 'هاتف';
      case DatabaseColumnType.json:
        return 'JSON';
      case DatabaseColumnType.binary:
        return 'بيانات ثنائية';
      case DatabaseColumnType.uuid:
        return 'معرف فريد';
      case DatabaseColumnType.password:
        return 'كلمة مرور';
      case DatabaseColumnType.real:
        return 'رقم حقيقي';
      case DatabaseColumnType.color:
        return 'لون';
      case DatabaseColumnType.enumType:
        return 'قائمة محددة';
      case DatabaseColumnType.foreignKey:
        return 'مفتاح خارجي';
      case DatabaseColumnType.markdown:
        return 'ماركداون';
      case DatabaseColumnType.richText:
        return 'نص منسق';
      case DatabaseColumnType.code:
        return 'كود';
      case DatabaseColumnType.image:
        return 'صورة';
      case DatabaseColumnType.file:
        return 'ملف';
    }
  }

  /// الحصول على أيقونة النوع
  String get icon {
    switch (this) {
      case DatabaseColumnType.text:
        return '📝';
      case DatabaseColumnType.integer:
        return '🔢';
      case DatabaseColumnType.decimal:
        return '💯';
      case DatabaseColumnType.boolean:
        return '☑️';
      case DatabaseColumnType.datetime:
      case DatabaseColumnType.date:
      case DatabaseColumnType.time:
        return '📅';
      case DatabaseColumnType.email:
        return '📧';
      case DatabaseColumnType.url:
        return '🔗';
      case DatabaseColumnType.phone:
        return '📞';
      case DatabaseColumnType.json:
        return '📋';
      case DatabaseColumnType.binary:
        return '📁';
      case DatabaseColumnType.uuid:
        return '🆔';
      case DatabaseColumnType.password:
        return '🔒';
      case DatabaseColumnType.real:
        return '🔢';
      case DatabaseColumnType.color:
        return '🎨';
      case DatabaseColumnType.enumType:
        return '📋';
      case DatabaseColumnType.foreignKey:
        return '🔗';
      case DatabaseColumnType.markdown:
        return '📝';
      case DatabaseColumnType.richText:
        return '📝';
      case DatabaseColumnType.code:
        return '💻';
      case DatabaseColumnType.image:
        return '🖼️';
      case DatabaseColumnType.file:
        return '📁';
    }
  }
}
