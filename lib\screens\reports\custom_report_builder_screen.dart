import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../constants/app_styles.dart';
import '../../constants/app_colors.dart';
import '../../controllers/auth_controller.dart';
import '../../services/api/reports_api_service.dart';
import '../../services/api/users_api_service.dart';
import '../../services/api/departments_api_service.dart';

import '../../models/user_model.dart';
import '../../models/department_model.dart';
import '../../models/report_models.dart';

/// شاشة بناء التقارير المخصصة - نسخة مبسطة
class CustomReportBuilderScreen extends StatefulWidget {
  final String title;
  final String? description;
  final String reportType;
  final int? reportId;

  const CustomReportBuilderScreen({
    super.key,
    required this.title,
    this.description,
    required this.reportType,
    this.reportId,
  });

  @override
  State<CustomReportBuilderScreen> createState() => _CustomReportBuilderScreenState();
}

class _CustomReportBuilderScreenState extends State<CustomReportBuilderScreen> {
  final ReportsApiService _reportsApiService = ReportsApiService();
  final UsersApiService _usersApiService = UsersApiService();
  final DepartmentsApiService _departmentsApiService = DepartmentsApiService();
  final AuthController _authController = Get.find<AuthController>();

  List<User> _allUsers = [];
  List<Department> _allDepartments = [];

  bool _isLoading = true;
  String? _errorMessage;

  // تواريخ البداية والنهاية
  DateTime? _startDate;
  DateTime? _endDate;

  // المستخدمون المحددون
  final List<int> _selectedUserIds = [];

  // الأقسام المحددة
  final List<int> _selectedDepartmentIds = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل المستخدمين
      _allUsers = await _usersApiService.getAllUsers();

      // تحميل الأقسام
      _allDepartments = await _departmentsApiService.getAllDepartments();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
      });
    }
  }

  /// حفظ التقرير
  Future<void> _saveReport() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigatorContext = Navigator.of(context);

    // عرض مؤشر التقدم
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(widget.reportId != null ? 'جاري تحديث التقرير...' : 'جاري حفظ التقرير...'),
          ],
        ),
      ),
    );

    try {
      // إنشاء معايير التقرير
      final criteria = {
        'startDate': _startDate?.toIso8601String(),
        'endDate': _endDate?.toIso8601String(),
        'userIds': _selectedUserIds.isEmpty ? null : _selectedUserIds,
        'departmentIds': _selectedDepartmentIds.isEmpty ? null : _selectedDepartmentIds,
      };

      if (widget.reportId != null) {
        // تحديث تقرير موجود
        final existingReport = await _reportsApiService.getReportById(widget.reportId!);

        if (existingReport != null) {
          // تحديث التقرير
          final updatedReport = Report(
            id: widget.reportId!,
            title: widget.title,
            description: widget.description,
            reportType: _convertStringToReportType(widget.reportType),
            query: criteria.toString(),
            createdBy: existingReport.createdBy,
            createdAt: existingReport.createdAt,
          );

          await _reportsApiService.updateReport(widget.reportId!, updatedReport);
        } else {
          throw Exception('لم يتم العثور على التقرير للتعديل');
        }
      } else {
        // إنشاء تقرير جديد
        final report = Report(
          id: 0, // سيتم تعيينه من الخادم
          title: widget.title,
          description: widget.description,
          reportType: _convertStringToReportType(widget.reportType),
          query: criteria.toString(),
          createdBy: _authController.currentUser.value?.id ?? 1,
          createdAt: DateTime.now().millisecondsSinceEpoch,
        );

        await _reportsApiService.createReport(report);
      }

      if (!mounted) return;

      // إغلاق مؤشر التقدم
      navigatorContext.pop();

      // إغلاق الشاشة
      navigatorContext.pop(true);

      // عرض رسالة النجاح
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(widget.reportId != null ? 'تم تحديث التقرير بنجاح' : 'تم حفظ التقرير بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // إغلاق مؤشر التقدم
      navigatorContext.pop();

      // عرض رسالة الخطأ
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء ${widget.reportId != null ? 'تحديث' : 'حفظ'} التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// تحويل النص إلى نوع التقرير
  ReportType _convertStringToReportType(String reportTypeString) {
    switch (reportTypeString.toLowerCase()) {
      case 'task_summary':
        return ReportType.taskSummary;
      case 'task_status':
        return ReportType.taskStatus;
      case 'task_progress':
        return ReportType.taskProgress;
      case 'task_details':
        return ReportType.taskDetails;
      case 'task_completion':
        return ReportType.taskCompletion;
      case 'user_activity':
        return ReportType.userActivity;
      case 'user_performance':
        return ReportType.userPerformance;
      case 'department_performance':
        return ReportType.departmentPerformance;
      case 'department_workload':
        return ReportType.departmentWorkload;
      case 'time_tracking':
        return ReportType.timeTracking;
      case 'project_progress':
        return ReportType.projectProgress;
      case 'project_status':
        return ReportType.projectStatus;
      case 'system_usage':
        return ReportType.systemUsage;
      case 'custom':
        return ReportType.custom;
      default:
        return ReportType.custom;
    }
  }

  /// عرض منتقي التاريخ
  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? (_startDate ?? DateTime.now()) : (_endDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء تقرير'),
        actions: [
          ElevatedButton.icon(
            icon: const Icon(Icons.save, color: Colors.white),
            label: const Text('حفظ', style: TextStyle(color: Colors.white)),
            onPressed: _saveReport,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
              elevation: 3,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _buildContent(),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReportInfoCard(),
          const SizedBox(height: 24),
          _buildDateRangeSection(),
          const SizedBox(height: 24),
          _buildFilterSection(),
        ],
      ),
    );
  }

  Widget _buildReportInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات التقرير',
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('عنوان التقرير'),
              subtitle: Text(widget.title),
              leading: const Icon(Icons.title),
            ),
            if (widget.description != null)
              ListTile(
                title: const Text('وصف التقرير'),
                subtitle: Text(widget.description!),
                leading: const Icon(Icons.description),
              ),
            ListTile(
              title: const Text('نوع التقرير'),
              subtitle: Text(widget.reportType),
              leading: const Icon(Icons.category),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نطاق التاريخ',
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ListTile(
                    title: const Text('تاريخ البداية'),
                    subtitle: Text(_startDate != null
                        ? DateFormat('yyyy-MM-dd').format(_startDate!)
                        : 'غير محدد'),
                    leading: const Icon(Icons.calendar_today),
                    onTap: () => _selectDate(context, true),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.clear),
                  tooltip: 'مسح تاريخ البداية',
                  onPressed: _startDate == null
                      ? null
                      : () {
                          setState(() {
                            _startDate = null;
                          });
                        },
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  child: ListTile(
                    title: const Text('تاريخ النهاية'),
                    subtitle: Text(_endDate != null
                        ? DateFormat('yyyy-MM-dd').format(_endDate!)
                        : 'غير محدد'),
                    leading: const Icon(Icons.calendar_today),
                    onTap: () => _selectDate(context, false),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.clear),
                  tooltip: 'مسح تاريخ النهاية',
                  onPressed: _endDate == null
                      ? null
                      : () {
                          setState(() {
                            _endDate = null;
                          });
                        },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معايير التصفية',
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('المستخدمون'),
              subtitle: Text(_selectedUserIds.isEmpty
                  ? 'جميع المستخدمين (${_allUsers.length} متاح)'
                  : '${_selectedUserIds.length} مستخدم محدد من ${_allUsers.length}'),
              leading: const Icon(Icons.people),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                _showUserSelectionDialog();
              },
            ),
            ListTile(
              title: const Text('الأقسام'),
              subtitle: Text(_selectedDepartmentIds.isEmpty
                  ? 'جميع الأقسام (${_allDepartments.length} متاح)'
                  : '${_selectedDepartmentIds.length} قسم محدد من ${_allDepartments.length}'),
              leading: const Icon(Icons.business),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                _showDepartmentSelectionDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// عرض حوار اختيار المستخدمين
  void _showUserSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار المستخدمين'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: _allUsers.isEmpty
              ? const Center(child: Text('لا توجد مستخدمون متاحون'))
              : ListView.builder(
                  itemCount: _allUsers.length,
                  itemBuilder: (context, index) {
                    final user = _allUsers[index];
                    final isSelected = _selectedUserIds.contains(user.id);
                    return CheckboxListTile(
                      title: Text(user.name),
                      subtitle: Text(user.email),
                      value: isSelected,
                      onChanged: (bool? value) {
                        setState(() {
                          if (value == true) {
                            _selectedUserIds.add(user.id);
                          } else {
                            _selectedUserIds.remove(user.id);
                          }
                        });
                      },
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار اختيار الأقسام
  void _showDepartmentSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار الأقسام'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: _allDepartments.isEmpty
              ? const Center(child: Text('لا توجد أقسام متاحة'))
              : ListView.builder(
                  itemCount: _allDepartments.length,
                  itemBuilder: (context, index) {
                    final department = _allDepartments[index];
                    final isSelected = _selectedDepartmentIds.contains(department.id);
                    return CheckboxListTile(
                      title: Text(department.name),
                      subtitle: Text(department.description ?? ''),
                      value: isSelected,
                      onChanged: (bool? value) {
                        setState(() {
                          if (value == true) {
                            _selectedDepartmentIds.add(department.id);
                          } else {
                            _selectedDepartmentIds.remove(department.id);
                          }
                        });
                      },
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
