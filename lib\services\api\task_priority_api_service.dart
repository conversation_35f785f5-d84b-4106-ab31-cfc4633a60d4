import 'package:flutter/foundation.dart';
import '../../models/task_priority_models.dart';
import 'api_service.dart';

/// خدمة API لأولويات المهام - متطابقة مع ASP.NET Core API
class TaskPriorityApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع أولويات المهام
  Future<List<TaskPriority>> getAllPriorities() async {
    try {
      final response = await _apiService.get('/TaskPriorities');
      return _apiService.handleListResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أولويات المهام: $e');
      rethrow;
    }
  }

  /// الحصول على أولوية مهمة بواسطة المعرف
  Future<TaskPriority?> getPriorityById(int id) async {
    try {
      final response = await _apiService.get('/TaskPriorities/$id');
      return _apiService.handleResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أولوية المهمة $id: $e');
      return null;
    }
  }

  /// إنشاء أولوية مهمة جديدة
  Future<TaskPriority> createPriority(TaskPriority priority) async {
    try {
      final response = await _apiService.post(
        '/TaskPriorities',
        priority.toJson(),
      );
      return _apiService.handleResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء أولوية المهمة: $e');
      rethrow;
    }
  }

  /// تحديث أولوية مهمة
  Future<TaskPriority> updatePriority(int id, TaskPriority priority) async {
    try {
      final response = await _apiService.put(
        '/TaskPriorities/$id',
        priority.toJson(),
      );
      return _apiService.handleResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث أولوية المهمة $id: $e');
      rethrow;
    }
  }

  /// حذف أولوية مهمة
  Future<bool> deletePriority(int id) async {
    try {
      final response = await _apiService.delete('/TaskPriorities/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف أولوية المهمة $id: $e');
      return false;
    }
  }

  /// الحصول على الأولويات النشطة فقط
  Future<List<TaskPriority>> getActivePriorities() async {
    try {
      final response = await _apiService.get('/TaskPriorities/active');
      return _apiService.handleListResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأولويات النشطة: $e');
      return [];
    }
  }

  /// البحث في أولويات المهام
  Future<List<TaskPriority>> searchPriorities(String query) async {
    try {
      final response = await _apiService.get('/TaskPriorities/search?q=$query');
      return _apiService.handleListResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في أولويات المهام: $e');
      return [];
    }
  }

  /// الحصول على الأولويات مرتبة حسب المستوى
  Future<List<TaskPriority>> getPrioritiesByLevel() async {
    try {
      final response = await _apiService.get('/TaskPriorities/by-level');
      return _apiService.handleListResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأولويات مرتبة: $e');
      return [];
    }
  }

  /// تفعيل/إلغاء تفعيل أولوية مهمة
  Future<bool> togglePriorityStatus(int id, bool isActive) async {
    try {
      final response = await _apiService.put(
        '/TaskPriorities/$id/toggle-status',
        {
          'isActive': isActive,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تغيير حالة أولوية المهمة: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات أولويات المهام
  Future<Map<String, dynamic>> getPrioritiesStatistics() async {
    try {
      final response = await _apiService.get('/TaskPriorities/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الأولويات: $e');
      return {};
    }
  }

  /// تصدير أولويات المهام
  Future<Map<String, dynamic>> exportPriorities(String format) async {
    try {
      final response = await _apiService.post(
        '/TaskPriorities/export',
        {
          'format': format,
        },
      );
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير أولويات المهام: $e');
      return {};
    }
  }

  /// استيراد أولويات المهام
  Future<bool> importPriorities(List<Map<String, dynamic>> priorities) async {
    try {
      final response = await _apiService.post(
        '/TaskPriorities/import',
        {
          'priorities': priorities,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد أولويات المهام: $e');
      return false;
    }
  }

  /// إعادة ترتيب أولويات المهام
  Future<bool> reorderPriorities(List<Map<String, dynamic>> priorityOrders) async {
    try {
      final response = await _apiService.put(
        '/TaskPriorities/reorder',
        {
          'orders': priorityOrders,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إعادة ترتيب أولويات المهام: $e');
      return false;
    }
  }

  /// الحصول على الأولوية الافتراضية
  Future<TaskPriority?> getDefaultPriority() async {
    try {
      final response = await _apiService.get('/TaskPriorities/default');
      return _apiService.handleResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأولوية الافتراضية: $e');
      return null;
    }
  }

  /// تعيين أولوية كافتراضية
  Future<bool> setDefaultPriority(int id) async {
    try {
      final response = await _apiService.put(
        '/TaskPriorities/$id/set-default',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تعيين الأولوية الافتراضية: $e');
      return false;
    }
  }
}
