import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../../../routes/app_routes.dart';
import '../../../utils/responsive_helper.dart';

/// إجراء البحث في شريط التطبيق
/// يمكن إضافته إلى أي شريط تطبيق للوصول السريع إلى البحث الشامل
class SearchAppBarAction extends StatefulWidget {
  /// حجم الأيقونة
  final double iconSize;

  /// لون الأيقونة
  final Color? iconColor;

  /// نص التلميح
  final String tooltip;

  /// إنشاء إجراء البحث في شريط التطبيق
  const SearchAppBarAction({
    Key? key,
    this.iconSize = 24.0,
    this.iconColor,
    this.tooltip = 'بحث شامل',
  }) : super(key: key);

  @override
  State<SearchAppBarAction> createState() => _SearchAppBarActionState();
}

class _SearchAppBarActionState extends State<SearchAppBarAction> {
  // حالة عرض حقل البحث
  final RxBool _showSearchField = false.obs;

  // تحكم النص
  final TextEditingController _searchController = TextEditingController();

  // مؤشر التركيز
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // التحقق من حجم الشاشة
    final isLargeScreen = ResponsiveHelper.isDesktop(context);
    final isMediumScreen = ResponsiveHelper.isTablet(context);

    return Obx(() {
      // إذا كان حقل البحث مرئيًا
      if (_showSearchField.value) {
        // عرض حقل البحث
        return Container(
          width: isLargeScreen ? 300 : (isMediumScreen ? 250 : 200),
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: TextField(
            controller: _searchController,
            focusNode: _focusNode,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'بحث شامل...',
              hintStyle: const TextStyle(color: Colors.white70),
              filled: true,
              fillColor: Colors.white.withValues(alpha: 0.2),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(20),
                borderSide: const BorderSide(color: Colors.white30),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(20),
                borderSide: const BorderSide(color: Colors.white30),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(20),
                borderSide: const BorderSide(color: Colors.white),
              ),
              prefixIcon: const Icon(Icons.search, color: Colors.white),
              suffixIcon: IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  // إخفاء حقل البحث
                  _showSearchField.value = false;
                  _searchController.clear();
                },
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            onSubmitted: (value) {
              if (value.isNotEmpty) {
                // الانتقال إلى شاشة البحث الشامل
                Get.toNamed(AppRoutes.unifiedSearch, arguments: value);

                // إخفاء حقل البحث
                _showSearchField.value = false;
                _searchController.clear();
              }
            },
          ),
        );
      }

      // إذا كان حقل البحث مخفيًا
      return IconButton(
        icon: Icon(
          Icons.search,
          size: widget.iconSize,
          color: widget.iconColor,
        ),
        tooltip: widget.tooltip,
        onPressed: () {
          // إظهار حقل البحث
          _showSearchField.value = true;

          // التركيز على حقل البحث
          Future.delayed(const Duration(milliseconds: 100), () {
            _focusNode.requestFocus();
          });
        },
      );
    });
  }
}
