import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../models/dashboard_model.dart' as dashboard_model;
import '../../../models/dashboard_widget_model.dart' as widget_model;
import '../../../services/dashboard_service.dart';
import '../../../utils/mouse_event_handler.dart';
import '../../../utils/dashboard_widget_adapter.dart';
import 'draggable_chart_widget.dart';
import 'chart_detail_dialog.dart';

/// مكون شبكة لوحة المعلومات
///
/// يوفر شبكة مرنة لعرض وتنظيم عناصر لوحة المعلومات
/// مع دعم السحب والإفلات وإعادة تحجيم العناصر
class DashboardGrid extends StatefulWidget {
  /// لوحة المعلومات
  final dashboard_model.Dashboard dashboard;

  /// دالة يتم استدعاؤها عند تحديث لوحة المعلومات
  final Function(dashboard_model.Dashboard)? onDashboardUpdated;

  /// دالة يتم استدعاؤها عند النقر على عنصر
  final Function(widget_model.DashboardWidget)? onWidgetTap;

  /// دالة يتم استدعاؤها عند النقر المزدوج على عنصر
  final Function(widget_model.DashboardWidget)? onWidgetDoubleTap;

  /// وضع التحرير
  final bool editMode;

  const DashboardGrid({
    super.key,
    required this.dashboard,
    this.onDashboardUpdated,
    this.onWidgetTap,
    this.onWidgetDoubleTap,
    this.editMode = false,
  });

  @override
  State<DashboardGrid> createState() => _DashboardGridState();
}

class _DashboardGridState extends State<DashboardGrid> {
  // خدمة لوحة المعلومات
  late final DashboardService _dashboardService;

  // العنصر الذي يتم سحبه حاليًا
  widget_model.DashboardWidget? _draggingWidget;

  // مؤشر الصف والعمود للإفلات
  int? _dropRowIndex;
  int? _dropColumnIndex;

  // حجم الخلية
  double _cellWidth = 0;
  final double _cellHeight = 100.0;

  // ألوان حسب السمة - مستوحاة من Monday.com
  final Color _deleteColor = const Color(0xFFE2445C); // أحمر Monday.com
  final Color _expandColor = const Color(0xFFFFCB00); // أصفر Monday.com
  final Color _dragColor = const Color(0xFF00A9FF); // أزرق Monday.com
  final Color _dropTargetColor = const Color(0x3300A9FF); // أزرق شفاف Monday.com

  @override
  void initState() {
    super.initState();
    _dashboardService = Get.find<DashboardService>();
  }

  @override
  Widget build(BuildContext context) {
    // حساب عرض الخلية بناءً على عرض الشاشة وعدد الأعمدة
    _cellWidth = MediaQuery.of(context).size.width / widget.dashboard.gridColumns;

    // تحديد لون الخلفية حسب السمة
    final Color bgColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF181B34) // لون خلفية داكن مستوحى من Monday.com
        : const Color(0xFFF6F7FB); // لون خلفية فاتح مستوحى من Monday.com

    return Container(
      color: bgColor,
      child: Stack(
        children: [
          // شبكة الخلايا
          _buildGridLines(),

          // عناصر لوحة المعلومات
          ...widget.dashboard.widgets.map((dashboardWidget) =>
            _buildDraggableWidget(DashboardWidgetAdapter.convertToWidgetModel(dashboardWidget, widget.dashboard.id))),

          // منطقة الإفلات (تظهر فقط في وضع التحرير وعند سحب عنصر)
          if (widget.editMode && _draggingWidget != null && _dropRowIndex != null && _dropColumnIndex != null)
            _buildDropTarget(),
        ],
      ),
    );
  }

  /// بناء خطوط الشبكة
  Widget _buildGridLines() {
    return CustomPaint(
      size: Size(
        MediaQuery.of(context).size.width,
        widget.dashboard.gridRows * _cellHeight,
      ),
      painter: GridPainter(
        columns: widget.dashboard.gridColumns,
        rows: widget.dashboard.gridRows,
        cellWidth: _cellWidth,
        cellHeight: _cellHeight,
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white.withAlpha(13) // 0.05 * 255 = 13
            : Colors.black.withAlpha(13), // 0.05 * 255 = 13
      ),
    );
  }

  /// بناء منطقة الإفلات
  Widget _buildDropTarget() {
    if (_dropRowIndex == null || _dropColumnIndex == null || _draggingWidget == null) {
      return const SizedBox.shrink();
    }

    final left = _dropColumnIndex! * _cellWidth;
    final top = _dropRowIndex! * _cellHeight;
    final width = _draggingWidget!.size.width;
    final height = _draggingWidget!.size.height;

    return Positioned(
      left: left,
      top: top,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: _dropTargetColor,
          border: Border.all(
            color: _dragColor,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// بناء عنصر قابل للسحب
  Widget _buildDraggableWidget(widget_model.DashboardWidget widget) {
    final left = widget.position.dx;
    final top = widget.position.dy;
    final width = widget.size.width;
    final height = widget.size.height;

    return Positioned(
      left: left,
      top: top,
      child: SizedBox(
        width: width,
        height: height,
        child: _buildWidgetContent(widget),
      ),
    );
  }

  /// بناء محتوى العنصر
  Widget _buildWidgetContent(widget_model.DashboardWidget widget) {
    return Stack(
      children: [
        // المخطط
        DraggableChartWidget(
          widget: widget,
          onDragStarted: this.widget.editMode ? _onDragStarted : null,
          onDragEnd: this.widget.editMode ? _updateWidgetPosition : null,
          onDoubleTap: _showChartDetailDialog,
          onSettingsUpdated: (w, settings) {
            // Convert widget id to int if needed
            final widgetId = w.id;
            _dashboardService.updateWidgetSettings(widgetId, settings);
          },
        ),

        // أزرار التحكم (تظهر فقط في وضع التحرير)
        if (this.widget.editMode) ...[
          // زر الحذف
          Positioned(
            top: 6,
            left: 6,
            child: _buildControlButton(
              icon: Icons.delete_outline_rounded,
              color: _deleteColor,
              bgColor: const Color(0x1AE2445C), // 0.1 opacity
              tooltip: 'حذف العنصر',
              onPressed: () => _deleteWidget(widget),
            ),
          ),

          // زر التوسيع (always show for now since widget_model doesn't have isExpandable)
          Positioned(
            top: 6,
            left: 42,
            child: _buildControlButton(
              icon: Icons.fullscreen,
              color: _expandColor,
              bgColor: const Color(0x1AFFCB00), // 0.1 opacity
              tooltip: 'توسيع العنصر',
              onPressed: () => _toggleWidgetExpansion(widget),
            ),
          ),

          // مؤشر السحب
          Positioned(
            top: 6,
            right: 6,
            child: SafeMouseRegion(
              cursor: SystemMouseCursors.grab,
              child: _buildControlButton(
                icon: Icons.drag_indicator_rounded,
                color: _dragColor,
                bgColor: const Color(0x1A00A9FF), // 0.1 opacity
                tooltip: 'سحب العنصر',
                onPressed: () {
                  // لا شيء، فقط للإشارة إلى أن العنصر قابل للسحب
                  HapticFeedback.selectionClick();
                },
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// بناء زر التحكم
  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    required Color bgColor,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(4),
          child: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: bgColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
        ),
      ),
    );
  }

  /// عند بدء سحب العنصر
  void _onDragStarted(widget_model.DashboardWidget widget) {
    setState(() {
      _draggingWidget = widget;
    });

    // اهتزاز خفيف عند بدء السحب
    HapticFeedback.lightImpact();
  }

  /// تحديث موقع العنصر
  void _updateWidgetPosition(widget_model.DashboardWidget widget, Offset position) {
    if (_dropRowIndex == null || _dropColumnIndex == null) return;

    // تحديث موقع العنصر
    final widgetId = widget.id;
    _dashboardService.updateWidgetPosition(
      widgetId,
      _dropRowIndex!,
      _dropColumnIndex!,
    );

    setState(() {
      _draggingWidget = null;
      _dropRowIndex = null;
      _dropColumnIndex = null;
    });
  }

  /// عرض مربع حوار تفاصيل المخطط
  void _showChartDetailDialog(widget_model.DashboardWidget widget) {
    if (this.widget.onWidgetDoubleTap != null) {
      this.widget.onWidgetDoubleTap!(widget);
    } else {
      // Create default settings for widget_model
      Map<String, dynamic> widgetSettings = {
        'showValues': widget.showValues ?? true,
        'showLabels': widget.showLabels ?? true,
        'showGrid': widget.showGrid ?? true,
        'showLegend': widget.showLegend ?? true,
      };

      // Convert to dashboard_model widget for ChartDetailDialog
      final dashboardWidget = DashboardWidgetAdapter.convertToDashboardModel(widget);
      Get.dialog(
        ChartDetailDialog(
          widget: dashboardWidget,
          settings: widgetSettings,
        ),
      );
    }
  }

  /// حذف العنصر
  void _deleteWidget(widget_model.DashboardWidget widget) {
    // عرض مربع حوار للتأكيد
    Get.defaultDialog(
      title: 'تأكيد الحذف',
      middleText: 'هل أنت متأكد من حذف هذا العنصر؟',
      textConfirm: 'حذف',
      textCancel: 'إلغاء',
      confirmTextColor: Colors.white,
      cancelTextColor: Colors.black,
      buttonColor: _deleteColor,
      onConfirm: () {
        // For now, just show a message since deleteWidget method doesn't exist
        Get.snackbar('تنبيه', 'وظيفة الحذف غير متاحة حاليًا');
        Get.back();
      },
    );
  }

  /// تبديل حالة توسيع العنصر
  void _toggleWidgetExpansion(widget_model.DashboardWidget widget) {
    // For now, just show a message since toggleWidgetExpansion method doesn't exist
    Get.snackbar('تنبيه', 'وظيفة التوسيع غير متاحة حاليًا');
  }
}

/// رسام الشبكة
class GridPainter extends CustomPainter {
  final int columns;
  final int rows;
  final double cellWidth;
  final double cellHeight;
  final Color color;

  GridPainter({
    required this.columns,
    required this.rows,
    required this.cellWidth,
    required this.cellHeight,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1;

    // رسم الخطوط الأفقية
    for (int i = 0; i <= rows; i++) {
      final y = i * cellHeight;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }

    // رسم الخطوط الرأسية
    for (int i = 0; i <= columns; i++) {
      final x = i * cellWidth;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
