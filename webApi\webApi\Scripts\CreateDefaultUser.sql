-- إنشاء مستخدم افتراضي لنظام إدارة المهام
-- يجب تشغيل هذا السكريبت بعد إنشاء قاعدة البيانات

USE [databasetasks]
GO

-- إنشاء الأدوار (Permissions) إذا لم تكن موجودة
IF NOT EXISTS (SELECT 1 FROM permissions WHERE id = 1)
BEGIN
    INSERT INTO permissions (id, name, description, created_at)
    VALUES (1, 'User', 'مستخدم عادي', DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE id = 2)
BEGIN
    INSERT INTO permissions (id, name, description, created_at)
    VALUES (2, 'Supervisor', 'مشرف', DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE id = 3)
BEGIN
    INSERT INTO permissions (id, name, description, created_at)
    VALUES (3, 'Manager', 'مدير', DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE id = 4)
BEGIN
    INSERT INTO permissions (id, name, description, created_at)
    VALUES (4, 'Admin', 'مدير عام', DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE id = 5)
BEGIN
    INSERT INTO permissions (id, name, description, created_at)
    VALUES (5, 'SuperAdmin', 'مدير النظام', DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
END

-- إنشاء قسم افتراضي
IF NOT EXISTS (SELECT 1 FROM departments WHERE id = 1)
BEGIN
    INSERT INTO departments (id, name, description, is_active, created_at)
    VALUES (1, 'تقنية المعلومات', 'قسم تقنية المعلومات والتطوير', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
END

-- إنشاء مستخدم افتراضي (Admin)
-- كلمة المرور: admin123 (مشفرة باستخدام BCrypt)
IF NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO users (
        name, 
        email, 
        username, 
        password, 
        department_id, 
        role, 
        is_active, 
        is_online, 
        is_deleted, 
        created_at,
        first_name,
        last_name
    )
    VALUES (
        'مدير النظام',
        '<EMAIL>',
        'admin',
        '$2a$11$8K1p/a0dL2LkqvQOuiOX2uy7lQompaqtbyrd4sztgJSGHINfS4IA6', -- admin123
        1,
        5, -- SuperAdmin
        1,
        0,
        0,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        'مدير',
        'النظام'
    )
END

-- إنشاء مستخدم تجريبي (User)
-- كلمة المرور: user123 (مشفرة باستخدام BCrypt)
IF NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO users (
        name, 
        email, 
        username, 
        password, 
        department_id, 
        role, 
        is_active, 
        is_online, 
        is_deleted, 
        created_at,
        first_name,
        last_name
    )
    VALUES (
        'مستخدم تجريبي',
        '<EMAIL>',
        'testuser',
        '$2a$11$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- user123
        1,
        1, -- User
        1,
        0,
        0,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        'مستخدم',
        'تجريبي'
    )
END

-- إنشاء مستخدم مدير (Manager)
-- كلمة المرور: manager123 (مشفرة باستخدام BCrypt)
IF NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO users (
        name, 
        email, 
        username, 
        password, 
        department_id, 
        role, 
        is_active, 
        is_online, 
        is_deleted, 
        created_at,
        first_name,
        last_name
    )
    VALUES (
        'مدير تجريبي',
        '<EMAIL>',
        'manager',
        '$2a$11$N0qjqp7cvspuEcHu5uQz2OmwrJaAQBdulvs.LjsxIDi9.WdyFNKlO', -- manager123
        1,
        3, -- Manager
        1,
        0,
        0,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        'مدير',
        'تجريبي'
    )
END

-- تحديث مدير القسم
UPDATE departments 
SET manager_id = (SELECT id FROM users WHERE email = '<EMAIL>')
WHERE id = 1 AND manager_id IS NULL

PRINT 'تم إنشاء المستخدمين الافتراضيين بنجاح!'
PRINT 'المستخدمين المتاحين:'
PRINT '1. مدير النظام - <EMAIL> / admin123'
PRINT '2. مستخدم تجريبي - <EMAIL> / user123'
PRINT '3. مدير تجريبي - <EMAIL> / manager123'

GO
