import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// متحكم تخطيط لوحة المعلومات
class DashboardLayoutController extends GetxController {
  // المتغيرات التفاعلية
  final RxInt _selectedIndex = 0.obs;
  final RxBool _isDrawerOpen = false.obs;
  final RxBool _isCompactMode = false.obs;
  final RxString _currentPage = 'dashboard'.obs;
  final RxList<String> _navigationHistory = <String>[].obs;
  final RxMap<String, bool> _widgetVisibility = <String, bool>{}.obs;

  // Getters
  int get selectedIndex => _selectedIndex.value;
  bool get isDrawerOpen => _isDrawerOpen.value;
  bool get isCompactMode => _isCompactMode.value;
  String get currentPage => _currentPage.value;
  List<String> get navigationHistory => _navigationHistory;
  Map<String, bool> get widgetVisibility => _widgetVisibility;

  @override
  void onInit() {
    super.onInit();
    _initializeLayout();
  }

  /// تهيئة التخطيط
  void _initializeLayout() {
    // تعيين رؤية الويدجت الافتراضية
    _widgetVisibility.value = {
      'statistics': true,
      'recentActivities': true,
      'taskChart': true,
      'projectChart': true,
      'quickActions': true,
      'notifications': true,
    };

    // إضافة الصفحة الحالية لتاريخ التنقل
    _navigationHistory.add('dashboard');
  }

  /// تغيير الفهرس المحدد
  void changeSelectedIndex(int index) {
    _selectedIndex.value = index;
  }

  /// تبديل حالة الدرج
  void toggleDrawer() {
    _isDrawerOpen.value = !_isDrawerOpen.value;
  }

  /// فتح الدرج
  void openDrawer() {
    _isDrawerOpen.value = true;
  }

  /// إغلاق الدرج
  void closeDrawer() {
    _isDrawerOpen.value = false;
  }

  /// تبديل الوضع المضغوط
  void toggleCompactMode() {
    _isCompactMode.value = !_isCompactMode.value;
  }

  /// تعيين الوضع المضغوط
  void setCompactMode(bool isCompact) {
    _isCompactMode.value = isCompact;
  }

  /// تغيير الصفحة الحالية
  void changePage(String page) {
    if (_currentPage.value != page) {
      _navigationHistory.add(page);
      _currentPage.value = page;
      
      // الاحتفاظ بآخر 10 صفحات في التاريخ
      if (_navigationHistory.length > 10) {
        _navigationHistory.removeAt(0);
      }
    }
  }

  /// العودة للصفحة السابقة
  void goBack() {
    if (_navigationHistory.length > 1) {
      _navigationHistory.removeLast();
      _currentPage.value = _navigationHistory.last;
    }
  }

  /// تبديل رؤية ويدجت
  void toggleWidgetVisibility(String widgetKey) {
    _widgetVisibility[widgetKey] = !(_widgetVisibility[widgetKey] ?? true);
  }

  /// تعيين رؤية ويدجت
  void setWidgetVisibility(String widgetKey, bool isVisible) {
    _widgetVisibility[widgetKey] = isVisible;
  }

  /// التحقق من رؤية ويدجت
  bool isWidgetVisible(String widgetKey) {
    return _widgetVisibility[widgetKey] ?? true;
  }

  /// إعادة تعيين رؤية جميع الويدجت
  void resetWidgetVisibility() {
    _widgetVisibility.value = {
      'statistics': true,
      'recentActivities': true,
      'taskChart': true,
      'projectChart': true,
      'quickActions': true,
      'notifications': true,
    };
  }

  /// الحصول على عنوان الصفحة الحالية
  String get currentPageTitle {
    switch (_currentPage.value) {
      case 'dashboard':
        return 'لوحة المعلومات';
      case 'tasks':
        return 'المهام';
      case 'projects':
        return 'المشاريع';
      case 'users':
        return 'المستخدمين';
      case 'departments':
        return 'الأقسام';
      case 'reports':
        return 'التقارير';
      case 'settings':
        return 'الإعدادات';
      default:
        return 'لوحة المعلومات';
    }
  }

  /// الحصول على أيقونة الصفحة الحالية
  IconData get currentPageIcon {
    switch (_currentPage.value) {
      case 'dashboard':
        return Icons.dashboard;
      case 'tasks':
        return Icons.task;
      case 'projects':
        return Icons.folder;
      case 'users':
        return Icons.people;
      case 'departments':
        return Icons.business;
      case 'reports':
        return Icons.analytics;
      case 'settings':
        return Icons.settings;
      default:
        return Icons.dashboard;
    }
  }

  /// قائمة عناصر التنقل
  List<Map<String, dynamic>> get navigationItems {
    return [
      {
        'key': 'dashboard',
        'title': 'لوحة المعلومات',
        'icon': Icons.dashboard,
        'route': '/dashboard',
      },
      {
        'key': 'tasks',
        'title': 'المهام',
        'icon': Icons.task,
        'route': '/tasks',
      },
      {
        'key': 'projects',
        'title': 'المشاريع',
        'icon': Icons.folder,
        'route': '/projects',
      },
      {
        'key': 'users',
        'title': 'المستخدمين',
        'icon': Icons.people,
        'route': '/users',
      },
      {
        'key': 'departments',
        'title': 'الأقسام',
        'icon': Icons.business,
        'route': '/departments',
      },
      {
        'key': 'reports',
        'title': 'التقارير',
        'icon': Icons.analytics,
        'route': '/reports',
      },
      {
        'key': 'settings',
        'title': 'الإعدادات',
        'icon': Icons.settings,
        'route': '/settings',
      },
    ];
  }

  /// التحقق من تحديد عنصر التنقل
  bool isNavigationItemSelected(String key) {
    return _currentPage.value == key;
  }

  /// الحصول على لون عنصر التنقل
  Color getNavigationItemColor(String key, {Color? activeColor, Color? inactiveColor}) {
    return isNavigationItemSelected(key) 
        ? (activeColor ?? Colors.blue) 
        : (inactiveColor ?? Colors.grey);
  }

  /// تحديث تخطيط الشاشة حسب حجم الشاشة
  void updateLayoutForScreenSize(Size screenSize) {
    if (screenSize.width < 600) {
      // شاشة صغيرة - وضع مضغوط
      setCompactMode(true);
      closeDrawer();
    } else if (screenSize.width < 1200) {
      // شاشة متوسطة
      setCompactMode(false);
      closeDrawer();
    } else {
      // شاشة كبيرة
      setCompactMode(false);
      openDrawer();
    }
  }

  /// الحصول على عدد الأعمدة للشبكة
  int getGridColumns(Size screenSize) {
    if (screenSize.width < 600) {
      return 1;
    } else if (screenSize.width < 900) {
      return 2;
    } else if (screenSize.width < 1200) {
      return 3;
    } else {
      return 4;
    }
  }

  /// الحصول على نسبة العرض للارتفاع للبطاقات
  double getCardAspectRatio(Size screenSize) {
    if (screenSize.width < 600) {
      return 1.5;
    } else {
      return 1.2;
    }
  }

  /// تحديد ما إذا كان يجب إظهار التنقل السفلي
  bool shouldShowBottomNavigation(Size screenSize) {
    return screenSize.width < 600;
  }

  /// تحديد ما إذا كان يجب إظهار الدرج الجانبي
  bool shouldShowSideDrawer(Size screenSize) {
    return screenSize.width >= 600;
  }

  /// حفظ إعدادات التخطيط
  void saveLayoutSettings() {
    // TODO: حفظ الإعدادات في التخزين المحلي
    debugPrint('تم حفظ إعدادات التخطيط');
  }

  /// تحميل إعدادات التخطيط
  void loadLayoutSettings() {
    // TODO: تحميل الإعدادات من التخزين المحلي
    debugPrint('تم تحميل إعدادات التخطيط');
  }

  /// إعادة تعيين التخطيط
  void resetLayout() {
    _selectedIndex.value = 0;
    _isDrawerOpen.value = false;
    _isCompactMode.value = false;
    _currentPage.value = 'dashboard';
    _navigationHistory.clear();
    _navigationHistory.add('dashboard');
    resetWidgetVisibility();
  }
}
