import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../constants/app_styles.dart';
import '../../constants/app_colors.dart';
import '../../models/report_models.dart';
import '../../models/enhanced_report_model.dart';
import '../../controllers/report_controller.dart';
import '../widgets/common/loading_indicator.dart';
import '../widgets/common/empty_state_widget.dart';
import '../widgets/reporting/report_visualization_widget.dart';

/// شاشة عرض التقرير بتصميم Monday.com
class MondayStyleReportViewerScreen extends StatefulWidget {
  final String? reportId;

  const MondayStyleReportViewerScreen({
    super.key,
    this.reportId,
  });

  @override
  State<MondayStyleReportViewerScreen> createState() => _MondayStyleReportViewerScreenState();
}

class _MondayStyleReportViewerScreenState extends State<MondayStyleReportViewerScreen> 
    with SingleTickerProviderStateMixin {
  final ReportController _reportController = Get.find<ReportController>();
  late TabController _tabController;
  
  EnhancedReport? _report;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReport();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReport() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final reportId = widget.reportId ?? Get.arguments?['reportId'];
      if (reportId != null) {
        final report = await _reportController.getReportById(reportId);
        setState(() {
          _report = report;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'معرف التقرير غير محدد';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل التقرير: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_report?.title ?? 'عرض التقرير'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_report != null) ...[
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () => _shareReport(),
            ),
            IconButton(
              icon: const Icon(Icons.download),
              onPressed: () => _exportReport(),
            ),
            PopupMenuButton<String>(
              onSelected: (value) => _handleMenuAction(value),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('تعديل'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'duplicate',
                  child: ListTile(
                    leading: Icon(Icons.copy),
                    title: Text('نسخ'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('حذف', style: TextStyle(color: Colors.red)),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    if (_errorMessage != null) {
      return EmptyStateWidget(
        icon: Icons.error_outline,
        title: 'خطأ',
        message: _errorMessage!,
        actionText: 'إعادة المحاولة',
        onActionPressed: _loadReport,
      );
    }

    if (_report == null) {
      return const EmptyStateWidget(
        icon: Icons.description_outlined,
        title: 'التقرير غير موجود',
        message: 'لم يتم العثور على التقرير المطلوب',
      );
    }

    return Column(
      children: [
        // معلومات التقرير
        _buildReportHeader(),
        
        // التبويبات
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'البيانات'),
            Tab(text: 'التصورات المرئية'),
            Tab(text: 'الإعدادات'),
          ],
        ),
        
        // محتوى التبويبات
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildDataTab(),
              _buildVisualizationsTab(),
              _buildSettingsTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReportHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _report!.title,
                      style: AppStyles.headlineSmall,
                    ),
                    if (_report!.description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        _report!.description!,
                        style: AppStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                      ),
                    ],
                  ],
                ),
              ),
              _buildStatusChip(),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildInfoChip(
                icon: Icons.calendar_today,
                label: 'تم الإنشاء: ${DateFormat('yyyy/MM/dd').format(_report!.createdAt)}',
              ),
              const SizedBox(width: 8),
              _buildInfoChip(
                icon: Icons.category,
                label: 'النوع: ${_getReportTypeLabel(_report!.type)}',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.success.withAlpha(51), // 0.2 * 255 = 51
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        'نشط',
        style: AppStyles.bodySmall.copyWith(
          color: AppColors.success,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildInfoChip({required IconData icon, required String label}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            label,
            style: AppStyles.bodySmall.copyWith(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildDataTab() {
    return const Center(
      child: Text('عرض البيانات - قيد التطوير'),
    );
  }

  Widget _buildVisualizationsTab() {
    if (_report!.visualizations.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.bar_chart,
        title: 'لا توجد تصورات مرئية',
        message: 'لم يتم إنشاء أي تصورات مرئية لهذا التقرير بعد',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _report!.visualizations.length,
      itemBuilder: (context, index) {
        final visualization = _report!.visualizations[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: ReportVisualizationWidget(
              visualization: visualization,
              data: const [], // TODO: تحميل البيانات الفعلية
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingsTab() {
    return const Center(
      child: Text('إعدادات التقرير - قيد التطوير'),
    );
  }

  String _getReportTypeLabel(ReportType type) {
    switch (type) {
      case ReportType.taskSummary:
      case ReportType.taskStatus:
      case ReportType.taskProgress:
      case ReportType.taskDetails:
      case ReportType.taskCompletion:
        return 'تقرير المهام';
      case ReportType.userActivity:
      case ReportType.userPerformance:
        return 'تقرير المستخدمين';
      case ReportType.departmentPerformance:
      case ReportType.departmentWorkload:
        return 'تقرير الأقسام';
      case ReportType.timeTracking:
        return 'تقرير تتبع الوقت';
      case ReportType.projectProgress:
      case ReportType.projectStatus:
        return 'تقرير المشاريع';
      case ReportType.systemUsage:
        return 'تقرير استخدام النظام';
      case ReportType.custom:
        return 'تقرير مخصص';
    }
  }

  void _shareReport() {
    // TODO: تنفيذ مشاركة التقرير
    Get.snackbar('مشاركة', 'تم نسخ رابط التقرير');
  }

  void _exportReport() {
    // TODO: تنفيذ تصدير التقرير
    Get.snackbar('تصدير', 'جاري تصدير التقرير...');
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        // TODO: الانتقال إلى شاشة التعديل
        break;
      case 'duplicate':
        // TODO: نسخ التقرير
        break;
      case 'delete':
        // TODO: حذف التقرير
        break;
    }
  }
}
