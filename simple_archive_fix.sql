-- Simple Archive Tables Fix
-- إصلاح بسيط لجداول الأرشيف

USE [databasetasks]
GO

PRINT 'Starting Archive Tables Migration...'

-- 1. Add missing columns to archive_categories
PRINT 'Adding columns to archive_categories...'

-- Add color column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_categories]') AND name = 'color')
BEGIN
    ALTER TABLE [dbo].[archive_categories] ADD [color] NVARCHAR(20) NULL;
    PRINT 'Added color column to archive_categories'
END
ELSE
    PRINT 'color column already exists in archive_categories'

-- Add icon column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_categories]') AND name = 'icon')
BEGIN
    ALTER TABLE [dbo].[archive_categories] ADD [icon] NVARCHAR(50) NULL;
    PRINT 'Added icon column to archive_categories'
END
ELSE
    PRINT 'icon column already exists in archive_categories'

-- Add is_active column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_categories]') AND name = 'is_active')
BEGIN
    ALTER TABLE [dbo].[archive_categories] ADD [is_active] BIT NOT NULL DEFAULT 1;
    PRINT 'Added is_active column to archive_categories'
END
ELSE
    PRINT 'is_active column already exists in archive_categories'

-- 2. Add missing columns to archive_documents
PRINT 'Adding columns to archive_documents...'

-- Add content column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_documents]') AND name = 'content')
BEGIN
    ALTER TABLE [dbo].[archive_documents] ADD [content] NTEXT NULL;
    PRINT 'Added content column to archive_documents'
END
ELSE
    PRINT 'content column already exists in archive_documents'

-- Add created_by column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_documents]') AND name = 'created_by')
BEGIN
    ALTER TABLE [dbo].[archive_documents] ADD [created_by] INT NOT NULL DEFAULT 1;
    PRINT 'Added created_by column to archive_documents'
END
ELSE
    PRINT 'created_by column already exists in archive_documents'

-- Add created_at column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_documents]') AND name = 'created_at')
BEGIN
    ALTER TABLE [dbo].[archive_documents] ADD [created_at] BIGINT NOT NULL DEFAULT 0;
    PRINT 'Added created_at column to archive_documents'
END
ELSE
    PRINT 'created_at column already exists in archive_documents'

-- 3. Add missing columns to archive_tags
PRINT 'Adding columns to archive_tags...'

-- Add description column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_tags]') AND name = 'description')
BEGIN
    ALTER TABLE [dbo].[archive_tags] ADD [description] NTEXT NULL;
    PRINT 'Added description column to archive_tags'
END
ELSE
    PRINT 'description column already exists in archive_tags'

-- Add is_active column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[archive_tags]') AND name = 'is_active')
BEGIN
    ALTER TABLE [dbo].[archive_tags] ADD [is_active] BIT NOT NULL DEFAULT 1;
    PRINT 'Added is_active column to archive_tags'
END
ELSE
    PRINT 'is_active column already exists in archive_tags'

-- 4. Create archive_document_tags table if it doesn't exist
PRINT 'Creating archive_document_tags table...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[archive_document_tags]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[archive_document_tags] (
        [document_id] INT NOT NULL,
        [tag_id] INT NOT NULL,
        CONSTRAINT [PK_archive_document_tags] PRIMARY KEY ([document_id], [tag_id]),
        CONSTRAINT [FK_archive_document_tags_document] FOREIGN KEY ([document_id]) 
            REFERENCES [dbo].[archive_documents] ([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_archive_document_tags_tag] FOREIGN KEY ([tag_id]) 
            REFERENCES [dbo].[archive_tags] ([id]) ON DELETE CASCADE
    );
    PRINT 'Created archive_document_tags table'
END
ELSE
    PRINT 'archive_document_tags table already exists'

-- 5. Add Foreign Key for created_by in archive_documents
PRINT 'Adding foreign key constraints...'
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_archive_documents_created_by_users')
BEGIN
    ALTER TABLE [dbo].[archive_documents]
    ADD CONSTRAINT [FK_archive_documents_created_by_users] 
    FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id]);
    PRINT 'Added FK_archive_documents_created_by_users constraint'
END
ELSE
    PRINT 'FK_archive_documents_created_by_users constraint already exists'

PRINT 'Archive Tables Migration Completed Successfully!'
PRINT 'Your API should now work without column errors.'
