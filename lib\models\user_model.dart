/// نموذج المستخدم المتوافق مع ASP.NET Core API
class User {
  final int id;
  final String name;
  final String email;
  final String? username;
  final String? password; // لن يتم إرسالها في الاستجابات
  final String? profileImage;
  final int? departmentId;
  final UserRole role;
  final bool isActive;
  final bool isOnline;
  final int createdAt; // Unix timestamp (converted from backend long)
  final int? lastLogin; // Unix timestamp (converted from backend long)
  final int? lastSeen; // Unix timestamp (converted from backend long)
  final String? firstName;
  final String? lastName;
  final bool isDeleted;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.username,
    this.password,
    this.profileImage,
    this.departmentId,
    required this.role,
    this.isActive = true,
    this.isOnline = false,
    required this.createdAt,
    this.lastLogin,
    this.lastSeen,
    this.firstName,
    this.lastName,
    this.isDeleted = false,
  });

  /// إنشاء User من JSON (من API)
  factory User.fromJson(Map<String, dynamic> json) {
    UserRole role;
    if (json['role'] is String) {
      role = UserRole.fromString(json['role'] as String);
    } else if (json['role'] is int) {
      role = UserRole.fromInt(json['role'] as int);
    } else {
      role = UserRole.user;
    }

    int? departmentId;
    if (json['departmentId'] != null && json['departmentId'] != "") {
      if (json['departmentId'] is String) {
        departmentId = int.tryParse(json['departmentId'] as String);
      } else if (json['departmentId'] is int) {
        departmentId = json['departmentId'] as int;
      }
    }

    return User(
      id: json['id'] as int,
      name: json['name'] as String,
      email: json['email'] as String,
      username: json['username'] as String?,
      profileImage: json['profileImage'] as String?,
      departmentId: departmentId,
      role: role,
      isActive: json['isActive'] as bool? ?? true,
      isOnline: json['isOnline'] as bool? ?? false,
      // Convert from backend long to frontend int (Unix timestamp)
      createdAt: (json['createdAt'] as num).toInt(),
      lastLogin: json['lastLogin'] != null ? (json['lastLogin'] as num).toInt() : null,
      lastSeen: json['lastSeen'] != null ? (json['lastSeen'] as num).toInt() : null,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      isDeleted: json['isDeleted'] as bool? ?? false,
    );
  }

  /// تحويل User إلى JSON (للإرسال إلى API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'username': username,
      'profileImage': profileImage,
      'departmentId': departmentId,
      'role': role.value,
      'isActive': isActive,
      'isOnline': isOnline,
      'createdAt': createdAt,
      'lastLogin': lastLogin,
      'lastSeen': lastSeen,
      'firstName': firstName,
      'lastName': lastName,
      'isDeleted': isDeleted,
      if (password != null) 'password': password,
    };
  }

  /// إنشاء نسخة محدثة من User
  User copyWith({
    int? id,
    String? name,
    String? email,
    String? username,
    String? password,
    String? profileImage,
    int? departmentId,
    UserRole? role,
    bool? isActive,
    bool? isOnline,
    int? createdAt,
    int? lastLogin,
    int? lastSeen,
    String? firstName,
    String? lastName,
    bool? isDeleted,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      username: username ?? this.username,
      password: password ?? this.password,
      profileImage: profileImage ?? this.profileImage,
      departmentId: departmentId ?? this.departmentId,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      isOnline: isOnline ?? this.isOnline,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      lastSeen: lastSeen ?? this.lastSeen,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// تعداد أدوار المستخدمين متوافق مع API
enum UserRole {
  user(1, 'مستخدم'),
  supervisor(2, 'مشرف'),
  manager(3, 'مدير'),
  admin(4, 'مدير عام'),
  superAdmin(5, 'مدير النظام');

  const UserRole(this.value, this.displayName);

  final int value;
  final String displayName;

  /// إنشاء UserRole من قيمة int
  static UserRole fromInt(int value) {
    switch (value) {
      case 1:
        return UserRole.user;
      case 2:
        return UserRole.supervisor;
      case 3:
        return UserRole.manager;
      case 4:
        return UserRole.admin;
      case 5:
        return UserRole.superAdmin;
      default:
        return UserRole.user;
    }
  }

  /// إنشاء UserRole من قيمة String
  static UserRole fromString(String value) {
    switch (value.toLowerCase()) {
      case 'user':
        return UserRole.user;
      case 'supervisor':
        return UserRole.supervisor;
      case 'manager':
        return UserRole.manager;
      case 'admin':
        return UserRole.admin;
      case 'superadmin':
        return UserRole.superAdmin;
      default:
        return UserRole.user;
    }
  }

  /// التحقق من كون الدور إداري
  bool get isAdmin => this == UserRole.admin || this == UserRole.superAdmin;

  /// التحقق من كون الدور مدير نظام
  bool get isSuperAdmin => this == UserRole.superAdmin;

  /// التحقق من كون الدور مدير قسم أو أعلى
  bool get isManagerOrAbove => 
      this == UserRole.manager || 
      this == UserRole.admin || 
      this == UserRole.superAdmin;
}

/// معلومات المستخدم المختصرة (من AuthResponse)
class UserInfo {
  final int id;
  final String name;
  final String email;
  final String? username;
  final UserRole role;
  final String roleName;
  final int? departmentId;
  final String? departmentName;
  final String? profileImage;
  final bool isActive;
  final bool isOnline;

  const UserInfo({
    required this.id,
    required this.name,
    required this.email,
    this.username,
    required this.role,
    required this.roleName,
    this.departmentId,
    this.departmentName,
    this.profileImage,
    this.isActive = true,
    this.isOnline = false,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    try {
      UserRole role;
      if (json['role'] is String) {
        role = UserRole.fromString(json['role'] as String);
      } else if (json['role'] is int) {
        role = UserRole.fromInt(json['role'] as int);
      } else {
        role = UserRole.user;
      }

      int? departmentId;
      if (json['departmentId'] != null && json['departmentId'] != "") {
        if (json['departmentId'] is String) {
          departmentId = int.tryParse(json['departmentId'] as String);
        } else if (json['departmentId'] is int) {
          departmentId = json['departmentId'] as int;
        }
      }

      return UserInfo(
        id: json['id'] as int? ?? 0,
        name: json['name'] as String? ?? '',
        email: json['email'] as String? ?? '',
        username: json['username'] as String?,
        role: role,
        roleName: json['roleName'] as String? ?? role.displayName,
        departmentId: departmentId,
        departmentName: json['departmentName'] as String?,
        profileImage: json['profileImage'] as String?,
        isActive: json['isActive'] as bool? ?? true,
        isOnline: json['isOnline'] as bool? ?? false,
      );
    } catch (e) {
      throw Exception('خطأ في تحليل UserInfo: $e');
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'username': username,
      'role': role.value,
      'roleName': roleName,
      'departmentId': departmentId,
      'departmentName': departmentName,
      'profileImage': profileImage,
      'isActive': isActive,
      'isOnline': isOnline,
    };
  }

  /// تحويل إلى User كامل
  User toUser() {
    return User(
      id: id,
      name: name,
      email: email,
      username: username,
      role: role,
      profileImage: profileImage,
      departmentId: departmentId,
      isActive: isActive,
      isOnline: isOnline,
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
    );
  }
}
