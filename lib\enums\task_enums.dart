/// تعدادات المهام
///
/// تحتوي على تعدادات الأولوية والحالة للمهام
library;

/// أولوية المهمة
enum TaskPriority {
  low(1, 'منخفضة', 'Low'),
  medium(2, 'متوسطة', 'Medium'),
  high(3, 'عالية', 'High'),
  urgent(4, 'عاجلة', 'Urgent');

  const TaskPriority(this.level, this.displayNameAr, this.displayNameEn);

  final int level;
  final String displayNameAr;
  final String displayNameEn;

  /// الحصول على الأولوية من المستوى
  static TaskPriority fromLevel(int level) {
    return TaskPriority.values.firstWhere(
      (priority) => priority.level == level,
      orElse: () => TaskPriority.medium,
    );
  }

  /// الحصول على الأولوية من الاسم
  static TaskPriority fromName(String name) {
    final lowerName = name.toLowerCase();
    return TaskPriority.values.firstWhere(
      (priority) => 
        priority.displayNameEn.toLowerCase() == lowerName ||
        priority.displayNameAr == name ||
        priority.name == lowerName,
      orElse: () => TaskPriority.medium,
    );
  }
}

/// حالة المهمة
enum TaskStatus {
  pending(1, 'قيد الانتظار', 'Pending'),
  inProgress(2, 'قيد التنفيذ', 'In Progress'),
  waitingForInfo(3, 'في انتظار معلومات', 'Waiting for Info'),
  completed(4, 'مكتملة', 'Completed'),
  cancelled(5, 'ملغاة', 'Cancelled'),
  news(6, 'جديدة', 'News');

  const TaskStatus(this.id, this.displayNameAr, this.displayNameEn);

  final int id;
  final String displayNameAr;
  final String displayNameEn;

  /// الحصول على الحالة من المعرف
  static TaskStatus fromId(int id) {
    return TaskStatus.values.firstWhere(
      (status) => status.id == id,
      orElse: () => TaskStatus.pending,
    );
  }

  /// الحصول على الحالة من الاسم
  static TaskStatus fromName(String name) {
    final lowerName = name.toLowerCase();
    return TaskStatus.values.firstWhere(
      (status) => 
        status.displayNameEn.toLowerCase() == lowerName ||
        status.displayNameAr == name ||
        status.name == lowerName,
      orElse: () => TaskStatus.pending,
    );
  }
}
