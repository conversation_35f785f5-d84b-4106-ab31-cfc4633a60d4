# تصحيح DashboardWidgetAdapter

## نظرة عامة
تم إعادة كتابة ملف `DashboardWidgetAdapter` بالكامل ليتوافق مع ASP.NET Core API backend في مجلد webapi.

## المشاكل التي تم حلها

### 1. أخطاء في بنية الكلاس
**المشكلة:** كان الملف يحتوي على أخطاء في بنية الكلاس وتعريف الدوال
**الحل:** إعادة كتابة الكلاس بالكامل مع بنية صحيحة

### 2. عدم التوافق مع نماذج API
**المشكلة:** الكود لم يكن متوافقاً مع نماذج ASP.NET Core API
**الحل:** استخدام النماذج الصحيحة من `dashboard_models.dart`

### 3. أخطاء في تحويل البيانات
**المشكلة:** أخطاء في تحويل البيانات بين النماذج المختلفة
**الحل:** إنشاء دوال تحويل صحيحة ومتوافقة

## الميزات الجديدة

### 1. تحويل من API إلى UI
```dart
static uiModel.DashboardWidget convertToUIModel(
    apiModel.DashboardWidget apiWidget)
```
- تحويل نماذج API إلى نماذج واجهة المستخدم
- معالجة إعدادات JSON
- تحويل المواقع والأحجام
- تحديد حقول المحاور تلقائياً

### 2. تحويل من UI إلى API
```dart
static apiModel.CreateDashboardWidgetRequest convertToAPIRequest(
    uiModel.DashboardWidget uiWidget)
```
- تحويل نماذج واجهة المستخدم إلى طلبات API
- تحويل الإعدادات إلى JSON
- تحويل المواقع والأحجام للأرقام الصحيحة

### 3. تحويل القوائم
```dart
static List<uiModel.DashboardWidget> convertListToUIModel(
    List<apiModel.DashboardWidget> apiWidgets)
```
- تحويل قوائم كاملة من النماذج
- معالجة فعالة للبيانات المتعددة

### 4. توليد الاستعلامات
```dart
static String _generateQueryForWidgetType(String type)
```
- توليد استعلامات SQL تلقائياً حسب نوع العنصر
- دعم أنواع مختلفة من المخططات والجداول

### 5. تحديد حقول المحاور
```dart
static Map<String, String?> _getAxisFieldsForWidgetType(String type)
```
- تحديد حقول X و Y تلقائياً
- تحديد تسميات المحاور باللغة العربية
- دعم أنواع مختلفة من المخططات

## أنواع العناصر المدعومة

### 1. مخططات الحالة (taskStatusChart)
- نوع: Pie Chart
- الحقول: status, count
- الاستعلام: تجميع المهام حسب الحالة

### 2. مخططات التقدم (taskProgressChart)
- نوع: Line Chart
- المحاور: التاريخ، عدد المهام
- الاستعلام: تجميع المهام حسب تاريخ الإنشاء

### 3. أداء المستخدمين (userPerformanceChart)
- نوع: Bar Chart
- المحاور: اسم المستخدم، عدد المهام
- الاستعلام: تجميع المهام حسب المستخدم المكلف

### 4. أداء الأقسام (departmentPerformanceChart)
- نوع: Bar Chart
- المحاور: اسم القسم، عدد المهام
- الاستعلام: تجميع المهام حسب القسم

### 5. تتبع الوقت (timeTrackingChart)
- نوع: Line Chart
- المحاور: التاريخ، الساعات
- الاستعلام: تجميع ساعات العمل حسب التاريخ

### 6. قائمة المهام (taskList)
- نوع: Table
- الحقول: المعرف، العنوان، الحالة، الأولوية، تاريخ الاستحقاق
- الاستعلام: أحدث 10 مهام

### 7. مؤشرات الأداء (kpi)
- نوع: KPI
- الحقول: المجموع، المكتمل
- الاستعلام: إحصائيات المهام

## التوافق مع الباك إند

### 1. نماذج البيانات
- متوافق مع `DashboardWidget` في ASP.NET Core
- استخدام `CreateDashboardWidgetRequest` للطلبات الجديدة
- دعم جميع الحقول المطلوبة

### 2. أنواع البيانات
- تحويل صحيح للأرقام والنصوص
- معالجة التواريخ بشكل صحيح
- تحويل JSON للإعدادات

### 3. مسارات API
- متوافق مع endpoints الباك إند
- استخدام المسارات الصحيحة
- معالجة الاستجابات بشكل صحيح

## الإعدادات المدعومة

### 1. إعدادات العرض
- `showValues`: عرض القيم
- `showLabels`: عرض التسميات
- `showGrid`: عرض الشبكة
- `showLegend`: عرض وسيلة الإيضاح

### 2. إعدادات التصميم
- `headerColor`: لون الرأس
- `orientation`: اتجاه المخطط

### 3. إعدادات الموقع
- `positionX`: الموقع الأفقي
- `positionY`: الموقع العمودي
- `width`: العرض
- `height`: الارتفاع

## دوال المساعدة

### 1. تحويل الألوان
```dart
static Color _getHeaderColorFromSettings(Map<String, dynamic> settings)
```
- تحويل قيم الألوان من النص إلى Color
- معالجة الأخطاء في التحويل
- قيمة افتراضية آمنة

### 2. إنشاء عناصر جديدة
```dart
static uiModel.DashboardWidget createNewUIWidget({...})
```
- إنشاء عناصر جديدة بقيم افتراضية
- تحديد معرف فريد تلقائياً
- إعدادات افتراضية محسنة

## التحسينات

### 1. معالجة الأخطاء
- معالجة أخطاء تحليل JSON
- قيم افتراضية آمنة
- رسائل خطأ واضحة

### 2. الأداء
- تحويل فعال للقوائم
- تجنب العمليات المكلفة
- استخدام ذاكرة محسن

### 3. القابلية للصيانة
- كود منظم ومقروء
- تعليقات واضحة باللغة العربية
- فصل الاهتمامات

## الاستخدام

### 1. تحويل من API إلى UI
```dart
final uiWidget = DashboardWidgetAdapter.convertToUIModel(apiWidget);
```

### 2. تحويل من UI إلى API
```dart
final apiRequest = DashboardWidgetAdapter.convertToAPIRequest(uiWidget);
```

### 3. تحويل قوائم
```dart
final uiWidgets = DashboardWidgetAdapter.convertListToUIModel(apiWidgets);
```

### 4. إنشاء عنصر جديد
```dart
final newWidget = DashboardWidgetAdapter.createNewUIWidget(
  dashboardId: '1',
  title: 'مخطط جديد',
  type: uiModel.DashboardWidgetType.pieChart,
  position: Offset(100, 100),
  size: Size(300, 200),
);
```

## الحالة النهائية
✅ متوافق بالكامل مع ASP.NET Core API
✅ دعم جميع أنواع العناصر
✅ تحويل صحيح للبيانات
✅ معالجة أخطاء محسنة
✅ كود منظم وقابل للصيانة
✅ تعليقات واضحة باللغة العربية
