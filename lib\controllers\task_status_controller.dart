import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/task_type_models.dart';
import 'package:get/get.dart';
import '../services/api/task_api_service.dart';

/// متحكم حالات المهام
class TaskStatusController extends GetxController {
  final TaskApiService _apiService = TaskApiService();

  // قوائم حالات المهام
  final RxList<TaskStatus> _allStatuses = <TaskStatus>[].obs;
  final RxList<TaskStatus> _filteredStatuses = <TaskStatus>[].obs;

  // حالة المهمة الحالية
  final Rx<TaskStatus?> _currentStatus = Rx<TaskStatus?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxString _errorMessage = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<TaskStatus> get allStatuses => _allStatuses;
  List<TaskStatus> get taskStatuses => _filteredStatuses;
  List<TaskStatus> get filteredStatuses => _filteredStatuses;
  TaskStatus? get currentStatus => _currentStatus.value;
  RxBool get isLoading => _isLoading;
  RxString get errorMessage => _errorMessage;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllStatuses();
  }

  /// تحميل جميع حالات المهام
  Future<void> loadAllStatuses() async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final statuses = await _apiService.getTaskStatuses();
      _allStatuses.assignAll(statuses);
      _applyFilters();
      debugPrint('تم تحميل ${statuses.length} حالة مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل حالات المهام: $e';
      _errorMessage.value = 'خطأ في تحميل حالات المهام: $e';
      debugPrint('خطأ في تحميل حالات المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل حالات المهام مع خيار تضمين غير النشطة
  Future<void> loadTaskStatuses({bool includeInactive = false}) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final statuses = await _apiService.getTaskStatuses();
      _allStatuses.assignAll(statuses);

      // تطبيق مرشح النشط/غير النشط
      if (!includeInactive) {
        _filteredStatuses.assignAll(statuses.where((s) => s.isActive).toList());
      } else {
        _filteredStatuses.assignAll(statuses);
      }

      debugPrint('تم تحميل ${statuses.length} حالة مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل حالات المهام: $e';
      _errorMessage.value = 'خطأ في تحميل حالات المهام: $e';
      debugPrint('خطأ في تحميل حالات المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على حالة مهمة بالمعرف
  Future<void> getStatusById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // البحث عن الحالة في القائمة المحملة
      final status = _allStatuses.firstWhereOrNull((s) => s.id == id);
      if (status != null) {
        _currentStatus.value = status;
        debugPrint('تم تحميل حالة المهمة: ${status.name}');
      } else {
        // إذا لم توجد، تحميل جميع الحالات أولاً
        await loadAllStatuses();
        final foundStatus = _allStatuses.firstWhereOrNull((s) => s.id == id);
        _currentStatus.value = foundStatus;
        debugPrint('تم تحميل حالة المهمة: ${foundStatus?.name ?? 'غير موجودة'}');
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل حالة المهمة: $e';
      debugPrint('خطأ في تحميل حالة المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء حالة مهمة جديدة
  Future<bool> createStatus(TaskStatus status) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newStatus = await _apiService.createTaskStatus(status);
      if (newStatus != null) {
        _allStatuses.add(newStatus);
        _applyFilters();
        debugPrint('تم إنشاء حالة مهمة جديدة: ${newStatus.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء حالة المهمة: $e';
      debugPrint('خطأ في إنشاء حالة المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث حالة مهمة
  Future<bool> updateStatus(int id, TaskStatus status) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedStatus = await _apiService.updateTaskStatusModel(status);
      if (updatedStatus != null) {
        final index = _allStatuses.indexWhere((s) => s.id == id);
        if (index != -1) {
          _allStatuses[index] = updatedStatus;
          _applyFilters();
        }
        debugPrint('تم تحديث حالة المهمة: ${updatedStatus.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث حالة المهمة: $e';
      debugPrint('خطأ في تحديث حالة المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء حالة مهمة جديدة بمعاملات مسماة
  Future<bool> createTaskStatus({
    required String name,
    required String description,
    String? color,
    String? icon,
    bool isDefault = false,
    int orderIndex = 0,
  }) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      // إنشاء كائن TaskStatus جديد
      final newStatus = TaskStatus(
        id: 0, // سيتم تعيينه من قبل الخادم
        name: name,
        description: description,
        color: color,
        icon: icon,
        orderIndex: orderIndex,
        isDefault: isDefault,
        isActive: true,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      final createdStatus = await _apiService.createTaskStatus(newStatus);

      if (createdStatus != null) {
        // إضافة الحالة الجديدة إلى القائمة
        _allStatuses.add(createdStatus);
        _applyFilters();
        debugPrint('تم إنشاء حالة المهمة بنجاح');
        return true;
      } else {
        _errorMessage.value = 'فشل في إنشاء حالة المهمة';
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في إنشاء حالة المهمة: $e';
      _errorMessage.value = 'خطأ في إنشاء حالة المهمة: $e';
      debugPrint('خطأ في إنشاء حالة المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث حالة مهمة
  Future<bool> updateTaskStatus(TaskStatus taskStatus) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final updatedStatus = await _apiService.updateTaskStatusModel(taskStatus);

      if (updatedStatus != null) {
        // تحديث القائمة المحلية
        final index = _allStatuses.indexWhere((s) => s.id == taskStatus.id);
        if (index != -1) {
          _allStatuses[index] = updatedStatus;
          _applyFilters();
        }
        debugPrint('تم تحديث حالة المهمة بنجاح');
        return true;
      } else {
        _errorMessage.value = 'فشل في تحديث حالة المهمة';
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في تحديث حالة المهمة: $e';
      _errorMessage.value = 'خطأ في تحديث حالة المهمة: $e';
      debugPrint('خطأ في تحديث حالة المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف حالة مهمة
  Future<bool> deleteTaskStatus(int id) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final success = await _apiService.deleteTaskStatus(id);
      if (success) {
        _allStatuses.removeWhere((s) => s.id == id);
        _applyFilters();
        debugPrint('تم حذف حالة المهمة');
        return true;
      } else {
        _errorMessage.value = 'فشل في حذف حالة المهمة';
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في حذف حالة المهمة: $e';
      _errorMessage.value = 'خطأ في حذف حالة المهمة: $e';
      debugPrint('خطأ في حذف حالة المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إعادة ترتيب حالات المهام
  Future<bool> reorderTaskStatuses(List<int> orderedIds) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      // تحديث ترتيب العرض محلياً
      for (int i = 0; i < orderedIds.length; i++) {
        final statusIndex = _allStatuses.indexWhere((s) => s.id == orderedIds[i]);
        if (statusIndex != -1) {
          final updatedStatus = _allStatuses[statusIndex].copyWith(orderIndex: i);
          _allStatuses[statusIndex] = updatedStatus;
        }
      }

      _applyFilters();
      debugPrint('تم إعادة ترتيب حالات المهام');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إعادة ترتيب حالات المهام: $e';
      _errorMessage.value = 'خطأ في إعادة ترتيب حالات المهام: $e';
      debugPrint('خطأ في إعادة ترتيب حالات المهام: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف حالة مهمة (الطريقة القديمة للتوافق)
  Future<bool> deleteStatus(int id) async {
    return await deleteTaskStatus(id);
  }

  /// الحصول على الحالات النشطة فقط
  List<TaskStatus> get activeStatuses {
    return _allStatuses.where((status) => status.isActive).toList();
  }

  /// الحصول على حالة افتراضية
  TaskStatus? get defaultStatus {
    return _allStatuses.firstWhereOrNull((status) => status.isDefault);
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allStatuses.where((status) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!status.name.toLowerCase().contains(query) &&
            !(status.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !status.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredStatuses.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllStatuses();
  }
}
