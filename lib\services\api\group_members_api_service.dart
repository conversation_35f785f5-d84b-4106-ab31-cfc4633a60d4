import 'package:flutter/foundation.dart';
import '../../models/group_member_models.dart';
import 'api_service.dart';

/// خدمة API لأعضاء المجموعات - متطابقة مع ASP.NET Core API
class GroupMembersApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع أعضاء المجموعات
  Future<List<GroupMember>> getAllMembers() async {
    try {
      final response = await _apiService.get('/GroupMembers');
      return _apiService.handleListResponse<GroupMember>(
        response,
        (json) => GroupMember.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل أعضاء المجموعات: $e');
      rethrow;
    }
  }

  /// الحصول على عضو مجموعة بواسطة المعرف
  Future<GroupMember?> getMemberById(int id) async {
    try {
      final response = await _apiService.get('/GroupMembers/$id');
      return _apiService.handleResponse<GroupMember>(
        response,
        (json) => GroupMember.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل عضو المجموعة $id: $e');
      return null;
    }
  }

  /// الحصول على أعضاء مجموعة محددة
  Future<List<GroupMember>> getMembersByGroup(int groupId) async {
    try {
      final response = await _apiService.get('/GroupMembers/group/$groupId');
      return _apiService.handleListResponse<GroupMember>(
        response,
        (json) => GroupMember.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل أعضاء المجموعة $groupId: $e');
      return [];
    }
  }

  /// الحصول على مجموعات المستخدم
  Future<List<GroupMember>> getUserGroups(int userId) async {
    try {
      final response = await _apiService.get('/GroupMembers/user/$userId');
      return _apiService.handleListResponse<GroupMember>(
        response,
        (json) => GroupMember.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل مجموعات المستخدم $userId: $e');
      return [];
    }
  }

  /// إضافة عضو إلى مجموعة
  Future<GroupMember> addMemberToGroup(int groupId, int userId, String role) async {
    try {
      final roleValue = _getRoleValue(role);
      final request = AddGroupMemberRequest(
        groupId: groupId,
        userId: userId,
        role: roleValue,
      );

      final response = await _apiService.post(
        '/GroupMembers',
        request.toJson(),
      );
      
      return _apiService.handleResponse<GroupMember>(
        response,
        (json) => GroupMember.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إضافة عضو للمجموعة: $e');
      rethrow;
    }
  }

  /// تحديث دور عضو في المجموعة
  Future<void> updateMemberRole(int memberId, String newRole) async {
    try {
      final roleValue = _getRoleValue(newRole);
      final request = UpdateGroupMemberRoleRequest(
        memberId: memberId,
        newRole: roleValue,
      );

      await _apiService.put(
        '/GroupMembers/$memberId/role',
        request.toJson(),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث دور العضو $memberId: $e');
      rethrow;
    }
  }

  /// إزالة عضو من المجموعة
  Future<void> removeMemberFromGroup(int memberId) async {
    try {
      await _apiService.delete('/GroupMembers/$memberId');
    } catch (e) {
      debugPrint('خطأ في إزالة العضو $memberId: $e');
      rethrow;
    }
  }

  /// ترقية عضو إلى مشرف
  Future<void> promoteToAdmin(int memberId) async {
    try {
      await _apiService.put(
        '/GroupMembers/$memberId/promote',
        {'role': GroupMemberRole.admin.value},
      );
    } catch (e) {
      debugPrint('خطأ في ترقية العضو $memberId: $e');
      rethrow;
    }
  }

  /// إزالة صلاحيات الإشراف من عضو
  Future<void> demoteFromAdmin(int memberId) async {
    try {
      await _apiService.put(
        '/GroupMembers/$memberId/demote',
        {'role': GroupMemberRole.member.value},
      );
    } catch (e) {
      debugPrint('خطأ في إزالة صلاحيات الإشراف من العضو $memberId: $e');
      rethrow;
    }
  }

  /// كتم عضو في المجموعة
  Future<void> muteMember(int memberId, int durationMinutes) async {
    try {
      await _apiService.put(
        '/GroupMembers/$memberId/mute',
        {'durationMinutes': durationMinutes},
      );
    } catch (e) {
      debugPrint('خطأ في كتم العضو $memberId: $e');
      rethrow;
    }
  }

  /// إلغاء كتم عضو في المجموعة
  Future<void> unmuteMember(int memberId) async {
    try {
      await _apiService.put('$memberId', {});
    } catch (e) {
      debugPrint('خطأ في إلغاء كتم العضو $memberId: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات أعضاء المجموعة
  Future<GroupMemberStatistics> getGroupStatistics(int groupId) async {
    try {
      final response = await _apiService.get('/GroupMembers/group/$groupId/statistics');
      return _apiService.handleResponse<GroupMemberStatistics>(
        response,
        (json) => GroupMemberStatistics.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات المجموعة $groupId: $e');
      rethrow;
    }
  }

  /// البحث في أعضاء المجموعة
  Future<List<GroupMember>> searchMembers(String query, {int? groupId}) async {
    try {
      final queryParams = <String, dynamic>{'search': query};
      if (groupId != null) {
        queryParams['groupId'] = groupId;
      }

      final response = await _apiService.get(
        '/GroupMembers/search',
        queryParams: queryParams,
      );
      
      return _apiService.handleListResponse<GroupMember>(
        response,
        (json) => GroupMember.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن أعضاء المجموعة: $e');
      return [];
    }
  }

  /// الحصول على الأعضاء النشطين فقط
  Future<List<GroupMember>> getActiveMembers({int? groupId}) async {
    try {
      final queryParams = <String, dynamic>{'active': true};
      if (groupId != null) {
        queryParams['groupId'] = groupId;
      }

      final response = await _apiService.get(
        '/GroupMembers/active',
        queryParams: queryParams,
      );
      
      return _apiService.handleListResponse<GroupMember>(
        response,
        (json) => GroupMember.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل الأعضاء النشطين: $e');
      return [];
    }
  }

  /// الحصول على أعضاء بدور محدد
  Future<List<GroupMember>> getMembersByRole(String role, {int? groupId}) async {
    try {
      final roleValue = _getRoleValue(role);
      final queryParams = <String, dynamic>{'role': roleValue};
      if (groupId != null) {
        queryParams['groupId'] = groupId;
      }

      final response = await _apiService.get(
        '/GroupMembers/by-role',
        queryParams: queryParams,
      );
      
      return _apiService.handleListResponse<GroupMember>(
        response,
        (json) => GroupMember.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل أعضاء بدور $role: $e');
      return [];
    }
  }

  /// تحويل اسم الدور إلى قيمة رقمية
  int _getRoleValue(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return GroupMemberRole.admin.value;
      case 'moderator':
        return GroupMemberRole.moderator.value;
      case 'member':
      default:
        return GroupMemberRole.member.value;
    }
  }
}
