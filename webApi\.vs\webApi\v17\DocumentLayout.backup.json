{"Version": 1, "WorkspaceRootPath": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\webapi.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\webapi.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\sql_scripts\\create_activity_logs_table.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\sql_scripts\\create_activity_logs_table.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\services\\databaseseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\services\\databaseseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\controllers\\powerbicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\powerbicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\controllers\\userpermissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\userpermissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|d:\\flutterproject\\flutter_application_2_converttosql - copy\\webapi\\webapi\\models\\tasksdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\models\\tasksdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 398, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "webApi.http", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\webApi.http", "RelativeDocumentMoniker": "webApi\\webApi.http", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\webApi.http", "RelativeToolTip": "webApi\\webApi.http", "ViewState": "AgIAAAcAAAAAAAAAAAAqwBUAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003502|", "WhenOpened": "2025-05-31T15:38:03.416Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "create_activity_logs_table.sql", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\SQL_Scripts\\create_activity_logs_table.sql", "RelativeDocumentMoniker": "webApi\\SQL_Scripts\\create_activity_logs_table.sql", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\SQL_Scripts\\create_activity_logs_table.sql", "RelativeToolTip": "webApi\\SQL_Scripts\\create_activity_logs_table.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-06-02T02:16:59.618Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "appsettings.json", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\appsettings.json", "RelativeDocumentMoniker": "webApi\\appsettings.json", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\appsettings.json", "RelativeToolTip": "webApi\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-31T17:26:52.761Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\appsettings.Development.json", "RelativeDocumentMoniker": "webApi\\appsettings.Development.json", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\appsettings.Development.json", "RelativeToolTip": "webApi\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-31T17:26:51.885Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "UsersController.cs", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Controllers\\UsersController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\UsersController.cs", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Controllers\\UsersController.cs", "RelativeToolTip": "webApi\\Controllers\\UsersController.cs", "ViewState": "AgIAABUBAAAAAAAAAAASwCsBAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:28:58.507Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DatabaseSeeder.cs", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Services\\DatabaseSeeder.cs", "RelativeDocumentMoniker": "webApi\\Services\\DatabaseSeeder.cs", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Services\\DatabaseSeeder.cs", "RelativeToolTip": "webApi\\Services\\DatabaseSeeder.cs", "ViewState": "AgIAAGkAAAAAAAAAAAASwHwAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T02:10:44.316Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Program.cs", "RelativeDocumentMoniker": "webApi\\Program.cs", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Program.cs", "RelativeToolTip": "webApi\\Program.cs", "ViewState": "AgIAANcAAAAAAAAAAAAlwOcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:27:38.295Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "TasksDbContext.cs", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Models\\TasksDbContext.cs", "RelativeDocumentMoniker": "webApi\\Models\\TasksDbContext.cs", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Models\\TasksDbContext.cs", "RelativeToolTip": "webApi\\Models\\TasksDbContext.cs", "ViewState": "AgIAADkAAAAAAAAAAAAAAFYAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:24:11.811Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "PowerBIController.cs", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Controllers\\PowerBIController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\PowerBIController.cs", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Controllers\\PowerBIController.cs", "RelativeToolTip": "webApi\\Controllers\\PowerBIController.cs", "ViewState": "AgIAAA0AAAAAAAAAAAASwB4AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T01:45:02.947Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "UserPermissionsController.cs", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Controllers\\UserPermissionsController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\UserPermissionsController.cs", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Controllers\\UserPermissionsController.cs", "RelativeToolTip": "webApi\\Controllers\\UserPermissionsController.cs", "ViewState": "AgIAAEwAAAAAAAAAAAAUwGsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:30:23.196Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "User.cs", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Models\\User.cs", "RelativeDocumentMoniker": "webApi\\Models\\User.cs", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Models\\User.cs", "RelativeToolTip": "webApi\\Models\\User.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAHkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:26:44.688Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "launchSettings.json", "DocumentMoniker": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "webApi\\Properties\\launchSettings.json", "ToolTip": "D:\\flutterproject\\flutter_application_2_convertToSQL - Copy\\webApi\\webApi\\Properties\\launchSettings.json", "RelativeToolTip": "webApi\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-31T15:38:10.607Z"}]}]}]}