import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/chat_models.dart';
import '../services/api/chat_groups_api_service.dart';
import '../services/api/messages_api_service.dart';

/// متحكم الدردشة الموحد
/// يجمع بين إدارة المجموعات والرسائل في واجهة واحدة
class UnifiedChatController extends GetxController {
  final ChatGroupsApiService _chatGroupsApiService = ChatGroupsApiService();
  final MessagesApiService _messagesApiService = MessagesApiService();

  // المجموعة الحالية
  final Rx<ChatGroup?> _currentChatGroup = Rx<ChatGroup?>(null);
  
  // قائمة الرسائل
  final RxList<Message> _messages = <Message>[].obs;
  
  // قائمة المجموعات
  final RxList<ChatGroup> _chatGroups = <ChatGroup>[].obs;
  
  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingMessages = false.obs;
  final RxString _error = ''.obs;
  
  // البحث والمرشحات
  final RxString _searchQuery = ''.obs;
  final RxList<Message> _filteredMessages = <Message>[].obs;

  // Getters
  ChatGroup? get currentChatGroup => _currentChatGroup.value;
  List<Message> get messages => _messages;
  List<Message> get filteredMessages => _filteredMessages;
  List<ChatGroup> get chatGroups => _chatGroups;
  bool get isLoading => _isLoading.value;
  bool get isLoadingMessages => _isLoadingMessages.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;

  @override
  void onInit() {
    super.onInit();
    loadChatGroups();
  }

  /// تحميل قائمة مجموعات الدردشة
  Future<void> loadChatGroups() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final groups = await _chatGroupsApiService.getAllGroups();
      _chatGroups.assignAll(groups);
      debugPrint('تم تحميل ${groups.length} مجموعة دردشة');
    } catch (e) {
      _error.value = 'خطأ في تحميل مجموعات الدردشة: $e';
      debugPrint('خطأ في تحميل مجموعات الدردشة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تعيين المجموعة الحالية وتحميل رسائلها
  Future<void> setCurrentChatGroup(ChatGroup chatGroup) async {
    _currentChatGroup.value = chatGroup;
    await loadMessages(chatGroup.id);
  }

  /// تحميل رسائل المجموعة
  Future<void> loadMessages(int groupId) async {
    _isLoadingMessages.value = true;
    _error.value = '';

    try {
      final messages = await _messagesApiService.getGroupMessages(groupId);
      _messages.assignAll(messages);
      _applyMessageFilters();
      debugPrint('تم تحميل ${messages.length} رسالة للمجموعة $groupId');
    } catch (e) {
      _error.value = 'خطأ في تحميل الرسائل: $e';
      debugPrint('خطأ في تحميل الرسائل: $e');
    } finally {
      _isLoadingMessages.value = false;
    }
  }

  /// إرسال رسالة جديدة
  Future<bool> sendMessage({
    required String content,
    int? replyToMessageId,
    List<String> mentionedUserIds = const [],
  }) async {
    if (_currentChatGroup.value == null) {
      _error.value = 'لا توجد مجموعة محددة';
      return false;
    }

    try {
      final message = Message(
        id: 0, // سيتم تعيينه من الخادم
        content: content,
        senderId: 1, // TODO: الحصول على معرف المستخدم الحالي
        groupId: _currentChatGroup.value!.id,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        replyToMessageId: replyToMessageId,
        mentionedUserIds: mentionedUserIds,
      );

      final newMessage = await _messagesApiService.sendMessage(message);
      _messages.add(newMessage);
      _applyMessageFilters();
      debugPrint('تم إرسال الرسالة بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إرسال الرسالة: $e';
      debugPrint('خطأ في إرسال الرسالة: $e');
      return false;
    }
  }

  /// حذف رسالة
  Future<bool> deleteMessage(int messageId) async {
    try {
      await _messagesApiService.deleteMessage(messageId);
      _messages.removeWhere((message) => message.id == messageId);
      _applyMessageFilters();
      debugPrint('تم حذف الرسالة بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف الرسالة: $e';
      debugPrint('خطأ في حذف الرسالة: $e');
      return false;
    }
  }

  /// تحديث رسالة
  Future<bool> updateMessage(int messageId, String newContent) async {
    try {
      final messageIndex = _messages.indexWhere((m) => m.id == messageId);
      if (messageIndex == -1) return false;

      final updatedMessage = _messages[messageIndex].copyWith(
        content: newContent,
        updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      final result = await _messagesApiService.updateMessage(messageId, updatedMessage);
      _messages[messageIndex] = result;
      _applyMessageFilters();
      debugPrint('تم تحديث الرسالة بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث الرسالة: $e';
      debugPrint('خطأ في تحديث الرسالة: $e');
      return false;
    }
  }

  /// البحث في الرسائل
  void searchMessages(String query) {
    _searchQuery.value = query;
    _applyMessageFilters();
  }

  /// تطبيق مرشحات الرسائل
  void _applyMessageFilters() {
    if (_searchQuery.value.isEmpty) {
      _filteredMessages.assignAll(_messages);
    } else {
      final query = _searchQuery.value.toLowerCase();
      final filtered = _messages.where((message) {
        return message.content.toLowerCase().contains(query);
      }).toList();
      _filteredMessages.assignAll(filtered);
    }
  }

  /// مسح البحث
  void clearSearch() {
    _searchQuery.value = '';
    _applyMessageFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadChatGroups();
    if (_currentChatGroup.value != null) {
      await loadMessages(_currentChatGroup.value!.id);
    }
  }

  /// إنشاء مجموعة دردشة جديدة
  Future<bool> createChatGroup({
    required String name,
    String? description,
    bool isPrivate = false,
  }) async {
    try {
      final chatGroup = ChatGroup(
        id: 0, // سيتم تعيينه من الخادم
        name: name,
        description: description,
        isPrivate: isPrivate,
        createdBy: 1, // TODO: الحصول على معرف المستخدم الحالي
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      final newGroup = await _chatGroupsApiService.createGroup(chatGroup);
      if (newGroup != null) {
        _chatGroups.add(newGroup);
        debugPrint('تم إنشاء مجموعة دردشة جديدة: ${newGroup.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء مجموعة الدردشة: $e';
      debugPrint('خطأ في إنشاء مجموعة الدردشة: $e');
      return false;
    }
  }

  /// الانضمام إلى مجموعة
  Future<bool> joinGroup(int groupId) async {
    try {
      await _chatGroupsApiService.joinGroup(groupId, 1); // TODO: استخدام معرف المستخدم الحقيقي
      await loadChatGroups(); // إعادة تحميل المجموعات
      debugPrint('تم الانضمام إلى المجموعة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في الانضمام إلى المجموعة: $e';
      debugPrint('خطأ في الانضمام إلى المجموعة: $e');
      return false;
    }
  }

  /// مغادرة مجموعة
  Future<bool> leaveGroup(int groupId) async {
    try {
      await _chatGroupsApiService.leaveGroup(groupId, 1); // TODO: استخدام معرف المستخدم الحقيقي
      await loadChatGroups(); // إعادة تحميل المجموعات
      
      // إذا كانت المجموعة الحالية هي التي تم مغادرتها، قم بإلغاء تحديدها
      if (_currentChatGroup.value?.id == groupId) {
        _currentChatGroup.value = null;
        _messages.clear();
        _filteredMessages.clear();
      }
      
      debugPrint('تم مغادرة المجموعة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في مغادرة المجموعة: $e';
      debugPrint('خطأ في مغادرة المجموعة: $e');
      return false;
    }
  }
}
