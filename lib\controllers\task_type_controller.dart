import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/task_type_models.dart';
import 'package:get/get.dart';
import '../services/api/task_types_api_service.dart';

/// متحكم أنواع المهام
class TaskTypeController extends GetxController {
  final TaskTypesApiService _apiService = TaskTypesApiService();

  // قوائم أنواع المهام
  final RxList<TaskType> _allTypes = <TaskType>[].obs;
  final RxList<TaskType> _filteredTypes = <TaskType>[].obs;

  // نوع المهمة الحالي
  final Rx<TaskType?> _currentType = Rx<TaskType?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxString _errorMessage = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<TaskType> get allTypes => _allTypes;
  List<TaskType> get taskTypes => _filteredTypes;
  List<TaskType> get filteredTypes => _filteredTypes;
  TaskType? get currentType => _currentType.value;
  RxBool get isLoading => _isLoading;
  RxString get errorMessage => _errorMessage;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllTypes();
  }

  /// تحميل جميع أنواع المهام
  Future<void> loadAllTypes() async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final types = await _apiService.getAllTypes();
      _allTypes.assignAll(types);
      _applyFilters();
      debugPrint('تم تحميل ${types.length} نوع مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل أنواع المهام: $e';
      _errorMessage.value = 'خطأ في تحميل أنواع المهام: $e';
      debugPrint('خطأ في تحميل أنواع المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل أنواع المهام مع خيار تضمين غير النشطة
  Future<void> loadTaskTypes({bool includeInactive = false}) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final types = await _apiService.getAllTypes();
      _allTypes.assignAll(types);
      
      // تطبيق مرشح النشط/غير النشط
      if (!includeInactive) {
        _filteredTypes.assignAll(types.where((t) => t.isActive).toList());
      } else {
        _filteredTypes.assignAll(types);
      }
      
      debugPrint('تم تحميل ${types.length} نوع مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل أنواع المهام: $e';
      _errorMessage.value = 'خطأ في تحميل أنواع المهام: $e';
      debugPrint('خطأ في تحميل أنواع المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على نوع مهمة بالمعرف
  Future<void> getTypeById(int id) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      // البحث عن النوع في القائمة المحملة
      final type = _allTypes.firstWhereOrNull((t) => t.id == id);
      if (type != null) {
        _currentType.value = type;
        debugPrint('تم تحميل نوع المهمة: ${type.name}');
      } else {
        // إذا لم يوجد، تحميل جميع الأنواع أولاً
        await loadAllTypes();
        final foundType = _allTypes.firstWhereOrNull((t) => t.id == id);
        _currentType.value = foundType;
        debugPrint('تم تحميل نوع المهمة: ${foundType?.name ?? 'غير موجود'}');
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل نوع المهمة: $e';
      _errorMessage.value = 'خطأ في تحميل نوع المهمة: $e';
      debugPrint('خطأ في تحميل نوع المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء نوع مهمة جديد بمعاملات مسماة
  Future<bool> createTaskType({
    required String name,
    required String description,
    String? color,
    String? icon,
    bool isDefault = false,
  }) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      // إنشاء كائن TaskType جديد
      final newType = TaskType(
        id: 0, // سيتم تعيينه من قبل الخادم
        name: name,
        description: description,
        color: color,
        icon: icon,
        isDefault: isDefault,
        isActive: true,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      final createdType = await _apiService.createType(newType);
      
      // إضافة النوع الجديد إلى القائمة
      _allTypes.add(createdType);
      _applyFilters();
      debugPrint('تم إنشاء نوع المهمة بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء نوع المهمة: $e';
      _errorMessage.value = 'خطأ في إنشاء نوع المهمة: $e';
      debugPrint('خطأ في إنشاء نوع المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث نوع مهمة
  Future<bool> updateTaskType(TaskType taskType) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final updatedType = await _apiService.updateType(taskType.id, taskType);
      
      // تحديث القائمة المحلية
      final index = _allTypes.indexWhere((t) => t.id == taskType.id);
      if (index != -1) {
        _allTypes[index] = updatedType;
        _applyFilters();
      }
      debugPrint('تم تحديث نوع المهمة بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث نوع المهمة: $e';
      _errorMessage.value = 'خطأ في تحديث نوع المهمة: $e';
      debugPrint('خطأ في تحديث نوع المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف نوع مهمة
  Future<bool> deleteTaskType(int id) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final success = await _apiService.deleteType(id);
      if (success) {
        _allTypes.removeWhere((t) => t.id == id);
        _applyFilters();
        debugPrint('تم حذف نوع المهمة');
        return true;
      } else {
        _errorMessage.value = 'فشل في حذف نوع المهمة';
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في حذف نوع المهمة: $e';
      _errorMessage.value = 'خطأ في حذف نوع المهمة: $e';
      debugPrint('خطأ في حذف نوع المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على الأنواع النشطة فقط
  List<TaskType> get activeTypes {
    return _allTypes.where((type) => type.isActive).toList();
  }

  /// الحصول على نوع افتراضي
  TaskType? get defaultType {
    return _allTypes.firstWhereOrNull((type) => type.isDefault);
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allTypes.where((type) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!type.name.toLowerCase().contains(query) &&
            !(type.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !type.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredTypes.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
    _errorMessage.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllTypes();
  }
}
