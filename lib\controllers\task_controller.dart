import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_models.dart';
import '../models/task_comment_models.dart';
import '../models/task_history_models.dart';
import '../models/subtask_models.dart';
import '../models/task_progress_models.dart';
import '../services/api/task_api_service.dart';
import '../services/api/task_comments_api_service.dart';
import '../services/api/task_history_api_service.dart';
import '../services/api/subtasks_api_service.dart';
import '../services/api/task_progress_trackers_api_service.dart';
import '../utils/user_helper.dart';
import 'seed_data_controller.dart';
import 'auth_controller.dart';

/// متحكم المهام
class TaskController extends GetxController {
  final TaskApiService _apiService = TaskApiService();
  final TaskCommentsApiService _commentsApiService = TaskCommentsApiService();
  final TaskHistoryApiService _historyApiService = TaskHistoryApiService();
  final SubtasksApiService _subtasksApiService = SubtasksApiService();
  final TaskProgressTrackersApiService _progressTrackersApiService = TaskProgressTrackersApiService();

  // قوائم المهام
  final RxList<Task> _allTasks = <Task>[].obs;
  final RxList<Task> _filteredTasks = <Task>[].obs;
  final RxList<Task> _myTasks = <Task>[].obs;
  final RxList<Task> _assignedTasks = <Task>[].obs;
  final RxList<TaskComment> _taskComments = <TaskComment>[].obs;
  final RxList<TaskHistory> _taskHistory = <TaskHistory>[].obs;
  final RxList<Subtask> _subtasks = <Subtask>[].obs;
  final RxList<TaskComment> _comments = <TaskComment>[].obs;
  final RxList<Map<String, dynamic>> _taskMessages = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> _userContributions = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> _messageAttachments = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> _progressTrackers = <Map<String, dynamic>>[].obs;

  // Progress tracking variables - إضافة المتغيرات المطلوبة للتوافق مع TaskProgressTab
  final Rx<TaskProgressSummary?> _progressSummary = Rx<TaskProgressSummary?>(null);
  final Rx<TaskTimeTrackingSummary?> _timeTrackingSummary = Rx<TaskTimeTrackingSummary?>(null);
  final RxList<dynamic> _timeEntries = <dynamic>[].obs;
  final RxList<Task> _similarTasks = <Task>[].obs;
  final RxBool _isTrackingTime = false.obs;
  final RxBool _isLoadingTimeTracking = false.obs;

  // المهمة الحالية
  final Rx<Task?> _currentTask = Rx<Task?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxBool _isSendingMessage = false.obs;
  final RxString _error = ''.obs;
  final RxString _errorMessage = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _statusFilter = Rx<int?>(null);
  final Rx<int?> _priorityFilter = Rx<int?>(null);
  final Rx<int?> _assigneeFilter = Rx<int?>(null);
  final Rx<int?> _departmentFilter = Rx<int?>(null);
  final RxBool _showOverdueOnly = false.obs;

  // Getters
  List<Task> get allTasks => _allTasks;
  List<Task> get filteredTasks => _filteredTasks;
  List<Task> get myTasks => _myTasks;
  List<Task> get assignedTasks => _assignedTasks;
  List<Task> get tasks => _allTasks; // Alias for compatibility
  List<TaskComment> get taskComments => _taskComments;
  RxList<TaskHistory> get taskHistory => _taskHistory;
  RxList<Subtask> get subtasks => _subtasks;
  RxList<TaskComment> get comments => _comments;
  RxList<Map<String, dynamic>> get taskMessages => _taskMessages;
  RxList<Map<String, dynamic>> get userContributions => _userContributions;
  RxList<Map<String, dynamic>> get messageAttachments => _messageAttachments;
  RxList<Map<String, dynamic>> get progressTrackers => _progressTrackers;
  Task? get currentTask => _currentTask.value;
  bool get isLoading => _isLoading.value;
  bool get isSendingMessage => _isSendingMessage.value;
  String get error => _error.value;
  String get errorMessage => _errorMessage.value;

  // Progress tracking getters - إضافة getters للمتغيرات الجديدة
  Rx<TaskProgressSummary?> get progressSummary => _progressSummary;
  Rx<TaskTimeTrackingSummary?> get timeTrackingSummary => _timeTrackingSummary;
  List<dynamic> get timeEntries => _timeEntries;
  List<Task> get similarTasks => _similarTasks;
  Rx<bool> get isTrackingTime => _isTrackingTime;
  Rx<bool> get isLoadingTimeTracking => _isLoadingTimeTracking;
  Rx<bool> get isLoadingSimilarTasks => _isLoading; // استخدام _isLoading الموجود

  @override
  void onInit() {
    super.onInit();
    loadAllTasks();
  }

  /// تحميل جميع المهام
  Future<void> loadAllTasks() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tasks = await _apiService.getAllTasks();
      _allTasks.assignAll(tasks);
      _applyFilters();
      debugPrint('تم تحميل ${tasks.length} مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل المهام: $e';
      debugPrint('خطأ في تحميل المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل مهمة بواسطة المعرف
  Future<void> loadTaskById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final task = await _apiService.getTaskById(id);
      if (task != null) {
        _currentTask.value = task;
        debugPrint('تم تحميل المهمة: ${task.title}');
      } else {
        _error.value = 'المهمة غير موجودة';
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل المهمة: $e';
      debugPrint('خطأ في تحميل المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل مهام المستخدم الحالي
  Future<void> loadMyTasks(int userId, {Map<String, dynamic>? queryParams}) async {
    try {
      final tasks = await _apiService.getTasksByAssignee(userId, queryParams: queryParams);
      _myTasks.assignAll(tasks);
      debugPrint('تم تحميل ${tasks.length} مهمة للمستخدم');
    } catch (e) {
      debugPrint('خطأ في تحميل مهام المستخدم: $e');
    }
  }

  /// تحميل المهام المعينة من قبل المستخدم
  Future<void> loadAssignedTasks(int userId) async {
    try {
      final tasks = await _apiService.getTasksByCreator(userId);
      _assignedTasks.assignAll(tasks);
      debugPrint('تم تحميل ${tasks.length} مهمة معينة');
    } catch (e) {
      debugPrint('خطأ في تحميل المهام المعينة: $e');
    }
  }

  /// تحميل المهام حسب الإدارة
  Future<void> loadTasksByDepartment(int departmentId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tasks = await _apiService.getTasksByDepartment(departmentId);
      _allTasks.assignAll(tasks);
      _applyFilters();
      debugPrint('تم تحميل ${tasks.length} مهمة للإدارة $departmentId');
    } catch (e) {
      _error.value = 'خطأ في تحميل مهام الإدارة: $e';
      debugPrint('خطأ في تحميل مهام الإدارة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل المهام حسب المكلف بها
  Future<void> loadTasksByAssignee(int assigneeId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tasks = await _apiService.getTasksByAssignee(assigneeId);
      _allTasks.assignAll(tasks);
      _applyFilters();
      debugPrint('تم تحميل ${tasks.length} مهمة للمكلف $assigneeId');
    } catch (e) {
      _error.value = 'خطأ في تحميل مهام المكلف: $e';
      debugPrint('خطأ في تحميل مهام المكلف: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل المهام حسب صلاحيات المستخدم
  Future<void> loadTasksByUserPermissions(int userId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // التحقق من صلاحيات المستخدم
      final authController = Get.find<AuthController>();

      if (authController.isAdmin || authController.isSuperAdmin) {
        // إذا كان المستخدم مدير، تحميل جميع المهام
        await loadAllTasks();
        debugPrint('تم تحميل جميع المهام للمدير');
      } else {
        // إذا كان مستخدم عادي، تحميل مهامه فقط
        await loadMyTasks(userId);
        // نسخ المهام الشخصية إلى قائمة جميع المهام للعرض
        _allTasks.assignAll(_myTasks);
        _applyFilters();
        debugPrint('تم تحميل مهام المستخدم ${userId}');
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل المهام حسب الصلاحيات: $e';
      debugPrint('خطأ في تحميل المهام حسب الصلاحيات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل تعليقات المهمة
  Future<void> loadTaskComments(int taskId) async {
    try {
      final comments = await _commentsApiService.getCommentsByTask(taskId);
      _taskComments.assignAll(comments);
      _comments.assignAll(comments);
      debugPrint('تم تحميل ${comments.length} تعليق للمهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في تحميل تعليقات المهمة: $e');
    }
  }

  /// تحميل تاريخ المهمة
  Future<void> loadTaskHistory(int taskId) async {
    try {
      final history = await _historyApiService.getHistoryByTask(taskId);
      _taskHistory.assignAll(history);
      debugPrint('تم تحميل ${history.length} سجل تاريخ للمهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في تحميل تاريخ المهمة: $e');
    }
  }

  /// تحميل المهام الفرعية
  Future<void> loadSubtasks(int taskId) async {
    try {
      final subtasks = await _subtasksApiService.getSubtasksByTask(taskId);
      _subtasks.assignAll(subtasks);
      debugPrint('تم تحميل ${subtasks.length} مهمة فرعية للمهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في تحميل المهام الفرعية: $e');
    }
  }

  /// تحميل رسائل المهمة
  Future<void> loadTaskMessages(String taskId) async {
    try {
      // TODO: تنفيذ تحميل الرسائل عندما تصبح متاحة في API
      debugPrint('تحميل رسائل المهمة غير متاح حالياً');
    } catch (e) {
      debugPrint('خطأ في تحميل رسائل المهمة: $e');
    }
  }

  /// تحميل تفاصيل المهمة الكاملة
  Future<void> loadTaskDetails(String taskId) async {
    final taskIdInt = int.tryParse(taskId);
    if (taskIdInt == null) {
      _error.value = 'معرف المهمة غير صحيح';
      return;
    }

    _isLoading.value = true;
    _error.value = '';

    try {
      // تحميل المهمة الأساسية
      await loadTaskById(taskIdInt);

      if (_currentTask.value != null) {
        // تحميل البيانات المرتبطة
        await Future.wait([
          loadTaskComments(taskIdInt),
          loadTaskHistory(taskIdInt),
          loadSubtasks(taskIdInt),
          loadTaskMessages(taskId),
        ]);
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل تفاصيل المهمة: $e';
      debugPrint('خطأ في تحميل تفاصيل المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// البحث في المهام
  Future<void> searchTasks(String query) async {
    _searchQuery.value = query;
    
    if (query.isEmpty) {
      _applyFilters();
      return;
    }

    try {
      final tasks = await _apiService.searchTasks(query);
      _filteredTasks.assignAll(tasks);
    } catch (e) {
      debugPrint('خطأ في البحث: $e');
      _applyFilters(); // العودة للمرشحات المحلية في حالة الخطأ
    }
  }

  /// إنشاء مهمة جديدة
  Future<bool> createTask({
    required String title,
    String? description,
    int? taskTypeId,
    int? assigneeId,
    int? departmentId,
    DateTime? startDate,
    DateTime? dueDate,
    int priority = 2, // Default: medium priority
    int? estimatedTime,
  }) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // التحقق من وجود البيانات الأساسية أولاً
      await _ensureBasicDataExists();

      // التحقق من صحة البيانات المطلوبة
      final currentUserId = UserHelper.getCurrentUserId();
      if (currentUserId == 0) {
        throw Exception('لا يمكن تحديد المستخدم الحالي');
      }

      // التأكد من وجود حالة وأولوية صحيحة
      final validStatus = await _getValidStatus();
      final validPriority = await _getValidPriority(priority);

      if (validStatus == 0 || validPriority == 0) {
        throw Exception('لا توجد حالات أو أولويات صحيحة للمهام');
      }

      // إنشاء بيانات المهمة للـ API
      final taskData = Task(
        id: 0, // سيتم تعيينه من الخادم
        title: title,
        description: description,
        taskTypeId: taskTypeId,
        creatorId: currentUserId,
        assigneeId: assigneeId,
        departmentId: departmentId,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        startDate: startDate != null ? startDate.millisecondsSinceEpoch ~/ 1000 : null,
        dueDate: dueDate != null ? dueDate.millisecondsSinceEpoch ~/ 1000 : null,
        status: validStatus,
        priority: validPriority,
        completionPercentage: 0, // نسبة إكمال افتراضية
        estimatedTime: estimatedTime,
        isDeleted: false, // قيمة افتراضية
      );

      debugPrint('إنشاء مهمة بالبيانات: ${taskData.toJson()}');

      final newTask = await _apiService.createTask(taskData);
      if (newTask != null) {
        _allTasks.add(newTask);
        _currentTask.value = newTask;
        _applyFilters();
        debugPrint('✅ تم إنشاء المهمة بنجاح: ${newTask.title}');
        debugPrint('📋 معرف المهمة الجديد: ${newTask.id}');
        return true;
      } else {
        _error.value = 'فشل في إنشاء المهمة - لم يتم إرجاع بيانات المهمة من الخادم';
        debugPrint('❌ فشل في إنشاء المهمة - استجابة فارغة من الخادم');
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ عام في إنشاء المهمة: $e';
      debugPrint('💥 خطأ عام في إنشاء المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }



  /// تحديث مهمة
  Future<bool> updateTask({
    required int id,
    String? title,
    String? description,
    int? taskTypeId,
    int? assigneeId,
    int? departmentId,
    DateTime? startDate,
    DateTime? dueDate,
    DateTime? completedAt,
    int? status,
    int? priority,
    int? completionPercentage,
    int? estimatedTime,
    int? actualTime,
  }) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // الحصول على المهمة الحالية أولاً
      final currentTask = _allTasks.firstWhereOrNull((task) => task.id == id);
      if (currentTask == null) {
        _error.value = 'المهمة غير موجودة';
        return false;
      }

      // إنشاء المهمة المحدثة
      final taskToUpdate = currentTask.copyWith(
        title: title,
        description: description,
        taskTypeId: taskTypeId,
        assigneeId: assigneeId,
        departmentId: departmentId,
        startDate: startDate != null ? startDate.millisecondsSinceEpoch ~/ 1000 : null,
        dueDate: dueDate != null ? dueDate.millisecondsSinceEpoch ~/ 1000 : null,
        completedAt: completedAt != null ? completedAt.millisecondsSinceEpoch ~/ 1000 : null,
        status: status,
        priority: priority,
        completionPercentage: completionPercentage,
        estimatedTime: estimatedTime,
        actualTime: actualTime,
      );

      final updatedTask = await _apiService.updateTask(taskToUpdate);
      if (updatedTask != null) {
        final index = _allTasks.indexWhere((task) => task.id == id);
        if (index != -1) {
          _allTasks[index] = updatedTask;
        }
        _currentTask.value = updatedTask;
        _applyFilters();
        debugPrint('تم تحديث المهمة: ${updatedTask.title}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث المهمة: $e';
      debugPrint('خطأ في تحديث المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مهمة
  Future<bool> deleteTask(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _apiService.deleteTask(id);
      if (success) {
        _allTasks.removeWhere((task) => task.id == id);
        if (_currentTask.value?.id == id) {
          _currentTask.value = null;
        }
        _applyFilters();
        debugPrint('تم حذف المهمة');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في حذف المهمة: $e';
      debugPrint('خطأ في حذف المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تغيير حالة المهمة
  Future<bool> changeTaskStatus(int id, int statusId) async {
    return await updateTask(
      id: id,
      status: statusId,
      completedAt: statusId == 3 // assuming 3 is completed status ID
          ? DateTime.now()
          : null,
    );
  }

  /// تعيين مهمة لمستخدم
  Future<bool> assignTask(int taskId, int assigneeId) async {
    return await updateTask(
      id: taskId,
      assigneeId: assigneeId,
    );
  }

  /// إضافة تعليق على المهمة
  Future<bool> addTaskComment(int taskId, String content) async {
    try {
      if (!UserHelper.isLoggedIn()) {
        debugPrint('خطأ: لا يوجد مستخدم مسجل دخول');
        return false;
      }

      final currentUserId = UserHelper.getCurrentUserId();
      final comment = TaskComment(
        id: 0, // سيتم تعيينه من قبل الخادم
        taskId: taskId,
        userId: currentUserId,
        content: content,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        isDeleted: false,
      );

      final createdComment = await _commentsApiService.createTaskComment(comment);

      // تحديث قائمة التعليقات المحلية
      _comments.add(createdComment);

      debugPrint('تم إضافة التعليق بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة التعليق: $e');
      return false;
    }
  }

  /// تحديث أولوية المهمة
  Future<bool> updateTaskPriority(int taskId, int userId, int priority) async {
    return await updateTask(
      id: taskId,
      priority: priority,
    );
  }

  /// تحديث حالة المهمة
  Future<bool> updateTaskStatus(int taskId, int userId, int status) async {
    return await updateTask(
      id: taskId,
      status: status,
      completedAt: status == 3 ? DateTime.now() : null, // assuming 3 is completed
    );
  }

  /// تحديث تقدم المهمة
  Future<bool> updateTaskProgress(
    int taskId,
    int userId,
    double progressPercentage, {
    String? notes,
    String? evidenceType,
    String? evidenceDescription,
    int? attachmentId,
  }) async {
    try {
      // استخدام خدمة TaskProgressTrackers لتحديث التقدم
      await _progressTrackersApiService.updateTaskProgress(
        taskId,
        progressPercentage,
        notes ?? '',
      );

      // تحديث نسبة الإكمال في المهمة نفسها
      await updateTask(
        id: taskId,
        completionPercentage: progressPercentage.toInt(),
        completedAt: progressPercentage >= 100 ? DateTime.now() : null,
      );

      debugPrint('تم تحديث تقدم المهمة: $progressPercentage%');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث تقدم المهمة: $e');
      return false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = List<Task>.from(_allTasks);

    // تطبيق مرشح الحالة
    if (_statusFilter.value != null) {
      filtered = filtered.where((task) => task.status == _statusFilter.value!).toList();
    }

    // تطبيق مرشح الأولوية
    if (_priorityFilter.value != null) {
      filtered = filtered.where((task) => task.priority == _priorityFilter.value!).toList();
    }

    // تطبيق مرشح المعين إليه
    if (_assigneeFilter.value != null) {
      filtered = filtered.where((task) => task.assigneeId == _assigneeFilter.value).toList();
    }

    // تطبيق مرشح القسم
    if (_departmentFilter.value != null) {
      filtered = filtered.where((task) => task.departmentId == _departmentFilter.value).toList();
    }

    // تطبيق مرشح المهام المتأخرة
    if (_showOverdueOnly.value) {
      filtered = filtered.where((task) => task.isOverdue()).toList();
    }

    // تطبيق مرشح البحث المحلي
    if (_searchQuery.value.isNotEmpty) {
      filtered = filtered.where((task) =>
          task.title.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
          (task.description?.toLowerCase().contains(_searchQuery.value.toLowerCase()) ?? false)
      ).toList();
    }

    _filteredTasks.assignAll(filtered);
  }

  /// تعيين مرشحات
  void setStatusFilter(int? statusId) {
    _statusFilter.value = statusId;
    _applyFilters();
  }

  void setPriorityFilter(int? priorityId) {
    _priorityFilter.value = priorityId;
    _applyFilters();
  }

  void setAssigneeFilter(int? assigneeId) {
    _assigneeFilter.value = assigneeId;
    _applyFilters();
  }

  void setDepartmentFilter(int? departmentId) {
    _departmentFilter.value = departmentId;
    _applyFilters();
  }

  void setOverdueFilter(bool showOverdueOnly) {
    _showOverdueOnly.value = showOverdueOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _statusFilter.value = null;
    _priorityFilter.value = null;
    _assigneeFilter.value = null;
    _departmentFilter.value = null;
    _showOverdueOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
    _errorMessage.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllTasks();
  }

  /// الحصول على إحصائيات المهام
  Map<String, int> get taskStats {
    return {
      'total': _allTasks.length,
      'pending': _allTasks.where((t) => t.status == 1).length, // assuming 1 is pending
      'inProgress': _allTasks.where((t) => t.status == 2).length, // assuming 2 is in progress
      'completed': _allTasks.where((t) => t.status == 3).length, // assuming 3 is completed
      'overdue': _allTasks.where((t) => t.isOverdue()).length,
    };
  }

  /// الحصول على عدد المهام الفرعية
  Future<int> getSubtasksCount(String taskId) async {
    final taskIdInt = int.tryParse(taskId);
    if (taskIdInt == null) return 0;

    try {
      final subtasks = await _subtasksApiService.getSubtasksByTask(taskIdInt);
      return subtasks.length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد المهام الفرعية: $e');
      return 0;
    }
  }

  /// الحصول على عدد المهام الفرعية المكتملة
  Future<int> getCompletedSubtasksCount(String taskId) async {
    final taskIdInt = int.tryParse(taskId);
    if (taskIdInt == null) return 0;

    try {
      final subtasks = await _subtasksApiService.getSubtasksByTask(taskIdInt);
      return subtasks.where((s) => s.isCompleted).length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد المهام الفرعية المكتملة: $e');
      return 0;
    }
  }

  /// الاشتراك في تحديثات الرسائل
  Worker subscribeToMessagesUpdates(VoidCallback callback) {
    return ever(_taskMessages, (_) => callback());
  }

  /// تحديث رسالة الخطأ
  void setError(String message) {
    _error.value = message;
    _errorMessage.value = message;
  }

  // دوال إضافية مطلوبة للتوافق مع الشاشات

  /// التحقق من وجود البيانات الأساسية وإنشاؤها إذا لزم الأمر
  Future<void> _ensureBasicDataExists() async {
    try {
      bool needsBasicData = false;

      // التحقق من وجود حالات المهام
      try {
        final statuses = await _apiService.getTaskStatuses();
        if (statuses.isEmpty) {
          debugPrint('لا توجد حالات مهام');
          needsBasicData = true;
        }
      } catch (e) {
        debugPrint('خطأ في الحصول على حالات المهام: $e');
        needsBasicData = true;
      }

      // التحقق من وجود أولويات المهام
      try {
        final priorities = await _apiService.getTaskPriorities();
        if (priorities.isEmpty) {
          debugPrint('لا توجد أولويات مهام');
          needsBasicData = true;
        }
      } catch (e) {
        debugPrint('خطأ في الحصول على أولويات المهام: $e');
        needsBasicData = true;
      }

      if (needsBasicData) {
        debugPrint('محاولة إنشاء البيانات الأساسية...');
        await _createBasicSeedData();
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من البيانات الأساسية: $e');
      // في حالة الخطأ، نحاول المتابعة بالقيم الافتراضية
    }
  }

  /// إنشاء البيانات الأساسية
  Future<void> _createBasicSeedData() async {
    try {
      // تهيئة SeedDataController إذا لم يكن موجوداً
      if (!Get.isRegistered<SeedDataController>()) {
        Get.put(SeedDataController(), permanent: true);
      }

      // استخدام SeedDataController لإنشاء البيانات
      final seedController = Get.find<SeedDataController>();
      await seedController.seedTaskStatuses();
      await seedController.seedTaskPriorities();
      debugPrint('تم إنشاء البيانات الأساسية بنجاح');
    } catch (e) {
      debugPrint('خطأ في إنشاء البيانات الأساسية: $e');
      // يمكن المتابعة بالقيم الافتراضية
    }
  }

  /// الحصول على حالة صحيحة للمهمة
  Future<int> _getValidStatus() async {
    try {
      final statuses = await _apiService.getTaskStatuses();
      if (statuses.isNotEmpty) {
        // البحث عن الحالة الافتراضية أو أول حالة متاحة
        final defaultStatus = statuses.firstWhere(
          (status) => status.isDefault == true,
          orElse: () => statuses.first,
        );
        return defaultStatus.id;
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على حالة صحيحة: $e');
    }
    return 1; // قيمة افتراضية
  }

  /// الحصول على أولوية صحيحة للمهمة
  Future<int> _getValidPriority(int requestedPriority) async {
    try {
      final priorities = await _apiService.getTaskPriorities();
      if (priorities.isNotEmpty) {
        // البحث عن الأولوية المطلوبة
        final priority = priorities.firstWhere(
          (p) => p.id == requestedPriority,
          orElse: () => priorities.firstWhere(
            (p) => p.isDefault == true,
            orElse: () => priorities.first,
          ),
        );
        return priority.id;
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على أولوية صحيحة: $e');
    }
    return requestedPriority; // إرجاع الأولوية المطلوبة كقيمة افتراضية
  }

  /// تحميل بيانات تتبع الوقت للمهمة
  Future<void> loadTaskTimeTracking(int taskId) async {
    try {
      // TODO: تنفيذ تحميل بيانات تتبع الوقت من API
      debugPrint('تحميل بيانات تتبع الوقت للمهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات تتبع الوقت: $e');
    }
  }

  /// تحميل المهام المشابهة
  Future<void> loadSimilarTasks(dynamic taskId) async {
    try {
      // TODO: تنفيذ تحميل المهام المشابهة من API
      debugPrint('تحميل المهام المشابهة للمهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في تحميل المهام المشابهة: $e');
    }
  }

  /// بدء تتبع الوقت
  Future<void> startTimeTracking(int taskId, int userId, {String? description}) async {
    try {
      // TODO: تنفيذ بدء تتبع الوقت من API
      debugPrint('بدء تتبع الوقت للمهمة $taskId للمستخدم $userId');
    } catch (e) {
      debugPrint('خطأ في بدء تتبع الوقت: $e');
    }
  }

  /// إنهاء تتبع الوقت
  Future<void> endTimeTracking(int entryId) async {
    try {
      // TODO: تنفيذ إنهاء تتبع الوقت من API
      debugPrint('إنهاء تتبع الوقت للسجل $entryId');
    } catch (e) {
      debugPrint('خطأ في إنهاء تتبع الوقت: $e');
    }
  }

  /// الحصول على سجلات تتبع الوقت النشطة
  Future<List<dynamic>> getActiveTimeTrackingEntries() async {
    try {
      // TODO: تنفيذ الحصول على سجلات تتبع الوقت النشطة من API
      debugPrint('الحصول على سجلات تتبع الوقت النشطة');
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات تتبع الوقت النشطة: $e');
      return [];
    }
  }

  /// تصدير تقرير مقارنة المهام المشابهة
  Future<String?> exportSimilarTasksComparisonReport() async {
    try {
      // TODO: تنفيذ تصدير تقرير مقارنة المهام المشابهة
      debugPrint('تصدير تقرير مقارنة المهام المشابهة');
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير تقرير مقارنة المهام المشابهة: $e');
      return null;
    }
  }

  /// تصدير تقرير أداء المستخدم
  Future<String?> exportUserPerformanceReport() async {
    try {
      // TODO: تنفيذ تصدير تقرير أداء المستخدم
      debugPrint('تصدير تقرير أداء المستخدم');
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير تقرير أداء المستخدم: $e');
      return null;
    }
  }

  /// تحميل ملخص تقدم المهمة
  Future<void> loadTaskProgressSummary(int taskId) async {
    try {
      // TODO: تنفيذ تحميل ملخص تقدم المهمة من API
      debugPrint('تحميل ملخص تقدم المهمة $taskId');

      // مؤقتاً، إنشاء ملخص وهمي للاختبار
      final summary = TaskProgressSummary(
        taskId: taskId,
        currentProgress: 65.0,
        lastUpdated: DateTime.now(),
        userContributions: {'user1': 40.0, 'user2': 25.0},
        totalUpdates: 8,
        averageProgressPerUpdate: 8.125,
        recentUpdates: [],
        dailyProgressRate: 5.2,
        estimatedDaysToCompletion: 7.0,
        totalPercentage: 65.0,
      );

      _progressSummary.value = summary;
    } catch (e) {
      debugPrint('خطأ في تحميل ملخص تقدم المهمة: $e');
    }
  }

  /// تحميل ملخص تتبع الوقت للمهمة
  Future<void> loadTaskTimeTrackingSummary(int taskId) async {
    try {
      // TODO: تنفيذ تحميل ملخص تتبع الوقت من API
      debugPrint('تحميل ملخص تتبع الوقت للمهمة $taskId');

      // مؤقتاً، إنشاء ملخص وهمي للاختبار
      final summary = TaskTimeTrackingSummary(
        taskId: taskId,
        userMinutes: {'user1': 120, 'user2': 90},
        totalMinutes: 210,
        activeEntries: 2,
        lastActivity: DateTime.now().subtract(const Duration(hours: 2)),
        lastUpdated: DateTime.now().subtract(const Duration(minutes: 30)),
        userEntries: {'user1': [], 'user2': []},
      );

      _timeTrackingSummary.value = summary;
    } catch (e) {
      debugPrint('خطأ في تحميل ملخص تتبع الوقت: $e');
    }
  }

  /// إضافة مرفق للمهمة
  Future<bool> addAttachment(int taskId, int userId, File file, {String? description}) async {
    try {
      // TODO: تنفيذ إضافة المرفق من خلال API
      debugPrint('إضافة مرفق للمهمة $taskId: ${file.path}');

      // مؤقتاً، إرجاع نجاح العملية
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة المرفق: $e');
      return false;
    }
  }

  /// الحصول على متتبعات التقدم لمستخدم محدد في مهمة محددة
  Future<List<TaskProgressTracker>> getProgressTrackersForUserInTask(
    String taskId,
    String userId,
  ) async {
    try {
      // تحويل معرفات النصوص إلى أرقام
      final taskIdInt = int.parse(taskId);
      final userIdInt = int.parse(userId);

      // الحصول على جميع متتبعات التقدم للمهمة
      final allTrackers = await _progressTrackersApiService.getTrackersByTask(taskIdInt);

      // تصفية المتتبعات للمستخدم المحدد
      final userTrackers = allTrackers.where((tracker) =>
        tracker.updatedBy == userIdInt
      ).toList();

      // إضافة بيانات وهمية للمساهمات (يمكن تحسينها لاحقاً)
      final enhancedTrackers = userTrackers.map((tracker) {
        return tracker.copyWith(
          evidenceType: _determineEvidenceType(tracker),
          evidenceDescription: _generateEvidenceDescription(tracker),
          contributionPercentage: tracker.progressPercentage,
        );
      }).toList();

      return enhancedTrackers;
    } catch (e) {
      debugPrint('خطأ في الحصول على متتبعات التقدم للمستخدم: $e');
      return [];
    }
  }

  /// تحديد نوع الدليل بناءً على بيانات المتتبع
  String _determineEvidenceType(TaskProgressTracker tracker) {
    // منطق بسيط لتحديد نوع الدليل
    if (tracker.notes != null && tracker.notes!.contains('تعليق')) {
      return 'comment';
    } else if (tracker.notes != null && tracker.notes!.contains('ملف')) {
      return 'file';
    } else if (tracker.notes != null && tracker.notes!.contains('تحويل')) {
      return 'transfer';
    } else if (tracker.progressPercentage > 0) {
      return 'progress';
    } else {
      return 'activity';
    }
  }

  /// إنشاء وصف للدليل
  String _generateEvidenceDescription(TaskProgressTracker tracker) {
    final type = _determineEvidenceType(tracker);
    switch (type) {
      case 'comment':
        return 'تم حساب المساهمة تلقائيًا بناءً على إضافة تعليق';
      case 'file':
        return 'تم حساب المساهمة تلقائيًا بناءً على إرفاق ملف';
      case 'transfer':
        return 'تم حساب المساهمة تلقائيًا بناءً على تحويل المهمة';
      case 'progress':
        return 'تم حساب المساهمة تلقائيًا بناءً على تحديث التقدم';
      default:
        return 'تم حساب المساهمة تلقائيًا بناءً على النشاط';
    }
  }

  // خصائص إضافية مطلوبة - إضافة getters للمتغيرات الجديدة

  /// إضافة تعليق (دالة مساعدة للتوافق)
  Future<bool> addComment(String taskId, String content) async {
    final taskIdInt = int.tryParse(taskId);
    if (taskIdInt == null) return false;
    return await addTaskComment(taskIdInt, content);
  }

  /// إرسال رسالة مهمة (دالة وهمية للتوافق)
  Future<Map<String, dynamic>?> sendTaskMessage(
    String taskId,
    String content, {
    int? contentType,
    List<String>? mentionedUserIds,
    String? replyToMessageId,
  }) async {
    try {
      _isSendingMessage.value = true;

      // محاكاة إرسال رسالة
      await Future.delayed(const Duration(seconds: 1));

      final message = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'taskId': taskId,
        'senderId': UserHelper.getCurrentUserId().toString(),
        'content': content,
        'contentType': contentType ?? 1,
        'createdAt': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'mentionedUserIds': mentionedUserIds,
        'replyToMessageId': replyToMessageId,
      };

      _taskMessages.add(message);
      return message;
    } catch (e) {
      debugPrint('خطأ في إرسال رسالة المهمة: $e');
      return null;
    } finally {
      _isSendingMessage.value = false;
    }
  }

  /// الحصول على رسالة بالمعرف (دالة وهمية للتوافق)
  Future<Map<String, dynamic>?> getMessageById(String messageId) async {
    try {
      return _taskMessages.firstWhere(
        (message) => message['id'] == messageId,
        orElse: () => {},
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسالة: $e');
      return null;
    }
  }

  /// إضافة مرفق لرسالة (دالة وهمية للتوافق)
  Future<Map<String, dynamic>?> addMessageAttachment(
    String messageId,
    dynamic file,
    String fileName,
  ) async {
    try {
      final attachment = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'messageId': messageId,
        'fileName': fileName,
        'fileSize': 0,
        'fileType': fileName.split('.').last,
        'uploadedAt': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      };

      _messageAttachments.add(attachment);
      return attachment;
    } catch (e) {
      debugPrint('خطأ في إضافة مرفق الرسالة: $e');
      return null;
    }
  }
}
