import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/api/api_service.dart';

/// مساعد قاعدة البيانات
/// 
/// يوفر واجهة للتعامل مع قاعدة البيانات عبر API
class DatabaseHelper extends GetxController {
  final ApiService _apiService = ApiService();

  // حالة الاتصال
  final RxBool _isConnected = false.obs;
  final RxString _error = ''.obs;
  final RxBool _isLoading = false.obs;

  // معلومات قاعدة البيانات
  final RxMap<String, dynamic> _databaseInfo = <String, dynamic>{}.obs;
  final RxList<String> _tableNames = <String>[].obs;

  // Getters
  bool get isConnected => _isConnected.value;
  String get error => _error.value;
  bool get isLoading => _isLoading.value;
  Map<String, dynamic> get databaseInfo => _databaseInfo;
  List<String> get tableNames => _tableNames;

  @override
  void onInit() {
    super.onInit();
    // تأخير بسيط للتأكد من تهيئة ApiService
    Future.delayed(const Duration(milliseconds: 500), () {
      testConnection();
    });
  }

  /// اختبار الاتصال بقاعدة البيانات
  Future<bool> testConnection() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // محاولة الاتصال بـ endpoint Database أولاً
      try {
        final response = await _apiService.get('/api/Database/status', requireAuth: false);
        if (response.statusCode == 200) {
          final result = _apiService.handleResponse<Map<String, dynamic>>(
            response,
            (json) => json,
          );
          _isConnected.value = result['success'] ?? true;
        } else {
          _isConnected.value = false;
        }
      } catch (e) {
        debugPrint('فشل في الاتصال بـ Database endpoint: $e');
        // محاولة استخدام endpoint بديل
        try {
          final response = await _apiService.get('/api/Auth/test', requireAuth: false);
          _isConnected.value = response.statusCode == 200;
        } catch (e2) {
          debugPrint('فشل في الاتصال بـ Auth endpoint: $e2');
          // محاولة أخيرة مع endpoint Users
          try {
            final response = await _apiService.get('/api/Users', requireAuth: true);
            _isConnected.value = response.statusCode == 200 || response.statusCode == 401; // 401 يعني الخادم يعمل
          } catch (e3) {
            debugPrint('فشل في جميع محاولات الاتصال: $e3');
            _isConnected.value = false;
          }
        }
      }

      if (_isConnected.value) {
        await _loadDatabaseInfo();
        await _loadTableNames();
        debugPrint('تم تأسيس الاتصال بقاعدة البيانات بنجاح');
      } else {
        _error.value = 'فشل في الاتصال بقاعدة البيانات. تحقق من إعدادات الشبكة.';
      }

      return _isConnected.value;
    } catch (e) {
      _error.value = 'خطأ عام في الاتصال بقاعدة البيانات: $e';
      _isConnected.value = false;
      debugPrint('خطأ عام في اختبار الاتصال: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// التحقق من حالة تسجيل الدخول
  bool get isLoggedIn => _apiService.isLoggedIn;

  /// تحميل معلومات قاعدة البيانات
  Future<void> _loadDatabaseInfo() async {
    try {
      // استخدام endpoint موجود أو إنشاء معلومات افتراضية
      final info = {
        'name': 'TaskManagement',
        'version': '1.0',
        'status': 'connected',
        'lastUpdate': DateTime.now().toIso8601String(),
      };
      _databaseInfo.assignAll(info);
    } catch (e) {
      debugPrint('خطأ في تحميل معلومات قاعدة البيانات: $e');
    }
  }

  /// تحميل أسماء الجداول
  Future<void> _loadTableNames() async {
    try {
      // قائمة الجداول المتاحة من الباك اند
      final tables = [
        'Users',
        'Departments', 
        'Tasks',
        'TaskStatuses',
        'TaskPriorities',
        'TaskTypes',
        'TaskComments',
        'TaskHistories',
        'Subtasks',
        'Permissions',
        'UserPermissions',
        'ArchiveCategories',
        'ArchiveDocuments',
        'ArchiveTags',
        'ArchiveDocumentTags',
        'Attachments',
        'Backups',
        'CalendarEvents',
        'ChatGroups',
        'GroupMembers',
        'Messages',
        'MessageAttachments',
        'MessageReactions',
        'Notifications',
        'NotificationSettings',
        'Reports',
        'ReportSchedules',
        'SystemLogs',
        'SystemSettings',
        'TimeTrackingEntries',
        'ActivityLogs',
        'Dashboards',
        'DashboardWidgets',
        'TaskProgressTrackers'
      ];
      
      _tableNames.assignAll(tables);
    } catch (e) {
      debugPrint('خطأ في تحميل أسماء الجداول: $e');
    }
  }

  /// الحصول على بيانات جدول
  Future<List<Map<String, dynamic>>> getTableData(String tableName, {
    int page = 1,
    int pageSize = 50,
    String? searchQuery,
    Map<String, dynamic>? filters,
  }) async {
    final result = await getTableDataWithPagination(
      tableName,
      page: page,
      pageSize: pageSize,
      searchQuery: searchQuery,
      filters: filters,
    );
    return result['data'] as List<Map<String, dynamic>>;
  }

  /// الحصول على بيانات جدول مع معلومات Pagination المتقدمة
  Future<Map<String, dynamic>> getTableDataWithPagination(String tableName, {
    int page = 1,
    int pageSize = 50,
    String? searchQuery,
    Map<String, dynamic>? filters,
    String? orderBy,
    String orderDirection = 'ASC',
  }) async {
    try {
      // التحقق من حالة تسجيل الدخول للجداول التي تتطلب مصادقة
      final requiresAuth = _tableRequiresAuth(tableName);
      if (requiresAuth && !isLoggedIn) {
        debugPrint('المستخدم غير مسجل دخول - لا يمكن الحصول على بيانات الجدول $tableName');
        _error.value = 'يرجى تسجيل الدخول للوصول إلى البيانات';
        return {'data': [], 'totalRecords': 0, 'totalPages': 1, 'currentPage': page};
      }

      // تحديد المسار المناسب حسب اسم الجدول
      String endpoint = _getEndpointForTable(tableName);

      // إضافة معاملات الاستعلام مع دعم التصفح المتقدم
      final queryParams = <String, String>{
        'page': page.toString(),
        'pageSize': pageSize.toString(),
      };

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryParams['search'] = searchQuery;
        queryParams['searchQuery'] = searchQuery; // دعم كلا الاسمين
      }

      if (orderBy != null && orderBy.isNotEmpty) {
        queryParams['orderBy'] = orderBy;
        queryParams['orderDirection'] = orderDirection;
        queryParams['sortBy'] = orderBy; // دعم كلا الاسمين
        queryParams['sortOrder'] = orderDirection;
      }

      if (filters != null) {
        for (final entry in filters.entries) {
          if (entry.value != null) {
            queryParams[entry.key] = entry.value.toString();
          }
        }
      }

      debugPrint('إرسال طلب إلى: $endpoint مع معاملات: $queryParams');
      final response = await _apiService.get(endpoint, queryParams: queryParams, requireAuth: requiresAuth);

      if (response.statusCode == 200) {
        final responseData = _apiService.handleResponse<dynamic>(
          response,
          (json) => json,
        );

        List<dynamic> dataList;
        int totalRecords = 0;
        int totalPages = 1;

        // التحقق من تنسيق الاستجابة
        if (responseData is Map<String, dynamic>) {
          // تنسيق pagination متقدم
          if (responseData.containsKey('data') || responseData.containsKey('items')) {
            dataList = responseData['data'] ?? responseData['items'] ?? [];
            totalRecords = responseData['totalRecords'] ?? responseData['totalCount'] ?? responseData['total'] ?? dataList.length;
            totalPages = responseData['totalPages'] ?? ((totalRecords / pageSize).ceil());
          } else {
            // تنسيق بسيط - البيانات في الجذر
            dataList = [responseData];
            totalRecords = 1;
            totalPages = 1;
          }
        } else if (responseData is List) {
          // تنسيق قائمة بسيطة
          dataList = responseData;
          totalRecords = dataList.length;
          totalPages = (totalRecords / pageSize).ceil();
        } else {
          dataList = [];
          totalRecords = 0;
          totalPages = 1;
        }

        debugPrint('تم تحميل ${dataList.length} سجل من أصل $totalRecords سجل');
        debugPrint('الصفحة $page من أصل $totalPages صفحة');
        _error.value = ''; // مسح أي خطأ سابق

        // تحويل البيانات المعقدة إلى تنسيق مبسط للعرض في الجدول
        final simplifiedData = dataList.map((item) {
          final Map<String, dynamic> simplifiedItem = {};
          final Set<String> usedKeys = <String>{}; // لتجنب المفاتيح المكررة

          if (item is Map<String, dynamic>) {
            // نسخ الحقول الأساسية فقط (تجنب الكائنات المعقدة)
            item.forEach((key, value) {
              if (value == null ||
                  value is String ||
                  value is num ||
                  value is bool ||
                  value is int ||
                  value is double) {
                // التأكد من عدم وجود مفتاح مكرر
                if (!usedKeys.contains(key.toLowerCase())) {
                  simplifiedItem[key] = value;
                  usedKeys.add(key.toLowerCase());
                }
              } else if (value is Map<String, dynamic>) {
                // للكائنات المعقدة، أخذ الـ id والـ name فقط مع تجنب التكرار
                if (value.containsKey('id')) {
                  final idKey = '${key}Id';
                  if (!usedKeys.contains(idKey.toLowerCase())) {
                    simplifiedItem[idKey] = value['id'];
                    usedKeys.add(idKey.toLowerCase());
                  }
                }
                if (value.containsKey('name')) {
                  final nameKey = '${key}Name';
                  if (!usedKeys.contains(nameKey.toLowerCase())) {
                    simplifiedItem[nameKey] = value['name'];
                    usedKeys.add(nameKey.toLowerCase());
                  }
                }
              }
            });
          }

          return simplifiedItem;
        }).toList();

        return {
          'data': simplifiedData,
          'totalRecords': totalRecords,
          'totalPages': totalPages,
          'currentPage': page,
          'pageSize': pageSize,
        };

      } else if (response.statusCode == 401) {
        debugPrint('غير مصرح للوصول للجدول $tableName - انتهت صلاحية الجلسة');
        _error.value = 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى';
        return {'data': [], 'totalRecords': 0, 'totalPages': 1, 'currentPage': page};
      } else if (response.statusCode == 403) {
        debugPrint('ممنوع الوصول للجدول $tableName - صلاحيات غير كافية');
        _error.value = 'ليس لديك صلاحية للوصول إلى هذا الجدول';
        return {'data': [], 'totalRecords': 0, 'totalPages': 1, 'currentPage': page};
      } else {
        debugPrint('خطأ HTTP ${response.statusCode} للجدول $tableName');
        _error.value = 'خطأ في الخادم (${response.statusCode})';
        return {'data': [], 'totalRecords': 0, 'totalPages': 1, 'currentPage': page};
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات الجدول $tableName: $e');
      _error.value = 'خطأ في الاتصال بالخادم';
      return {'data': [], 'totalRecords': 0, 'totalPages': 1, 'currentPage': page};
    }
  }

  /// إنشاء سجل جديد
  Future<Map<String, dynamic>?> createRecord(String tableName, Map<String, dynamic> data) async {
    try {
      String endpoint = _getEndpointForTable(tableName);
      final response = await _apiService.post(endpoint, data);
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json is Map<String, dynamic> ? json : {},
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء سجل في $tableName: $e');
      return null;
    }
  }

  /// تحديث سجل
  Future<Map<String, dynamic>?> updateRecord(String tableName, int id, Map<String, dynamic> data) async {
    try {
      String endpoint = _getEndpointForTable(tableName);
      final response = await _apiService.put('$endpoint/$id', data);
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json is Map<String, dynamic> ? json : {},
      );
    } catch (e) {
      debugPrint('خطأ في تحديث سجل في $tableName: $e');
      return null;
    }
  }

  /// حذف سجل
  Future<bool> deleteRecord(String tableName, int id) async {
    try {
      String endpoint = _getEndpointForTable(tableName);
      final response = await _apiService.delete('$endpoint/$id');
      return response.statusCode == 200 || response.statusCode == 204;
    } catch (e) {
      debugPrint('خطأ في حذف سجل من $tableName: $e');
      return false;
    }
  }

  /// الحصول على المسار المناسب للجدول
  String _getEndpointForTable(String tableName) {
    switch (tableName.toLowerCase()) {
      case 'users':
        return '/api/Users';
      case 'departments':
        return '/api/Departments';
      case 'tasks':
        return '/api/Tasks';
      case 'taskstatuses':
        return '/api/TaskStatuses';
      case 'taskpriorities':
        return '/api/TaskPriorities';
      case 'tasktypes':
        return '/api/TaskTypes';
      case 'taskcomments':
        return '/api/TaskComments';
      case 'taskhistories':
        return '/api/TaskHistories';
      case 'subtasks':
        return '/api/Subtasks';
      case 'permissions':
        return '/api/Permissions';
      case 'userpermissions':
        return '/api/UserPermissions';
      case 'archivecategories':
        return '/api/ArchiveCategories';
      case 'archivedocuments':
        return '/api/ArchiveDocuments';
      case 'archivetags':
        return '/api/ArchiveTags';
      case 'archivedocumenttags':
        return '/api/ArchiveDocumentTags';
      case 'attachments':
        return '/api/Attachments';
      case 'backups':
        return '/api/Backups';
      case 'calendarevents':
        return '/api/CalendarEvents';
      case 'chatgroups':
        return '/api/ChatGroups';
      case 'messages':
        return '/api/Messages';
      case 'notifications':
        return '/api/Notifications';
      case 'reports':
        return '/api/Reports';
      case 'systemlogs':
        return '/api/SystemLogs';
      case 'systemsettings':
        return '/api/SystemSettings';
      case 'timetrackingentries':
        return '/api/TimeTrackingEntries';
      case 'activitylogs':
        return '/api/ActivityLogs';
      default:
        return '/api/$tableName';
    }
  }

  /// التحقق من ما إذا كان الجدول يتطلب مصادقة
  bool _tableRequiresAuth(String tableName) {
    // الجداول التي لا تتطلب مصادقة (عامة)
    final publicTables = [
      'taskstatuses',
      'taskpriorities',
      'tasktypes',
      'departments', // قد يكون عام حسب التطبيق
      'activitylogs', // إضافة ActivityLogs للجداول العامة
    ];

    return !publicTables.contains(tableName.toLowerCase());
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// إعادة تحميل البيانات
  @override
  Future<void> refresh() async {
    await testConnection();
  }
}
