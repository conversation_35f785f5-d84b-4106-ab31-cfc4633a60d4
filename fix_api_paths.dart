import 'dart:io';

void main() async {
  // قائمة الملفات التي تحتاج إصلاح المسارات
  final files = [
    'lib/services/api/users_api_service.dart',
    'lib/services/api/permissions_api_service.dart',
  ];

  // قائمة المسارات التي تحتاج إصلاح
  final pathReplacements = {
    "'/Users": "'/api/Users",
    "'/Permissions": "'/api/Permissions",
    "'/Departments": "'/api/Departments",
    "'/Tasks": "'/api/Tasks",
    "'/Reports": "'/api/Reports",
    "'/Notifications": "'/api/Notifications",
  };

  for (final filePath in files) {
    await fixApiPaths(filePath, pathReplacements);
  }
  
  print('تم إصلاح جميع مسارات API');
}

Future<void> fixApiPaths(String filePath, Map<String, String> replacements) async {
  try {
    final file = File(filePath);
    if (!await file.exists()) {
      print('الملف غير موجود: $filePath');
      return;
    }

    String content = await file.readAsString();
    bool hasChanges = false;

    for (final entry in replacements.entries) {
      final oldPath = entry.key;
      final newPath = entry.value;
      
      if (content.contains(oldPath)) {
        content = content.replaceAll(oldPath, newPath);
        hasChanges = true;
        print('تم إصلاح: $oldPath → $newPath في $filePath');
      }
    }

    if (hasChanges) {
      await file.writeAsString(content);
      print('تم حفظ التغييرات في: $filePath');
    } else {
      print('لا توجد تغييرات مطلوبة في: $filePath');
    }
  } catch (e) {
    print('خطأ في معالجة الملف $filePath: $e');
  }
}
