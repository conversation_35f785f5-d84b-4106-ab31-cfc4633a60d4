-- إنشاء مستخدمين افتراضيين بسيط
-- يجب تشغيل هذا السكريبت على قاعدة البيانات databasetasks

USE [databasetasks]
GO

-- إنشاء مستخدم مدير النظام
IF NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO users (
        name, 
        email, 
        username, 
        password, 
        role, 
        is_active, 
        is_online, 
        is_deleted, 
        created_at,
        first_name,
        last_name
    )
    VALUES (
        N'مدير النظام',
        '<EMAIL>',
        'admin',
        '$2a$11$8K1p/a0dL2LkqvQOuiOX2uy7lQompaqtbyrd4sztgJSGHINfS4IA6', -- admin123
        5, -- SuperAdmin
        1,
        0,
        0,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        N'مدير',
        N'النظام'
    )
    PRINT 'تم إنشاء مستخدم مدير النظام'
END
ELSE
BEGIN
    PRINT 'مستخدم مدير النظام موجود بالفعل'
END

-- إنشاء مستخدم تجريبي
IF NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO users (
        name, 
        email, 
        username, 
        password, 
        role, 
        is_active, 
        is_online, 
        is_deleted, 
        created_at,
        first_name,
        last_name
    )
    VALUES (
        N'مستخدم تجريبي',
        '<EMAIL>',
        'testuser',
        '$2a$11$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- user123
        1, -- User
        1,
        0,
        0,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        N'مستخدم',
        N'تجريبي'
    )
    PRINT 'تم إنشاء مستخدم تجريبي'
END
ELSE
BEGIN
    PRINT 'مستخدم تجريبي موجود بالفعل'
END

-- إنشاء مستخدم مدير
IF NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO users (
        name, 
        email, 
        username, 
        password, 
        role, 
        is_active, 
        is_online, 
        is_deleted, 
        created_at,
        first_name,
        last_name
    )
    VALUES (
        N'مدير تجريبي',
        '<EMAIL>',
        'manager',
        '$2a$11$N0qjqp7cvspuEcHu5uQz2OmwrJaAQBdulvs.LjsxIDi9.WdyFNKlO', -- manager123
        3, -- Manager
        1,
        0,
        0,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        N'مدير',
        N'تجريبي'
    )
    PRINT 'تم إنشاء مدير تجريبي'
END
ELSE
BEGIN
    PRINT 'مدير تجريبي موجود بالفعل'
END

PRINT '=== تم الانتهاء من إنشاء المستخدمين الافتراضيين ==='
PRINT 'المستخدمين المتاحين:'
PRINT '1. مدير النظام - <EMAIL> / admin123'
PRINT '2. مستخدم تجريبي - <EMAIL> / user123'
PRINT '3. مدير تجريبي - <EMAIL> / manager123'

-- عرض المستخدمين المنشأين
SELECT 
    id,
    name,
    email,
    username,
    role,
    is_active,
    CASE role
        WHEN 1 THEN 'User'
        WHEN 2 THEN 'Supervisor'
        WHEN 3 THEN 'Manager'
        WHEN 4 THEN 'Admin'
        WHEN 5 THEN 'SuperAdmin'
        ELSE 'Unknown'
    END as role_name
FROM users 
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
ORDER BY role DESC

GO
