import 'package:flutter/material.dart';

import '../../../constants/app_styles.dart';
import '../../../models/dashboard_model.dart' as simple_model;
import '../reporting/visualizations/bar_chart_visualization.dart';
import '../reporting/visualizations/line_chart_visualization.dart';
import '../reporting/visualizations/pie_chart_visualization.dart';
import '../reporting/visualizations/kpi_card_visualization.dart';
import '../common/loading_indicator.dart';

/// مكون عنصر المخطط بتصميم Monday.com
///
/// يعرض عنصر المخطط في لوحة المعلومات بتصميم مشابه لـ Monday.com
class MondayStyleChartWidget extends StatefulWidget {
  /// عنصر لوحة المعلومات
  final simple_model.SimpleDashboardWidget widget;

  /// هل في وضع التعديل
  final bool isEditing;

  /// حدث الحذف
  final VoidCallback onDelete;

  /// حدث التعديل
  final VoidCallback onEdit;

  /// حدث النقل
  final Function(Offset newPosition) onMove;

  /// حدث تغيير الحجم
  final Function(Size newSize) onResize;

  const MondayStyleChartWidget({
    super.key,
    required this.widget,
    required this.isEditing,
    required this.onDelete,
    required this.onEdit,
    required this.onMove,
    required this.onResize,
  });

  @override
  State<MondayStyleChartWidget> createState() => _MondayStyleChartWidgetState();
}

class _MondayStyleChartWidgetState extends State<MondayStyleChartWidget> {
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  List<Map<String, dynamic>> _data = [];

  Offset _dragStartPosition = Offset.zero;
  Offset _widgetStartPosition = Offset.zero;

  // لا نحتاج لتخزين الحجم هنا لأنه سيتم تحديده من خلال الـ Positioned widget

  @override
  void initState() {
    super.initState();
    // لا نحتاج لتحديد الحجم هنا لأنه سيتم تحديده من خلال الـ Positioned widget
    _loadData();
  }

  @override
  void didUpdateWidget(MondayStyleChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.widget.id != widget.widget.id ||
        oldWidget.widget.type != widget.widget.type ||
        oldWidget.widget.settings != widget.widget.settings) {
      _loadData();
    }
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // تحميل البيانات التجريبية حسب نوع العنصر
      await Future.delayed(const Duration(milliseconds: 500));

      switch (widget.widget.type) {
        case 'taskStatusChart':
          _data = [
            {'status': 'قيد التنفيذ', 'count': 15},
            {'status': 'مكتمل', 'count': 25},
            {'status': 'متأخر', 'count': 8},
            {'status': 'معلق', 'count': 5},
          ];
          break;
        case 'taskProgressChart':
          _data = [
            {'date': '2023-01-01', 'count': 5},
            {'date': '2023-01-02', 'count': 8},
            {'date': '2023-01-03', 'count': 12},
            {'date': '2023-01-04', 'count': 10},
            {'date': '2023-01-05', 'count': 15},
            {'date': '2023-01-06', 'count': 20},
            {'date': '2023-01-07', 'count': 18},
          ];
          break;
        case 'taskCounters':
          _data = [
            {'total': 50, 'completed': 25, 'overdue': 8, 'inProgress': 17},
          ];
          break;
        case 'userPerformanceChart':
          _data = [
            {'userName': 'أحمد', 'taskCount': 15},
            {'userName': 'محمد', 'taskCount': 20},
            {'userName': 'علي', 'taskCount': 12},
            {'userName': 'خالد', 'taskCount': 18},
            {'userName': 'عمر', 'taskCount': 10},
          ];
          break;
        case 'departmentPerformanceChart':
          _data = [
            {'departmentName': 'تطوير', 'taskCount': 30},
            {'departmentName': 'تسويق', 'taskCount': 25},
            {'departmentName': 'مبيعات', 'taskCount': 20},
            {'departmentName': 'دعم', 'taskCount': 15},
          ];
          break;
        default:
          _data = [];
          break;
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      margin: EdgeInsets.zero, // إزالة الهوامش الافتراضية للبطاقة
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max, // تحديد الحجم الرئيسي للعمود
        children: [
          // رأس العنصر
          _buildHeader(),

          // محتوى العنصر
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  /// بناء رأس العنصر
  Widget _buildHeader() {
    return GestureDetector(
      onPanStart: widget.isEditing ? _onPanStart : null,
      onPanUpdate: widget.isEditing ? _onPanUpdate : null,
      onPanEnd: widget.isEditing ? _onPanEnd : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.blue,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
        ),
        child: Row(
          children: [
            // أيقونة العنصر
            Icon(
              _getWidgetIcon(),
              size: 16,
              color: Colors.white,
            ),

            const SizedBox(width: 8),

            // عنوان العنصر
            Expanded(
              child: Text(
                widget.widget.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // أزرار الإجراءات
            if (widget.isEditing) ...[
              IconButton(
                icon: const Icon(Icons.edit, size: 16, color: Colors.white),
                onPressed: widget.onEdit,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                tooltip: 'تعديل',
              ),

              const SizedBox(width: 8),

              IconButton(
                icon: const Icon(Icons.delete, size: 16, color: Colors.white),
                onPressed: widget.onDelete,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                tooltip: 'حذف',
              ),
            ] else ...[
              IconButton(
                icon: const Icon(Icons.refresh, size: 16, color: Colors.white),
                onPressed: _loadData,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                tooltip: 'تحديث',
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء محتوى العنصر
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator(),
      );
    }

    if (_hasError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min, // إضافة هذه الخاصية لمنع التمدد غير المحدود
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage,
                style: AppStyles.bodyMedium.copyWith(color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadData,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    if (_data.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min, // إضافة هذه الخاصية لمنع التمدد غير المحدود
            children: [
              const Icon(
                Icons.bar_chart,
                color: Colors.grey,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد بيانات للعرض',
                style: AppStyles.bodyMedium.copyWith(color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // عرض المخطط حسب النوع
    return Padding(
      padding: const EdgeInsets.all(16),
      child: _buildChart(),
    );
  }

  /// بناء المخطط
  Widget _buildChart() {
    // استخراج إعدادات العنصر من خصائص العنصر
    final settings = widget.widget.settings;

    // استخراج إعدادات المخطط من الإعدادات المخزنة

    // تحديد نوع المخطط حسب نوع العنصر
    switch (widget.widget.type) {
      case 'taskStatusChart':
        return PieChartVisualization(
          title: widget.widget.title,
          description: null,
          data: _data,
          labelField: 'status',
          valueField: 'count',
          showValues: settings['showValues'] ?? true,
          showPercentages: true,
          showLegend: settings['showLegend'] ?? true,
          sectionColors: null,
        );

      case 'taskProgressChart':
        return LineChartVisualization(
          title: widget.widget.title,
          description: null,
          data: _data,
          xAxisField: 'date',
          xAxisLabel: 'التاريخ',
          yAxisFields: ['count'],
          yAxisLabel: 'العدد',
          showDots: settings['showDots'] ?? true,
          showArea: settings['showArea'] ?? false,
          showLabels: settings['showLabels'] ?? true,
          showGrid: settings['showGrid'] ?? true,
          showLegend: settings['showLegend'] ?? true,
          seriesColors: null,
        );

      case 'userPerformanceChart':
        return BarChartVisualization(
          title: widget.widget.title,
          description: null,
          data: _data,
          xAxisField: 'userName',
          xAxisLabel: 'المستخدم',
          yAxisField: 'taskCount',
          yAxisLabel: 'عدد المهام',
          orientation: ChartOrientation.vertical,
          showValues: settings['showValues'] ?? true,
          showLabels: settings['showLabels'] ?? true,
          showGrid: settings['showGrid'] ?? true,
          showLegend: settings['showLegend'] ?? true,
          seriesColors: null,
        );

      case 'departmentPerformanceChart':
        return BarChartVisualization(
          title: widget.widget.title,
          description: null,
          data: _data,
          xAxisField: 'departmentName',
          xAxisLabel: 'القسم',
          yAxisField: 'taskCount',
          yAxisLabel: 'عدد المهام',
          orientation: ChartOrientation.vertical,
          showValues: settings['showValues'] ?? true,
          showLabels: settings['showLabels'] ?? true,
          showGrid: settings['showGrid'] ?? true,
          showLegend: settings['showLegend'] ?? true,
          seriesColors: null,
        );

      case 'taskCounters':
        // KPI card expects a single data item, not a list
        final kpiData = _data.isNotEmpty ? _data.first : <String, dynamic>{};

        return KpiCardVisualization(
          title: widget.widget.title,
          description: null,
          data: kpiData,
          valueField: 'total',
          unit: 'مهمة',
          color: Colors.blue,
          icon: Icons.task_alt,
        );

      default:
        return Center(
          child: Text(
            'نوع مخطط غير مدعوم: ${widget.widget.type}',
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
        );
    }
  }

  /// الحصول على أيقونة العنصر
  IconData _getWidgetIcon() {
    switch (widget.widget.type) {
      case 'taskStatusChart':
        return Icons.pie_chart;
      case 'taskProgressChart':
        return Icons.show_chart;
      case 'taskCounters':
        return Icons.dashboard;
      case 'userPerformanceChart':
        return Icons.bar_chart;
      case 'departmentPerformanceChart':
        return Icons.bar_chart;
      case 'timeTrackingChart':
        return Icons.access_time;
      case 'taskList':
        return Icons.list_alt;
      case 'kpi':
        return Icons.speed;
      default:
        return Icons.bar_chart;
    }
  }

  /// بدء السحب
  void _onPanStart(DragStartDetails details) {
    _dragStartPosition = details.globalPosition;
    // نستخدم الموقع الحالي للعنصر
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    _widgetStartPosition = renderBox.localToGlobal(Offset.zero);
  }

  /// تحديث السحب
  void _onPanUpdate(DragUpdateDetails details) {
    final delta = details.globalPosition - _dragStartPosition;
    final newPosition = _widgetStartPosition + delta;

    // إرسال الموقع الجديد إلى الدالة المسؤولة عن تحريك العنصر
    widget.onMove(newPosition);
  }

  /// انتهاء السحب
  void _onPanEnd(DragEndDetails details) {
    // لا شيء للقيام به هنا
  }
}
