import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import '../../../constants/app_styles.dart';
import '../../../models/dashboard_widget_model.dart';
import '../../../services/dashboard_service.dart';
import '../../../controllers/auth_controller.dart';

/// حوار إضافة عنصر بتصميم Monday.com
///
/// يعرض حوار إضافة عنصر جديد إلى لوحة المعلومات بتصميم مشابه لـ Monday.com
class MondayStyleAddWidgetDialog extends StatefulWidget {
  /// معرف لوحة المعلومات
  final String dashboardId;

  /// العنصر (للتعديل)
  final DashboardWidget? widget;

  /// هل في وضع التعديل
  final bool isEditing;

  const MondayStyleAddWidgetDialog({
    super.key,
    required this.dashboardId,
    this.widget,
    this.isEditing = false,
  });

  @override
  State<MondayStyleAddWidgetDialog> createState() => _MondayStyleAddWidgetDialogState();
}

class _MondayStyleAddWidgetDialogState extends State<MondayStyleAddWidgetDialog> {
  final DashboardService _dashboardService = Get.find<DashboardService>();
  final AuthController _authController = Get.find<AuthController>();

  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  DashboardWidgetType _selectedType = DashboardWidgetType.barChart;
  String _selectedDataSource = '';
  String _selectedQuery = '';
  String? _xAxisField;
  String? _yAxisField;
  String? _labelField;
  String? _valueField;

  List<String> _availableDataSources = [];
  List<String> _availableQueries = [];
  List<String> _availableFields = [];

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // تعبئة البيانات إذا كان في وضع التعديل
    if (widget.isEditing && widget.widget != null) {
      _titleController.text = widget.widget!.title;
      _descriptionController.text = widget.widget!.description ?? '';
      _selectedType = widget.widget!.type;
      _selectedDataSource = widget.widget!.dataSource;
      _selectedQuery = widget.widget!.query;
      _xAxisField = widget.widget!.xAxisField;
      _yAxisField = widget.widget!.yAxisField;
      _labelField = widget.widget!.labelField;
      _valueField = widget.widget!.valueField;
    }

    _loadDataSources();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// تحميل مصادر البيانات
  Future<void> _loadDataSources() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل مصادر البيانات المتاحة (محاكاة)
      final dataSources = await _getAvailableDataSources();

      setState(() {
        _availableDataSources = dataSources;
        _isLoading = false;

        // تحديد مصدر البيانات الافتراضي إذا لم يكن محددًا
        if (_selectedDataSource.isEmpty && _availableDataSources.isNotEmpty) {
          _selectedDataSource = _availableDataSources.first;
          _loadQueries();
        } else if (_selectedDataSource.isNotEmpty) {
          _loadQueries();
        }
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل مصادر البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withAlpha(179),
        colorText: Colors.white,
        margin: const EdgeInsets.all(8),
      );
    }
  }

  /// تحميل الاستعلامات
  Future<void> _loadQueries() async {
    if (_selectedDataSource.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الاستعلامات المتاحة (محاكاة)
      final queries = await _getAvailableQueries(_selectedDataSource);

      setState(() {
        _availableQueries = queries;
        _isLoading = false;

        // تحديد الاستعلام الافتراضي إذا لم يكن محددًا
        if (_selectedQuery.isEmpty && _availableQueries.isNotEmpty) {
          _selectedQuery = _availableQueries.first;
          _loadFields();
        } else if (_selectedQuery.isNotEmpty) {
          _loadFields();
        }
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل الاستعلامات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withAlpha(179),
        colorText: Colors.white,
        margin: const EdgeInsets.all(8),
      );
    }
  }

  /// تحميل الحقول
  Future<void> _loadFields() async {
    if (_selectedDataSource.isEmpty || _selectedQuery.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الحقول المتاحة (محاكاة)
      final fields = await _getAvailableFields(_selectedDataSource, _selectedQuery);

      setState(() {
        _availableFields = fields;
        _isLoading = false;

        // إضافة الحقول الحالية إذا لم تكن موجودة في القائمة
        if (_xAxisField != null && _xAxisField!.isNotEmpty && !_availableFields.contains(_xAxisField)) {
          _availableFields.add(_xAxisField!);
        }

        if (_yAxisField != null && _yAxisField!.isNotEmpty && !_availableFields.contains(_yAxisField)) {
          _availableFields.add(_yAxisField!);
        }

        if (_labelField != null && _labelField!.isNotEmpty && !_availableFields.contains(_labelField)) {
          _availableFields.add(_labelField!);
        }

        if (_valueField != null && _valueField!.isNotEmpty && !_availableFields.contains(_valueField)) {
          _availableFields.add(_valueField!);
        }

        // تحديد الحقول الافتراضية إذا لم تكن محددة
        if (_availableFields.isNotEmpty) {
          if (_xAxisField == null || _xAxisField!.isEmpty) {
            _xAxisField = _availableFields.first;
          }

          if (_yAxisField == null || _yAxisField!.isEmpty) {
            _yAxisField = _availableFields.length > 1 ? _availableFields[1] : _availableFields.first;
          }

          if (_labelField == null || _labelField!.isEmpty) {
            _labelField = _availableFields.first;
          }

          if (_valueField == null || _valueField!.isEmpty) {
            _valueField = _availableFields.length > 1 ? _availableFields[1] : _availableFields.first;
          }
        } else {
          // إذا كانت قائمة الحقول فارغة، إضافة حقول افتراضية
          _availableFields = ['id', 'name', 'value', 'count', 'date'];
          _xAxisField = 'name';
          _yAxisField = 'value';
          _labelField = 'name';
          _valueField = 'value';
        }
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل الحقول: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withAlpha(179),
        colorText: Colors.white,
        margin: const EdgeInsets.all(8),
      );
    }
  }

  /// حفظ العنصر
  void _saveWidget() {
    if (_formKey.currentState!.validate()) {
      // إنشاء العنصر من نموذج dashboard_widget_model.dart
      final newWidget = DashboardWidget(
        id: widget.widget?.id ?? const Uuid().v4(),
        dashboardId: widget.dashboardId,
        title: _titleController.text,
        description: _descriptionController.text.isEmpty ? null : _descriptionController.text,
        type: _selectedType,
        dataSource: _selectedDataSource,
        query: _selectedQuery,
        xAxisField: _xAxisField,
        yAxisField: _yAxisField,
        labelField: _labelField,
        valueField: _valueField,
        position: widget.widget?.position ?? const Offset(0, 0),
        size: widget.widget?.size ?? const Size(400, 300),
        createdAt: widget.widget?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        createdById: widget.widget?.createdById ?? _authController.currentUser.value!.id.toString(),
      );

      // إرجاع العنصر الجديد
      Get.back(result: newWidget);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 600,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الحوار
              Text(
                widget.isEditing ? 'تعديل عنصر' : 'إضافة عنصر جديد',
                style: AppStyles.titleLarge,
              ),

              const SizedBox(height: 24),

              // نموذج إضافة العنصر
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان العنصر
                      TextFormField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: 'العنوان',
                          hintText: 'أدخل عنوان العنصر',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'الرجاء إدخال عنوان العنصر';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // وصف العنصر
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'الوصف (اختياري)',
                          hintText: 'أدخل وصف العنصر',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                      ),

                      const SizedBox(height: 16),

                      // نوع العنصر
                      DropdownButtonFormField<DashboardWidgetType>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع العنصر',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          DropdownMenuItem(
                            value: DashboardWidgetType.barChart,
                            child: Row(
                              children: [
                                const Icon(Icons.bar_chart, size: 16),
                                const SizedBox(width: 8),
                                const Text('مخطط شريطي'),
                              ],
                            ),
                          ),
                          DropdownMenuItem(
                            value: DashboardWidgetType.lineChart,
                            child: Row(
                              children: [
                                const Icon(Icons.show_chart, size: 16),
                                const SizedBox(width: 8),
                                const Text('مخطط خطي'),
                              ],
                            ),
                          ),
                          DropdownMenuItem(
                            value: DashboardWidgetType.pieChart,
                            child: Row(
                              children: [
                                const Icon(Icons.pie_chart, size: 16),
                                const SizedBox(width: 8),
                                const Text('مخطط دائري'),
                              ],
                            ),
                          ),
                          DropdownMenuItem(
                            value: DashboardWidgetType.table,
                            child: Row(
                              children: [
                                const Icon(Icons.table_chart, size: 16),
                                const SizedBox(width: 8),
                                const Text('جدول'),
                              ],
                            ),
                          ),
                          DropdownMenuItem(
                            value: DashboardWidgetType.kpiCard,
                            child: Row(
                              children: [
                                const Icon(Icons.dashboard, size: 16),
                                const SizedBox(width: 8),
                                const Text('بطاقة KPI'),
                              ],
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedType = value;
                            });
                          }
                        },
                      ),

                      const SizedBox(height: 16),

                      // مصدر البيانات
                      DropdownButtonFormField<String>(
                        value: _selectedDataSource.isEmpty ? null : _selectedDataSource,
                        decoration: const InputDecoration(
                          labelText: 'مصدر البيانات',
                          border: OutlineInputBorder(),
                        ),
                        items: _availableDataSources.map((source) {
                          return DropdownMenuItem(
                            value: source,
                            child: Text(source),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedDataSource = value;
                              _selectedQuery = '';
                              _availableQueries = [];
                              _availableFields = [];
                            });
                            _loadQueries();
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'الرجاء اختيار مصدر البيانات';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // الاستعلام
                      DropdownButtonFormField<String>(
                        value: _selectedQuery.isEmpty ? null : _selectedQuery,
                        decoration: const InputDecoration(
                          labelText: 'الاستعلام',
                          border: OutlineInputBorder(),
                        ),
                        items: _availableQueries.map((query) {
                          return DropdownMenuItem(
                            value: query,
                            child: Text(query),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedQuery = value;
                              _availableFields = [];
                            });
                            _loadFields();
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'الرجاء اختيار الاستعلام';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // حقول المخطط
                      if (_availableFields.isNotEmpty) ...[
                        // حقول المخطط حسب النوع
                        if (_selectedType == DashboardWidgetType.barChart ||
                            _selectedType == DashboardWidgetType.lineChart) ...[
                          // حقل المحور السيني
                          DropdownButtonFormField<String>(
                            value: _xAxisField,
                            decoration: const InputDecoration(
                              labelText: 'حقل المحور السيني (X)',
                              border: OutlineInputBorder(),
                            ),
                            items: _availableFields.map((field) {
                              return DropdownMenuItem(
                                value: field,
                                child: Text(field),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _xAxisField = value;
                                });
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء اختيار حقل المحور السيني';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // حقل المحور الصادي
                          DropdownButtonFormField<String>(
                            value: _yAxisField,
                            decoration: const InputDecoration(
                              labelText: 'حقل المحور الصادي (Y)',
                              border: OutlineInputBorder(),
                            ),
                            items: _availableFields.map((field) {
                              return DropdownMenuItem(
                                value: field,
                                child: Text(field),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _yAxisField = value;
                                });
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء اختيار حقل المحور الصادي';
                              }
                              return null;
                            },
                          ),
                        ] else if (_selectedType == DashboardWidgetType.pieChart ||
                                  _selectedType == DashboardWidgetType.kpiCard) ...[
                          // حقل التسمية
                          DropdownButtonFormField<String>(
                            value: _labelField,
                            decoration: const InputDecoration(
                              labelText: 'حقل التسمية',
                              border: OutlineInputBorder(),
                            ),
                            items: _availableFields.map((field) {
                              return DropdownMenuItem(
                                value: field,
                                child: Text(field),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _labelField = value;
                                });
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء اختيار حقل التسمية';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // حقل القيمة
                          DropdownButtonFormField<String>(
                            value: _valueField,
                            decoration: const InputDecoration(
                              labelText: 'حقل القيمة',
                              border: OutlineInputBorder(),
                            ),
                            items: _availableFields.map((field) {
                              return DropdownMenuItem(
                                value: field,
                                child: Text(field),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _valueField = value;
                                });
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء اختيار حقل القيمة';
                              }
                              return null;
                            },
                          ),
                        ],
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _saveWidget,
                    child: Text(widget.isEditing ? 'حفظ التغييرات' : 'إضافة'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// محاكاة تحميل مصادر البيانات المتاحة
  Future<List<String>> _getAvailableDataSources() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return [
      'قاعدة البيانات الرئيسية',
      'قاعدة بيانات المهام',
      'قاعدة بيانات المستخدمين',
      'قاعدة بيانات التقارير',
    ];
  }

  /// محاكاة تحميل الاستعلامات المتاحة
  Future<List<String>> _getAvailableQueries(String dataSource) async {
    await Future.delayed(const Duration(milliseconds: 500));
    switch (dataSource) {
      case 'قاعدة البيانات الرئيسية':
        return ['جميع البيانات', 'البيانات النشطة', 'البيانات المحدثة'];
      case 'قاعدة بيانات المهام':
        return ['جميع المهام', 'المهام المكتملة', 'المهام المعلقة', 'المهام المتأخرة'];
      case 'قاعدة بيانات المستخدمين':
        return ['جميع المستخدمين', 'المستخدمين النشطين', 'أداء المستخدمين'];
      case 'قاعدة بيانات التقارير':
        return ['التقارير الشهرية', 'التقارير الأسبوعية', 'التقارير اليومية'];
      default:
        return ['استعلام افتراضي'];
    }
  }

  /// محاكاة تحميل الحقول المتاحة
  Future<List<String>> _getAvailableFields(String dataSource, String query) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return [
      'id',
      'name',
      'title',
      'value',
      'count',
      'status',
      'date',
      'created_at',
      'updated_at',
      'category',
      'priority',
      'description',
    ];
  }
}
