import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

class InlineEditField extends StatefulWidget {
  final String initialValue;
  final String label;
  final IconData icon;
  final Function(String) onSaved;
  final bool multiline;
  final int? maxLines;
  final int? maxLength;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;

  const InlineEditField({
    super.key,
    required this.initialValue,
    required this.label,
    required this.icon,
    required this.onSaved,
    this.multiline = false,
    this.maxLines,
    this.maxLength,
    this.keyboardType = TextInputType.text,
    this.validator,
  });

  @override
  State<InlineEditField> createState() => _InlineEditFieldState();
}

class _InlineEditFieldState extends State<InlineEditField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _isEditing = false;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    if (!_focusNode.hasFocus && _isEditing) {
      _saveChanges();
    }
  }

  void _saveChanges() {
    final value = _controller.text.trim();

    // Validate the input
    if (widget.validator != null) {
      final error = widget.validator!(value);
      if (error != null) {
        setState(() {
          _errorText = error;
        });
        return;
      }
    }

    setState(() {
      _isEditing = false;
      _errorText = null;
    });

    if (value != widget.initialValue) {
      widget.onSaved(value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              widget.icon,
              size: 16,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: 8),
            Text(
              widget.label,
              style: AppStyles.labelMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        if (_isEditing)
          _buildEditField()
        else
          _buildDisplayField(),
        if (_errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              _errorText!,
              style: AppStyles.labelSmall.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDisplayField() {
    return InkWell(
      onTap: () {
        setState(() {
          _isEditing = true;
        });
        // Schedule focus for the next frame
        Future.delayed(Duration.zero, () {
          _focusNode.requestFocus();
        });
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Get.isDarkMode ? const Color(0xFF2A2A2A) : const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: AppColors.getBorderColor()),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                widget.initialValue.isEmpty ? 'Not set' : widget.initialValue,
                style: widget.initialValue.isEmpty
                    ? AppStyles.bodyMedium.copyWith(
                        color: AppColors.textHint,
                        fontStyle: FontStyle.italic,
                      )
                    : AppStyles.bodyMedium,
                maxLines: widget.multiline ? null : 1,
                overflow: widget.multiline ? null : TextOverflow.ellipsis,
              ),
            ),
            Icon(
              Icons.edit,
              size: 16,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: _errorText != null ? AppColors.error : AppColors.primary,
          width: 1.5,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: TextFormField(
              controller: _controller,
              focusNode: _focusNode,
              maxLines: widget.multiline ? widget.maxLines ?? 5 : 1,
              maxLength: widget.maxLength,
              keyboardType: widget.keyboardType,
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                border: InputBorder.none,
                counterText: '',
              ),
              onFieldSubmitted: (_) => _saveChanges(),
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              IconButton(
                icon: Icon(Icons.check, color: AppColors.success),
                onPressed: _saveChanges,
                constraints: const BoxConstraints(
                  minWidth: 40,
                  minHeight: 40,
                ),
                padding: EdgeInsets.zero,
                iconSize: 20,
              ),
              IconButton(
                icon: Icon(Icons.close, color: AppColors.error),
                onPressed: () {
                  setState(() {
                    _controller.text = widget.initialValue;
                    _isEditing = false;
                    _errorText = null;
                  });
                },
                constraints: const BoxConstraints(
                  minWidth: 40,
                  minHeight: 40,
                ),
                padding: EdgeInsets.zero,
                iconSize: 20,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
