import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/dashboard_models.dart' as dashboard_models;
import '../../controllers/dashboards_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/department_controller.dart';
import '../widgets/app_drawer.dart';
import '../widgets/common/loading_indicator.dart';
import '../widgets/common/empty_state_widget.dart';
import '../../constants/app_styles.dart';
import '../widgets/dashboard/modern_dashboard_widget.dart';

/// شاشة لوحة المعلومات الجديدة
///
/// تعرض لوحة معلومات مخصصة بتصميم حديث وبسيط
class NewDashboardScreen extends StatefulWidget {
  const NewDashboardScreen({super.key});

  @override
  State<NewDashboardScreen> createState() => _NewDashboardScreenState();
}

class _NewDashboardScreenState extends State<NewDashboardScreen> {
  final DashboardsController _dashboardController = Get.find<DashboardsController>();
  final AuthController _authController = Get.find<AuthController>();
  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();
  final DepartmentController _departmentController = Get.find<DepartmentController>();

  bool _isLoading = true;
  String? _errorMessage;
  dashboard_models.Dashboard? _currentDashboard;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _loadDashboards();
  }

  /// تحميل لوحات المعلومات
  Future<void> _loadDashboards() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل البيانات الأساسية
      await _taskController.loadAllTasks();
      await _userController.loadAllUsers();
      await _departmentController.loadAllDepartments();

      // تحميل لوحات المعلومات
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        throw Exception('لم يتم تسجيل الدخول');
      }

      await _dashboardController.loadMyDashboards();
      final dashboards = _dashboardController.myDashboards;

      // البحث عن لوحة المعلومات الافتراضية
      dashboard_models.Dashboard? defaultDashboard = dashboards.firstWhereOrNull((d) => d.isDefault);

      // إذا لم يتم العثور على لوحة معلومات افتراضية، استخدم الأولى أو أنشئ واحدة جديدة
      if (defaultDashboard == null) {
        if (dashboards.isNotEmpty) {
          defaultDashboard = dashboards.first;
        } else {
          // إنشاء لوحة معلومات افتراضية
          defaultDashboard = await _createDefaultDashboard(userId.toString());
        }
      }

      setState(() {
        _currentDashboard = defaultDashboard;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل لوحات المعلومات: $e';
        _isLoading = false;
      });
    }
  }

  /// إنشاء لوحة معلومات افتراضية
  Future<dashboard_models.Dashboard> _createDefaultDashboard(String userId) async {
    final dashboard = dashboard_models.Dashboard(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'لوحة المعلومات الرئيسية',
      description: 'لوحة المعلومات الافتراضية',
      ownerId: int.tryParse(userId) ?? 1,
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      dashboardWidgets: _getDefaultWidgets(),
      isDefault: true,
      gridRows: 12,
      gridColumns: 12,
    );

    await _dashboardController.createDashboard(dashboard);
    return dashboard;
  }

  /// الحصول على العناصر الافتراضية للوحة المعلومات
  List<dashboard_models.DashboardWidget> _getDefaultWidgets() {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return [
      dashboard_models.DashboardWidget(
        id: 1,
        dashboardId: 1,
        title: 'توزيع المهام حسب الحالة',
        type: 'taskStatusChart',
        config: '{"chartType":"pie","showLegend":true,"showValues":true,"showPercentages":true}',
        positionX: 0,
        positionY: 0,
        width: 6,
        height: 4,
        createdAt: now,
      ),
      dashboard_models.DashboardWidget(
        id: 2,
        dashboardId: 1,
        title: 'أداء المستخدمين',
        type: 'userPerformanceChart',
        config: '{"chartType":"bar","showGrid":true,"showValues":true,"maxUsers":5,"sortBy":"completed"}',
        positionX: 6,
        positionY: 0,
        width: 6,
        height: 4,
        createdAt: now,
      ),
      dashboard_models.DashboardWidget(
        id: 3,
        dashboardId: 1,
        title: 'تقدم المهام على مدار الوقت',
        type: 'taskProgressChart',
        config: '{"chartType":"line","showGrid":true,"showDots":true,"showBelowArea":true,"timeRange":"month"}',
        positionX: 0,
        positionY: 4,
        width: 12,
        height: 4,
        createdAt: now,
      ),
      dashboard_models.DashboardWidget(
        id: 4,
        dashboardId: 1,
        title: 'أداء الأقسام',
        type: 'departmentPerformanceChart',
        config: '{"chartType":"bar","showGrid":true,"showValues":true,"sortBy":"completed"}',
        positionX: 0,
        positionY: 8,
        width: 12,
        height: 4,
        createdAt: now,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة المعلومات الجديدة'),
        actions: [
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            onPressed: _loadDashboards,
          ),
          // زر التعديل
          IconButton(
            icon: Icon(_isEditing ? Icons.check : Icons.edit),
            tooltip: _isEditing ? 'حفظ التغييرات' : 'تعديل لوحة المعلومات',
            onPressed: () {
              setState(() {
                _isEditing = !_isEditing;
              });
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      floatingActionButton: _isEditing ? FloatingActionButton(
        onPressed: _showAddWidgetDialog,
        tooltip: 'إضافة عنصر جديد',
        child: const Icon(Icons.add),
      ) : null,
      body: _buildBody(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator(message: 'جاري تحميل لوحة المعلومات...'),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: EmptyStateWidget(
          icon: Icons.error_outline,
          title: 'حدث خطأ',
          message: _errorMessage!,
          buttonText: 'إعادة المحاولة',
          onButtonPressed: _loadDashboards,
        ),
      );
    }

    if (_currentDashboard == null) {
      return Center(
        child: EmptyStateWidget(
          icon: Icons.dashboard_customize,
          title: 'لا توجد لوحة معلومات',
          message: 'لم يتم العثور على لوحة معلومات. يمكنك إنشاء لوحة معلومات جديدة.',
          buttonText: 'إنشاء لوحة معلومات',
          onButtonPressed: () => _createDefaultDashboard(_authController.currentUser.value!.id.toString()),
        ),
      );
    }

    return _buildDashboardContent();
  }

  /// بناء محتوى لوحة المعلومات
  Widget _buildDashboardContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان لوحة المعلومات
          Text(
            _currentDashboard!.title,
            style: AppStyles.headingLarge,
          ),
          if (_currentDashboard!.description != null) ...[
            const SizedBox(height: 8),
            Text(
              _currentDashboard!.description!,
              style: AppStyles.bodyMedium,
            ),
          ],
          const SizedBox(height: 24),

          // عناصر لوحة المعلومات
          _buildDashboardWidgets(),
        ],
      ),
    );
  }

  /// بناء عناصر لوحة المعلومات
  Widget _buildDashboardWidgets() {
    if (_currentDashboard!.dashboardWidgets?.isEmpty ?? true) {
      return Center(
        child: EmptyStateWidget(
          icon: Icons.dashboard_customize,
          title: 'لا توجد عناصر',
          message: 'لوحة المعلومات فارغة. يمكنك إضافة عناصر جديدة.',
          buttonText: 'إضافة عنصر',
          onButtonPressed: _showAddWidgetDialog,
        ),
      );
    }

    // إنشاء شبكة من العناصر
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _currentDashboard!.dashboardWidgets?.length ?? 0,
      itemBuilder: (context, index) {
        final widget = _currentDashboard!.dashboardWidgets![index];
        return ModernDashboardWidget(
          widget: widget,
          isEditing: _isEditing,
          onDelete: () => _deleteWidget(widget),
          onEdit: () => _editWidget(widget),
          onMove: (newPosition) => _moveWidget(widget, newPosition),
          onResize: (newSize) => _resizeWidget(widget, newSize),
        );
      },
    );
  }

  /// حذف عنصر
  void _deleteWidget(dashboard_models.DashboardWidget widget) {
    if (_currentDashboard == null) return;

    // عرض مربع حوار للتأكيد
    Get.dialog(
      AlertDialog(
        title: const Text('حذف العنصر'),
        content: const Text('هل أنت متأكد من حذف هذا العنصر؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();

              // حذف العنصر من لوحة المعلومات
              final updatedWidgets = List<dashboard_models.DashboardWidget>.from(_currentDashboard!.dashboardWidgets ?? [])
                ..removeWhere((w) => w.id == widget.id);

              final updatedDashboard = _currentDashboard!.copyWith(
                dashboardWidgets: updatedWidgets,
                updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
              );

              // حفظ التغييرات
              _dashboardController.updateDashboard(updatedDashboard.id, updatedDashboard);

              // تحديث واجهة المستخدم
              setState(() {
                _currentDashboard = updatedDashboard;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تعديل عنصر
  void _editWidget(dashboard_models.DashboardWidget widget) {
    if (_currentDashboard == null) return;

    // عرض رسالة "قريباً"
    Get.snackbar(
      'قريباً',
      'ميزة تعديل العناصر ستكون متاحة قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// نقل عنصر
  void _moveWidget(dashboard_models.DashboardWidget widget, Offset newPosition) {
    // سيتم تنفيذ هذه الميزة لاحقاً
  }

  /// تغيير حجم عنصر
  void _resizeWidget(dashboard_models.DashboardWidget widget, Size newSize) {
    // سيتم تنفيذ هذه الميزة لاحقاً
  }

  /// عرض مربع حوار إضافة عنصر جديد
  void _showAddWidgetDialog() {
    if (_currentDashboard == null) return;

    Get.dialog(
      AlertDialog(
        title: const Text('إضافة عنصر جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر نوع العنصر:'),
            const SizedBox(height: 16),
            _buildWidgetTypeButton(
              title: 'مخطط حالة المهام',
              icon: Icons.pie_chart,
              color: Colors.blue,
              onTap: () => _createWidget('taskStatusChart'),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'مخطط تقدم المهام',
              icon: Icons.show_chart,
              color: Colors.green,
              onTap: () => _createWidget('taskProgressChart'),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'مخطط أداء المستخدمين',
              icon: Icons.people,
              color: Colors.orange,
              onTap: () => _createWidget('userPerformanceChart'),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'مخطط أداء الأقسام',
              icon: Icons.business,
              color: Colors.purple,
              onTap: () => _createWidget('departmentPerformanceChart'),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'عدادات المهام',
              icon: Icons.numbers,
              color: Colors.teal,
              onTap: () => _createWidget('taskCounters'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// بناء زر نوع العنصر
  Widget _buildWidgetTypeButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: color.withAlpha(128),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[600],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// إنشاء عنصر جديد
  void _createWidget(String type) {
    if (_currentDashboard == null) return;

    // إغلاق مربع الحوار
    Get.back();

    // سيتم تنفيذ إنشاء العناصر لاحقاً

    // عرض رسالة "قريباً"
    Get.snackbar(
      'قريباً',
      'ميزة إضافة العناصر ستكون متاحة قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
