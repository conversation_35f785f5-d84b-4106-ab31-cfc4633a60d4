# سكريبت PowerShell لتشغيل سكريبت SQL وإنشاء المستخدم الافتراضي

param(
    [string]$ServerInstance = ".\sqlexpress",
    [string]$Database = "databasetasks",
    [string]$SqlFile = "create_admin_user.sql"
)

Write-Host "=== إنشاء المستخدم الافتراضي ===" -ForegroundColor Green
Write-Host "الخادم: $ServerInstance" -ForegroundColor Yellow
Write-Host "قاعدة البيانات: $Database" -ForegroundColor Yellow
Write-Host "ملف SQL: $SqlFile" -ForegroundColor Yellow
Write-Host ""

try {
    # التحقق من وجود ملف SQL
    if (-not (Test-Path $SqlFile)) {
        Write-Host "خطأ: ملف SQL غير موجود: $SqlFile" -ForegroundColor Red
        exit 1
    }

    # التحقق من وجود sqlcmd
    $sqlcmdPath = Get-Command sqlcmd -ErrorAction SilentlyContinue
    if (-not $sqlcmdPath) {
        Write-Host "خطأ: sqlcmd غير موجود. يرجى تثبيت SQL Server Command Line Utilities" -ForegroundColor Red
        exit 1
    }

    Write-Host "تشغيل سكريبت SQL..." -ForegroundColor Cyan
    
    # تشغيل سكريبت SQL
    $result = sqlcmd -S $ServerInstance -d $Database -i $SqlFile -E
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "تم تشغيل السكريبت بنجاح!" -ForegroundColor Green
        Write-Host ""
        Write-Host "=== بيانات تسجيل الدخول ===" -ForegroundColor Green
        Write-Host "البريد الإلكتروني: <EMAIL>" -ForegroundColor White
        Write-Host "كلمة المرور: admin123" -ForegroundColor White
        Write-Host "الدور: SuperAdmin" -ForegroundColor White
        Write-Host ""
        Write-Host "يمكنك الآن تسجيل الدخول باستخدام هذه البيانات" -ForegroundColor Green
    } else {
        Write-Host "فشل في تشغيل السكريبت" -ForegroundColor Red
        Write-Host "رمز الخطأ: $LASTEXITCODE" -ForegroundColor Red
    }
    
    # عرض النتيجة
    if ($result) {
        Write-Host ""
        Write-Host "=== نتيجة السكريبت ===" -ForegroundColor Cyan
        Write-Host $result
    }
    
} catch {
    Write-Host "خطأ في تشغيل السكريبت: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "انتهى السكريبت" -ForegroundColor Green
