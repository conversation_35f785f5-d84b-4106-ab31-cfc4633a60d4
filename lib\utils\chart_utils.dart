import 'package:flutter/material.dart';
import '../models/chart_enums.dart';

/// أدوات مساعدة للمخططات - متوافق مع ASP.NET Core API
class ChartTypeUtils {
  /// الحصول على أيقونة نوع المخطط
  static IconData getChartTypeIcon(ChartType type) {
    switch (type) {
      case ChartType.pie:
        return Icons.pie_chart;
      case ChartType.donut:
        return Icons.donut_large;
      case ChartType.bar:
        return Icons.bar_chart;
      case ChartType.line:
        return Icons.show_chart;
      case ChartType.area:
        return Icons.area_chart;
      case ChartType.scatter:
        return Icons.scatter_plot;
      case ChartType.bubble:
        return Icons.bubble_chart;
      case ChartType.radar:
        return Icons.radar;
      case ChartType.gauge:
        return Icons.speed;
      case ChartType.funnel:
        return Icons.filter_list;
      case ChartType.treemap:
        return Icons.account_tree;
      case ChartType.heatmap:
        return Icons.grid_view;
      case ChartType.gantt:
        return Icons.timeline;
      case ChartType.table:
        return Icons.table_chart;
      case ChartType.waterfall:
        return Icons.waterfall_chart;
      case ChartType.candlestick:
        return Icons.candlestick_chart;
      case ChartType.boxplot:
        return Icons.view_module;
      case ChartType.network:
        return Icons.hub;
      case ChartType.sankey:
        return Icons.device_hub;
    }
  }

  /// الحصول على تسمية نوع المخطط
  static String getChartTypeLabel(ChartType type) {
    switch (type) {
      case ChartType.pie:
        return 'مخطط دائري';
      case ChartType.donut:
        return 'مخطط حلقي';
      case ChartType.bar:
        return 'مخطط شريطي';
      case ChartType.line:
        return 'مخطط خطي';
      case ChartType.area:
        return 'مخطط مساحي';
      case ChartType.scatter:
        return 'مخطط انتشاري';
      case ChartType.bubble:
        return 'مخطط فقاعي';
      case ChartType.radar:
        return 'مخطط راداري';
      case ChartType.gauge:
        return 'مخطط مقياس';
      case ChartType.funnel:
        return 'مخطط قمعي';
      case ChartType.treemap:
        return 'خريطة شجرية';
      case ChartType.heatmap:
        return 'خريطة حرارية';
      case ChartType.gantt:
        return 'مخطط جانت';
      case ChartType.table:
        return 'جدول';
      case ChartType.waterfall:
        return 'مخطط شلال';
      case ChartType.candlestick:
        return 'مخطط شموع';
      case ChartType.boxplot:
        return 'مخطط صندوقي';
      case ChartType.network:
        return 'مخطط العلاقات';
      case ChartType.sankey:
        return 'مخطط سانكي';
    }
  }

  /// التحويل من قيمة نصية إلى نوع المخطط (للتوافق مع الباك اند)
  static ChartType fromString(String value) {
    return ChartType.fromValue(value);
  }

  /// التحويل من نوع المخطط إلى قيمة نصية (للتوافق مع الباك اند)
  static String toValue(ChartType type) {
    return type.value;
  }

  /// التحقق من دعم نوع المخطط
  static bool isSupported(ChartType type) {
    return ChartType.values.contains(type);
  }

  /// الحصول على جميع أنواع المخططات المدعومة
  static List<ChartType> getSupportedTypes() {
    return ChartType.values;
  }

  /// الحصول على أنواع المخططات المناسبة للبيانات الرقمية
  static List<ChartType> getNumericChartTypes() {
    return [
      ChartType.bar,
      ChartType.line,
      ChartType.area,
      ChartType.scatter,
      ChartType.bubble,
      ChartType.gauge,
      ChartType.waterfall,
      ChartType.candlestick,
      ChartType.boxplot,
    ];
  }

  /// الحصول على أنواع المخططات المناسبة للبيانات الفئوية
  static List<ChartType> getCategoricalChartTypes() {
    return [
      ChartType.pie,
      ChartType.donut,
      ChartType.bar,
      ChartType.treemap,
      ChartType.funnel,
    ];
  }

  /// الحصول على أنواع المخططات المناسبة للبيانات الزمنية
  static List<ChartType> getTimeSeriesChartTypes() {
    return [
      ChartType.line,
      ChartType.area,
      ChartType.gantt,
      ChartType.candlestick,
    ];
  }

  /// الحصول على أنواع المخططات المناسبة للعلاقات والشبكات
  static List<ChartType> getRelationalChartTypes() {
    return [
      ChartType.network,
      ChartType.sankey,
      ChartType.heatmap,
    ];
  }
}

/// مخططات الألوان للمخططات - متوافق مع Power BI والباك اند
class ChartColorSchemes {
  /// الحصول على مخطط الألوان
  static List<Color> getColorScheme(String schemeName) {
    switch (schemeName) {
      case 'default':
        return _defaultColors;
      case 'pastel':
        return _pastelColors;
      case 'bright':
        return _brightColors;
      case 'dark':
        return _darkColors;
      case 'monochrome_blue':
        return _monochromeBlue;
      case 'monochrome_green':
        return _monochromeGreen;
      case 'powerbi_default':
        return _powerBIDefault;
      case 'powerbi_corporate':
        return _powerBICorporate;
      case 'powerbi_modern':
        return _powerBIModern;
      case 'gradient_blue':
        return _gradientBlue;
      case 'gradient_green':
        return _gradientGreen;
      case 'gradient_orange':
        return _gradientOrange;
      default:
        return _defaultColors;
    }
  }

  /// الألوان الافتراضية
  static const List<Color> _defaultColors = [
    Color(0xFF2196F3), // أزرق
    Color(0xFF4CAF50), // أخضر
    Color(0xFFFF9800), // برتقالي
    Color(0xFFF44336), // أحمر
    Color(0xFF9C27B0), // بنفسجي
    Color(0xFF00BCD4), // سماوي
    Color(0xFFFFEB3B), // أصفر
    Color(0xFF795548), // بني
    Color(0xFF607D8B), // رمادي مزرق
    Color(0xFFE91E63), // وردي
  ];

  /// ألوان هادئة
  static const List<Color> _pastelColors = [
    Color(0xFFBBDEFB), // أزرق فاتح
    Color(0xFFC8E6C9), // أخضر فاتح
    Color(0xFFFFE0B2), // برتقالي فاتح
    Color(0xFFFFCDD2), // أحمر فاتح
    Color(0xFFE1BEE7), // بنفسجي فاتح
    Color(0xFFB2EBF2), // سماوي فاتح
    Color(0xFFFFF9C4), // أصفر فاتح
    Color(0xFFD7CCC8), // بني فاتح
    Color(0xFFCFD8DC), // رمادي فاتح
    Color(0xFFF8BBD9), // وردي فاتح
  ];

  /// ألوان زاهية
  static const List<Color> _brightColors = [
    Color(0xFF0D47A1), // أزرق داكن
    Color(0xFF1B5E20), // أخضر داكن
    Color(0xFFE65100), // برتقالي داكن
    Color(0xFFB71C1C), // أحمر داكن
    Color(0xFF4A148C), // بنفسجي داكن
    Color(0xFF006064), // سماوي داكن
    Color(0xFFF57F17), // أصفر داكن
    Color(0xFF3E2723), // بني داكن
    Color(0xFF263238), // رمادي داكن
    Color(0xFF880E4F), // وردي داكن
  ];

  /// ألوان داكنة
  static const List<Color> _darkColors = [
    Color(0xFF1A237E),
    Color(0xFF0D5302),
    Color(0xFFBF360C),
    Color(0xFF4E342E),
    Color(0xFF212121),
    Color(0xFF37474F),
    Color(0xFF1B5E20),
    Color(0xFF4A148C),
    Color(0xFF006064),
    Color(0xFF880E4F),
  ];

  /// تدرجات زرقاء
  static const List<Color> _monochromeBlue = [
    Color(0xFF0D47A1),
    Color(0xFF1565C0),
    Color(0xFF1976D2),
    Color(0xFF1E88E5),
    Color(0xFF2196F3),
    Color(0xFF42A5F5),
    Color(0xFF64B5F6),
    Color(0xFF90CAF9),
    Color(0xFFBBDEFB),
    Color(0xFFE3F2FD),
  ];

  /// تدرجات خضراء
  static const List<Color> _monochromeGreen = [
    Color(0xFF1B5E20),
    Color(0xFF2E7D32),
    Color(0xFF388E3C),
    Color(0xFF43A047),
    Color(0xFF4CAF50),
    Color(0xFF66BB6A),
    Color(0xFF81C784),
    Color(0xFFA5D6A7),
    Color(0xFFC8E6C9),
    Color(0xFFE8F5E8),
  ];

  /// ألوان Power BI الافتراضية
  static const List<Color> _powerBIDefault = [
    Color(0xFF118DFF), // أزرق Power BI
    Color(0xFFFF6900), // برتقالي
    Color(0xFF12B347), // أخضر
    Color(0xFFE74856), // أحمر
    Color(0xFF744DA9), // بنفسجي
    Color(0xFF00BCF2), // سماوي
    Color(0xFFFFC72C), // أصفر
    Color(0xFF8764B8), // بنفسجي فاتح
    Color(0xFF00B7C3), // تركوازي
    Color(0xFFEA4300), // أحمر برتقالي
  ];

  /// ألوان Power BI للشركات
  static const List<Color> _powerBICorporate = [
    Color(0xFF003F5C), // أزرق داكن
    Color(0xFF2F4B7C), // أزرق متوسط
    Color(0xFF665191), // بنفسجي
    Color(0xFFA05195), // وردي
    Color(0xFFD45087), // وردي فاتح
    Color(0xFFFF6B6B), // أحمر فاتح
    Color(0xFFFFB347), // برتقالي
    Color(0xFFFFE66D), // أصفر
    Color(0xFF4ECDC4), // تركوازي
    Color(0xFF45B7D1), // أزرق فاتح
  ];

  /// ألوان Power BI الحديثة
  static const List<Color> _powerBIModern = [
    Color(0xFF0078D4), // أزرق Microsoft
    Color(0xFF00BCF2), // سماوي
    Color(0xFF40E0D0), // تركوازي
    Color(0xFF32CD32), // أخضر فاتح
    Color(0xFFFFD700), // ذهبي
    Color(0xFFFF8C00), // برتقالي داكن
    Color(0xFFFF1493), // وردي عميق
    Color(0xFF9370DB), // بنفسجي متوسط
    Color(0xFF20B2AA), // أخضر بحري فاتح
    Color(0xFF87CEEB), // أزرق سماوي
  ];

  /// تدرجات زرقاء متقدمة
  static const List<Color> _gradientBlue = [
    Color(0xFF001F3F), // أزرق داكن جداً
    Color(0xFF003366), // أزرق داكن
    Color(0xFF004080), // أزرق متوسط داكن
    Color(0xFF0066CC), // أزرق متوسط
    Color(0xFF3399FF), // أزرق فاتح
    Color(0xFF66B2FF), // أزرق فاتح جداً
    Color(0xFF99CCFF), // أزرق باهت
    Color(0xFFCCE5FF), // أزرق شاحب
    Color(0xFFE6F2FF), // أزرق شاحب جداً
    Color(0xFFF0F8FF), // أزرق أبيض
  ];

  /// تدرجات خضراء متقدمة
  static const List<Color> _gradientGreen = [
    Color(0xFF003300), // أخضر داكن جداً
    Color(0xFF006600), // أخضر داكن
    Color(0xFF009900), // أخضر متوسط داكن
    Color(0xFF00CC00), // أخضر متوسط
    Color(0xFF33FF33), // أخضر فاتح
    Color(0xFF66FF66), // أخضر فاتح جداً
    Color(0xFF99FF99), // أخضر باهت
    Color(0xFFCCFFCC), // أخضر شاحب
    Color(0xFFE6FFE6), // أخضر شاحب جداً
    Color(0xFFF0FFF0), // أخضر أبيض
  ];

  /// تدرجات برتقالية متقدمة
  static const List<Color> _gradientOrange = [
    Color(0xFF330000), // برتقالي داكن جداً
    Color(0xFF663300), // برتقالي داكن
    Color(0xFF996600), // برتقالي متوسط داكن
    Color(0xFFCC9900), // برتقالي متوسط
    Color(0xFFFFCC33), // برتقالي فاتح
    Color(0xFFFFDD66), // برتقالي فاتح جداً
    Color(0xFFFFEE99), // برتقالي باهت
    Color(0xFFFFF5CC), // برتقالي شاحب
    Color(0xFFFFFAE6), // برتقالي شاحب جداً
    Color(0xFFFFFFF0), // برتقالي أبيض
  ];

  /// الحصول على قائمة بأسماء مخططات الألوان
  static List<String> get availableSchemes => [
        'default',
        'pastel',
        'bright',
        'dark',
        'monochrome_blue',
        'monochrome_green',
        'powerbi_default',
        'powerbi_corporate',
        'powerbi_modern',
        'gradient_blue',
        'gradient_green',
        'gradient_orange',
      ];

  /// الحصول على اسم مخطط الألوان المعروض
  static String getSchemeDisplayName(String schemeName) {
    switch (schemeName) {
      case 'default':
        return 'الألوان الافتراضية';
      case 'pastel':
        return 'ألوان هادئة';
      case 'bright':
        return 'ألوان زاهية';
      case 'dark':
        return 'ألوان داكنة';
      case 'monochrome_blue':
        return 'تدرجات زرقاء';
      case 'monochrome_green':
        return 'تدرجات خضراء';
      case 'powerbi_default':
        return 'ألوان Power BI الافتراضية';
      case 'powerbi_corporate':
        return 'ألوان Power BI للشركات';
      case 'powerbi_modern':
        return 'ألوان Power BI الحديثة';
      case 'gradient_blue':
        return 'تدرجات زرقاء متقدمة';
      case 'gradient_green':
        return 'تدرجات خضراء متقدمة';
      case 'gradient_orange':
        return 'تدرجات برتقالية متقدمة';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على مخطط ألوان مناسب لنوع المخطط
  static String getRecommendedScheme(ChartType chartType) {
    switch (chartType) {
      case ChartType.pie:
      case ChartType.donut:
        return 'powerbi_default';
      case ChartType.bar:
      case ChartType.line:
        return 'gradient_blue';
      case ChartType.area:
        return 'gradient_green';
      case ChartType.heatmap:
        return 'gradient_orange';
      case ChartType.gauge:
        return 'powerbi_modern';
      case ChartType.network:
      case ChartType.sankey:
        return 'powerbi_corporate';
      default:
        return 'default';
    }
  }

  /// التحقق من توافق مخطط الألوان مع نوع المخطط
  static bool isSchemeCompatible(String schemeName, ChartType chartType) {
    // جميع مخططات الألوان متوافقة مع جميع أنواع المخططات
    // يمكن إضافة منطق أكثر تعقيداً هنا حسب الحاجة
    return availableSchemes.contains(schemeName);
  }

  /// الحصول على لون أساسي من مخطط الألوان
  static Color getPrimaryColor(String schemeName) {
    final colors = getColorScheme(schemeName);
    return colors.isNotEmpty ? colors.first : const Color(0xFF2196F3);
  }

  /// الحصول على لون ثانوي من مخطط الألوان
  static Color getSecondaryColor(String schemeName) {
    final colors = getColorScheme(schemeName);
    return colors.length > 1 ? colors[1] : const Color(0xFF4CAF50);
  }
}
