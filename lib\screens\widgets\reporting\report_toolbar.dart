import 'package:flutter/material.dart';

// Unused import removed:
// import '../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

/// شريط أدوات التقرير
///
/// يعرض أزرار الإجراءات المتاحة للتقرير مثل التصدير والمشاركة والطباعة
class ReportToolbar extends StatelessWidget {
  /// حدث تصدير PDF
  final VoidCallback onExportPdf;

  /// حدث تصدير Excel
  final VoidCallback onExportExcel;

  /// حدث تصدير CSV
  final VoidCallback onExportCsv;

  /// حدث المشاركة
  final VoidCallback onShare;

  /// حدث الطباعة
  final VoidCallback onPrint;

  /// حدث إضافة إلى لوحة المعلومات (اختياري)
  final VoidCallback? onAddToDashboard;

  /// حدث جدولة التقرير (اختياري)
  final VoidCallback? onSchedule;

  const ReportToolbar({
    Key? key,
    required this.onExportPdf,
    required this.onExportExcel,
    required this.onExportCsv,
    required this.onShare,
    required this.onPrint,
    this.onAddToDashboard,
    this.onSchedule,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 26), // 0.1 * 255 = 26
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withValues(alpha: 51), // 0.2 * 255 = 51
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // زر تصدير
          _buildExportButton(),

          const SizedBox(width: 8),

          // زر مشاركة
          _buildToolbarButton(
            icon: Icons.share,
            label: 'مشاركة',
            onPressed: onShare,
          ),

          const SizedBox(width: 8),

          // زر طباعة
          _buildToolbarButton(
            icon: Icons.print,
            label: 'طباعة',
            onPressed: onPrint,
          ),

          if (onAddToDashboard != null) ...[
            const SizedBox(width: 8),

            // زر إضافة إلى لوحة المعلومات
            _buildToolbarButton(
              icon: Icons.dashboard_customize,
              label: 'إضافة إلى لوحة المعلومات',
              onPressed: onAddToDashboard!,
            ),
          ],

          if (onSchedule != null) ...[
            const SizedBox(width: 8),

            // زر جدولة التقرير
            _buildToolbarButton(
              icon: Icons.schedule,
              label: 'جدولة التقرير',
              onPressed: onSchedule!,
            ),
          ],

          const Spacer(),

          // نص آخر تحديث
          Text(
            'آخر تحديث: ${DateTime.now().hour}:${DateTime.now().minute}',
            style: AppStyles.captionSmall.copyWith(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// بناء زر التصدير
  Widget _buildExportButton() {
    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'pdf':
            onExportPdf();
            break;
          case 'excel':
            onExportExcel();
            break;
          case 'csv':
            onExportCsv();
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem<String>(
          value: 'pdf',
          child: Row(
            children: [
              Icon(Icons.picture_as_pdf, color: Colors.red),
              SizedBox(width: 8),
              Text('تصدير PDF'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'excel',
          child: Row(
            children: [
              Icon(Icons.table_chart, color: Colors.green),
              SizedBox(width: 8),
              Text('تصدير Excel'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'csv',
          child: Row(
            children: [
              Icon(Icons.description, color: Colors.blue),
              SizedBox(width: 8),
              Text('تصدير CSV'),
            ],
          ),
        ),
      ],
      child: _buildToolbarButton(
        icon: Icons.download,
        label: 'تصدير',
        onPressed: null,
        showArrow: true,
      ),
    );
  }

  /// بناء زر شريط الأدوات
  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    bool showArrow = false,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Row(
        children: [
          Text(label),
          if (showArrow) ...[
            const SizedBox(width: 4),
            const Icon(Icons.arrow_drop_down, size: 16),
          ],
        ],
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
          side: BorderSide(color: Colors.grey.withValues(alpha: 77)), // 0.3 * 255 = 77
        ),
      ),
    );
  }
}
