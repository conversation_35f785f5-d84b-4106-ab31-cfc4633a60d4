import 'dart:io' if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:get/get.dart';

import '../../models/attachment_models.dart';
import '../../utils/file_processor.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

/// مكون معاينة المرفق
/// يعرض معاينة للمرفق مع إمكانية العرض والتنزيل والحذف
class AttachmentPreview extends StatelessWidget {
  /// المرفق المراد عرضه
  final Attachment attachment;

  /// دالة يتم استدعاؤها عند عرض المرفق
  final Function(Attachment)? onView;

  /// دالة يتم استدعاؤها عند فتح المرفق
  final Function(Attachment)? onOpen;

  /// دالة يتم استدعاؤها عند تنزيل المرفق
  final Function(Attachment)? onDownload;

  /// دالة يتم استدعاؤها عند حذف المرفق
  final Function(Attachment)? onDelete;

  /// هل يمكن حذف المرفق
  final bool canDelete;

  /// إنشاء مكون معاينة المرفق
  const AttachmentPreview({
    super.key,
    required this.attachment,
    this.onView,
    this.onOpen,
    this.onDownload,
    this.onDelete,
    this.canDelete = false,
  });

  @override
  Widget build(BuildContext context) {
    final fileType = FileProcessor.getFileType(attachment.filePath);
    final fileIcon = FileProcessor.getFileIcon(attachment.filePath);
    final fileSize = FileProcessor.formatFileSize(attachment.fileSize);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => onView?.call(attachment),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معاينة الملف
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: _buildFilePreview(fileType, fileIcon),
              ),
            ),

            // معلومات الملف
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم الملف
                    Text(
                      attachment.fileName,
                      style: AppStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // حجم الملف
                    Text(
                      fileSize,
                      style: AppStyles.bodySmall.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const Spacer(),

                    // أزرار الإجراءات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // زر العرض
                        if (onView != null)
                          _buildActionButton(
                            icon: Icons.visibility,
                            tooltip: 'عرض'.tr,
                            onPressed: () => onView?.call(attachment),
                          ),

                        // زر الفتح
                        if (onOpen != null)
                          _buildActionButton(
                            icon: Icons.open_in_new,
                            tooltip: 'فتح'.tr,
                            onPressed: () => onOpen?.call(attachment),
                          ),

                        // زر التنزيل
                        if (onDownload != null)
                          _buildActionButton(
                            icon: Icons.download,
                            tooltip: 'تنزيل'.tr,
                            onPressed: () => onDownload?.call(attachment),
                          ),

                        // زر الحذف
                        if (canDelete && onDelete != null)
                          _buildActionButton(
                            icon: Icons.delete,
                            tooltip: 'حذف'.tr,
                            color: Colors.red,
                            onPressed: () => onDelete?.call(attachment),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معاينة الملف
  Widget _buildFilePreview(String fileType, IconData fileIcon) {
    switch (fileType) {
      case 'image':
        return _buildImagePreview();
      default:
        return _buildGenericFilePreview(fileIcon, fileType);
    }
  }

  /// بناء معاينة الصورة
  Widget _buildImagePreview() {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(12),
        topRight: Radius.circular(12),
      ),
      child: kIsWeb
          ? Image.network(
              attachment.filePath,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return _buildGenericFilePreview(Icons.image, 'image');
              },
            )
          : Image.file(
              File(attachment.filePath),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return _buildGenericFilePreview(Icons.image, 'image');
              },
            ),
    );
  }

  /// بناء معاينة الملف العام
  Widget _buildGenericFilePreview(IconData fileIcon, String fileType) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            fileIcon,
            size: 48,
            color: _getFileTypeColor(fileType),
          ),
          const SizedBox(height: 8),
          Text(
            _getFileTypeDisplay(fileType),
            style: AppStyles.bodySmall.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return IconButton(
      icon: Icon(icon, size: 20),
      tooltip: tooltip,
      onPressed: onPressed,
      color: color ?? AppColors.primary,
      constraints: const BoxConstraints(
        minWidth: 32,
        minHeight: 32,
      ),
      padding: EdgeInsets.zero,
    );
  }

  /// الحصول على لون نوع الملف
  Color _getFileTypeColor(String fileType) {
    switch (fileType) {
      case 'image':
        return Colors.green;
      case 'pdf':
        return Colors.red;
      case 'word':
        return Colors.blue;
      case 'spreadsheet':
        return Colors.green.shade700;
      case 'presentation':
        return Colors.orange;
      case 'video':
        return Colors.red.shade700;
      case 'audio':
        return Colors.purple;
      case 'archive':
        return Colors.brown;
      case 'code':
        return Colors.indigo;
      default:
        return Colors.grey.shade700;
    }
  }

  /// الحصول على اسم نوع الملف للعرض
  String _getFileTypeDisplay(String fileType) {
    switch (fileType) {
      case 'image':
        return 'صورة';
      case 'document':
        return 'مستند';
      case 'video':
        return 'فيديو';
      case 'audio':
        return 'ملف صوتي';
      case 'archive':
        return 'ملف مضغوط';
      default:
        return 'ملف';
    }
  }
}
