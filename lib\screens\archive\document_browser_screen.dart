import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/archive_documents_controller.dart';
import '../../controllers/archive_categories_controller.dart';
import '../../controllers/archive_tags_controller.dart';
import '../../constants/app_styles.dart';
import '../../utils/responsive_helper.dart';
import '../widgets/common/loading_indicator.dart';
import 'widgets/document_list_item.dart';
import 'widgets/document_grid_item.dart';
import 'widgets/document_search_bar.dart';
import 'document_detail_screen.dart';

/// شاشة تصفح الوثائق
class DocumentBrowserScreen extends StatefulWidget {
  final int? initialDocumentId;
  final int? categoryId;
  final List<int>? tagIds;

  const DocumentBrowserScreen({
    super.key,
    this.initialDocumentId,
    this.categoryId,
    this.tagIds,
  });

  @override
  State<DocumentBrowserScreen> createState() => _DocumentBrowserScreenState();
}

class _DocumentBrowserScreenState extends State<DocumentBrowserScreen> {
  final ArchiveDocumentsController _documentsController = Get.find<ArchiveDocumentsController>();
  final ArchiveCategoriesController _categoriesController = Get.find<ArchiveCategoriesController>();
  final ArchiveTagsController _tagsController = Get.find<ArchiveTagsController>();

  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool _isGridView = false;
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    // تحميل البيانات الأساسية
    await Future.wait([
      _documentsController.loadAllDocuments(),
      _categoriesController.loadAllCategories(),
      _tagsController.loadAllTags(),
    ]);

    // تطبيق المرشحات الأولية
    if (widget.categoryId != null) {
      _documentsController.setCategoryFilter(widget.categoryId!);
    }

    // الانتقال إلى وثيقة محددة إذا تم تمريرها
    if (widget.initialDocumentId != null) {
      _navigateToDocument(widget.initialDocumentId!);
    }
  }

  void _onScroll() {
    // يمكن إضافة تحميل المزيد هنا إذا لزم الأمر
    // حالياً لا يوجد دعم للتحميل التدريجي في الـ controller
  }

  void _navigateToDocument(int documentId) {
    final document = _documentsController.filteredDocuments.firstWhereOrNull(
      (doc) => doc.id == documentId,
    );
    if (document != null) {
      Get.to(() => DocumentDetailScreen(document: document));
    }
  }

  void _onSearchChanged(String query) {
    _documentsController.setSearchQuery(query);
  }

  void _toggleView() {
    setState(() {
      _isGridView = !_isGridView;
    });
  }

  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تصفح الوثائق'),
        actions: [
          // زر تبديل العرض
          IconButton(
            icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
            onPressed: _toggleView,
            tooltip: _isGridView ? 'عرض قائمة' : 'عرض شبكة',
          ),
          // زر المرشحات
          IconButton(
            icon: Icon(_showFilters ? Icons.filter_list_off : Icons.filter_list),
            onPressed: _toggleFilters,
            tooltip: _showFilters ? 'إخفاء المرشحات' : 'إظهار المرشحات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: DocumentSearchBar(
              onSearch: _onSearchChanged,
              onFilterTap: _showFilters ? null : _toggleFilters,
            ),
          ),

          // المحتوى الرئيسي
          Expanded(
            child: _buildDocumentsList(),
          ),
        ],
      ),


    );
  }

  Widget _buildDocumentsList() {
    return Obx(() {
      if (_documentsController.isLoading && _documentsController.filteredDocuments.isEmpty) {
        return const Center(child: LoadingIndicator());
      }

      if (_documentsController.filteredDocuments.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.description_outlined,
                size: 64,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد وثائق',
                style: AppStyles.titleMedium.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'لم يتم العثور على أي وثائق تطابق المعايير المحددة',
                style: AppStyles.bodyMedium.copyWith(
                  color: Colors.grey.shade500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: () => _documentsController.loadAllDocuments(),
        child: _isGridView ? _buildGridView() : _buildListView(),
      );
    });
  }

  Widget _buildListView() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16.0),
      itemCount: _documentsController.filteredDocuments.length,
      itemBuilder: (context, index) {
        final document = _documentsController.filteredDocuments[index];
        return DocumentListItem(
          document: document,
          onTap: () => Get.to(() => DocumentDetailScreen(document: document)),
        );
      },
    );
  }

  Widget _buildGridView() {
    return GridView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16.0),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: ResponsiveHelper.isSmallScreen(context) ? 2 : 4,
        crossAxisSpacing: 16.0,
        mainAxisSpacing: 16.0,
        childAspectRatio: 0.8,
      ),
      itemCount: _documentsController.filteredDocuments.length,
      itemBuilder: (context, index) {
        final document = _documentsController.filteredDocuments[index];
        return DocumentGridItem(
          document: document,
          onTap: () => Get.to(() => DocumentDetailScreen(document: document)),
        );
      },
    );
  }
}
