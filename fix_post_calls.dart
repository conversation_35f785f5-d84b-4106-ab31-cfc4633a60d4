import 'dart:io';

void main() async {
  // قائمة الملفات التي تحتاج إصلاح
  final files = [
    'lib/services/api/archive_tags_api_service.dart',
    'lib/services/api/attachments_api_service.dart',
    'lib/services/api/project_api_service.dart',
    'lib/services/api/reports_api_service.dart',
    'lib/services/api/report_schedules_api_service.dart',
    'lib/services/api/role_api_service.dart',
    'lib/services/api/subtasks_api_service.dart',
    'lib/services/api/time_tracking_api_service.dart',
    'lib/services/api/user_permissions_api_service.dart',
    'lib/services/api/calendar_events_api_service.dart',
    'lib/services/api/chat_groups_api_service.dart',
    'lib/services/api/departments_api_service.dart',
    'lib/services/api/department_api_service.dart',
    'lib/services/api/messages_api_service.dart',
    'lib/services/api/notifications_api_service.dart',
    'lib/services/api/notification_settings_api_service.dart',
    'lib/services/api/permissions_api_service.dart',
    'lib/services/api/seed_data_api_service.dart',
    'lib/services/api/task_comments_api_service.dart',
    'lib/services/api/task_progress_trackers_api_service.dart',
    'lib/services/api/text_document_api_service.dart',
    'lib/services/api/users_api_service.dart',
    'lib/services/api/user_api_service.dart',
    'lib/services/api/task_api_service.dart',
    'lib/services/api/group_members_api_service.dart',
  ];

  for (final filePath in files) {
    await fixPostCalls(filePath);
  }
  
  print('تم إصلاح جميع استدعاءات POST');
}

Future<void> fixPostCalls(String filePath) async {
  final file = File(filePath);
  if (!await file.exists()) {
    print('الملف غير موجود: $filePath');
    return;
  }

  String content = await file.readAsString();
  
  // إصلاح استدعاءات POST مع body
  content = content.replaceAll('body: ', '');

  // إصلاح استدعاءات POST بدون معاملات
  content = content.replaceAll(
    RegExp(r"_apiService\.post\('([^']+)'\)"),
    r"_apiService.post('$1', {})",
  );

  // إصلاح استدعاءات PUT بدون معاملات
  content = content.replaceAll(
    RegExp(r"_apiService\.put\('([^']+)'\)"),
    r"_apiService.put('$1', {})",
  );

  await file.writeAsString(content);
  print('تم إصلاح: $filePath');
}
