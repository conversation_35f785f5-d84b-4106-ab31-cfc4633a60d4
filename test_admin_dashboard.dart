import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'lib/helpers/user_helper.dart';
import 'lib/controllers/admin_controller.dart';
import 'lib/controllers/auth_controller.dart';
import 'lib/services/storage_service.dart';
import 'lib/services/api/api_service.dart';

/// ملف اختبار سريع للتأكد من أن لوحة التحكم الإدارية تعمل بشكل صحيح
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 بدء اختبار لوحة التحكم الإدارية...');
  
  try {
    // تهيئة الخدمات
    print('📦 تهيئة الخدمات...');
    
    // تهيئة StorageService
    final storageService = await StorageService.init();
    Get.put(storageService, permanent: true);
    print('✅ StorageService تم تهيئته');
    
    // تهيئة ApiService
    final apiService = ApiService();
    await apiService.initialize();
    Get.put(apiService, permanent: true);
    print('✅ ApiService تم تهيئته');
    
    // تهيئة AuthController
    final authController = AuthController();
    Get.put(authController, permanent: true);
    print('✅ AuthController تم تهيئته');
    
    // تهيئة AdminController
    final adminController = AdminController();
    Get.put(adminController, permanent: true);
    print('✅ AdminController تم تهيئته');
    
    // اختبار UserHelper
    print('\n🔍 اختبار UserHelper...');
    
    // اختبار getToken
    final token = UserHelper.getToken();
    print('🔑 getToken(): ${token != null ? "✅ يعمل" : "⚠️ null"}');
    
    // اختبار getTokenAsync
    final tokenAsync = await UserHelper.getTokenAsync();
    print('🔑 getTokenAsync(): ${tokenAsync != null ? "✅ يعمل" : "⚠️ null"}');
    
    // اختبار isLoggedIn
    final isLoggedIn = UserHelper.isLoggedIn();
    print('👤 isLoggedIn(): ${isLoggedIn ? "✅ مسجل الدخول" : "⚠️ غير مسجل"}');
    
    // اختبار isAdmin
    final isAdmin = UserHelper.isAdmin();
    print('👑 isAdmin(): ${isAdmin ? "✅ مدير" : "⚠️ ليس مدير"}');
    
    // اختبار hasPermission
    final hasAdminPermission = UserHelper.hasPermission('admin');
    print('🔐 hasPermission(admin): ${hasAdminPermission ? "✅ لديه صلاحية" : "⚠️ ليس لديه صلاحية"}');
    
    // اختبار canSeeAllPages
    final canSeeAllPages = UserHelper.canSeeAllPages();
    print('📄 canSeeAllPages(): ${canSeeAllPages ? "✅ يمكنه رؤية جميع الصفحات" : "⚠️ لا يمكنه"}');
    
    // اختبار getUserName
    final userName = UserHelper.getUserName();
    print('👤 getUserName(): $userName');
    
    // اختبار getUserEmail
    final userEmail = UserHelper.getUserEmail();
    print('📧 getUserEmail(): $userEmail');
    
    // اختبار isTokenValid
    final isTokenValid = UserHelper.isTokenValid();
    print('🔒 isTokenValid(): ${isTokenValid ? "✅ صالح" : "⚠️ غير صالح"}');
    
    // اختبار getAuthHeaders
    final authHeaders = UserHelper.getAuthHeaders();
    print('📋 getAuthHeaders(): ${authHeaders.isNotEmpty ? "✅ يعمل" : "⚠️ فارغ"}');
    
    print('\n🔍 اختبار AdminController...');
    
    // اختبار تحميل المستخدمين
    try {
      await adminController.loadAllUsers();
      print('👥 loadAllUsers(): ✅ تم التحميل (${adminController.users.length} مستخدم)');
    } catch (e) {
      print('👥 loadAllUsers(): ⚠️ خطأ - $e');
    }
    
    // اختبار تحميل الإعدادات
    try {
      await adminController.loadSystemSettings();
      print('⚙️ loadSystemSettings(): ✅ تم التحميل (${adminController.systemSettings.length} إعداد)');
    } catch (e) {
      print('⚙️ loadSystemSettings(): ⚠️ خطأ - $e');
    }
    
    // اختبار تحميل النسخ الاحتياطية
    try {
      await adminController.loadBackups();
      print('💾 loadBackups(): ✅ تم التحميل (${adminController.backups.length} نسخة)');
    } catch (e) {
      print('💾 loadBackups(): ⚠️ خطأ - $e');
    }
    
    print('\n📊 ملخص الاختبار:');
    print('✅ UserHelper: جميع الوظائف تعمل');
    print('✅ AdminController: تم تهيئته بنجاح');
    print('✅ جميع الخدمات: مسجلة في GetX');
    print('🎉 لوحة التحكم الإدارية جاهزة للاستخدام!');
    
  } catch (e) {
    print('❌ خطأ في الاختبار: $e');
  }
}

/// اختبار سريع للتأكد من أن جميع الاستيرادات تعمل
void testImports() {
  print('📦 اختبار الاستيرادات...');
  
  // اختبار UserHelper
  try {
    UserHelper.getUserName();
    print('✅ UserHelper: يعمل');
  } catch (e) {
    print('❌ UserHelper: خطأ - $e');
  }
  
  // اختبار AdminController
  try {
    final controller = AdminController();
    print('✅ AdminController: يعمل');
  } catch (e) {
    print('❌ AdminController: خطأ - $e');
  }
  
  // اختبار AuthController
  try {
    final controller = AuthController();
    print('✅ AuthController: يعمل');
  } catch (e) {
    print('❌ AuthController: خطأ - $e');
  }
  
  print('📦 انتهى اختبار الاستيرادات');
}

/// معلومات النظام
void printSystemInfo() {
  print('\n📋 معلومات النظام:');
  print('🔧 Flutter: ${WidgetsBinding.instance.runtimeType}');
  print('📱 Platform: ${Theme.of(Get.context!).platform}');
  print('🌐 GetX: ${Get.isRegistered<AuthController>() ? "مسجل" : "غير مسجل"}');
}
