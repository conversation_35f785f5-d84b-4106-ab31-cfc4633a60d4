import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/archive_models.dart';
import '../services/api/archive_documents_api_service.dart';
import '../screens/archive/document_upload_screen.dart';

/// متحكم مستندات الأرشيف
class ArchiveDocumentsController extends GetxController {
  final ArchiveDocumentsApiService _apiService = ArchiveDocumentsApiService();

  // قوائم المستندات
  final RxList<ArchiveDocument> _allDocuments = <ArchiveDocument>[].obs;
  final RxList<ArchiveDocument> _filteredDocuments = <ArchiveDocument>[].obs;

  // المستند الحالي
  final Rx<ArchiveDocument?> _currentDocument = Rx<ArchiveDocument?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _categoryFilter = Rx<int?>(null);
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<ArchiveDocument> get allDocuments => _allDocuments;
  List<ArchiveDocument> get filteredDocuments => _filteredDocuments;
  ArchiveDocument? get currentDocument => _currentDocument.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get categoryFilter => _categoryFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllDocuments();
  }

  /// تحميل جميع مستندات الأرشيف
  Future<void> loadAllDocuments() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final documents = await _apiService.getAllDocuments();
      _allDocuments.assignAll(documents);
      _applyFilters();
      debugPrint('تم تحميل ${documents.length} مستند أرشيف');
    } catch (e) {
      _error.value = 'خطأ في تحميل مستندات الأرشيف: $e';
      debugPrint('خطأ في تحميل مستندات الأرشيف: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على مستند أرشيف بالمعرف
  Future<void> getDocumentById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final document = await _apiService.getDocumentById(id);
      _currentDocument.value = document;
      debugPrint('تم تحميل مستند الأرشيف: ${document?.title ?? 'غير محدد'}');
    } catch (e) {
      _error.value = 'خطأ في تحميل مستند الأرشيف: $e';
      debugPrint('خطأ في تحميل مستند الأرشيف: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// رفع مستند أرشيف جديد مع ملف
  Future<ArchiveDocument?> uploadDocument({
    required String title,
    required int categoryId,
    required File file,
    required String fileName,
    required String fileType,
    String? description,
    List<int>? tagIds,
    String? documentNumber,
    DateTime? documentDate,
    String? issuer,
    String? recipient,
    ArchiveDocumentConfidentiality? confidentiality,
    ArchiveDocumentImportance? importance,
  }) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // TODO: الحصول على معرف المستخدم الحالي من خدمة المصادقة
      const int currentUserId = 1; // مؤقت

      final newDocument = await _apiService.uploadDocument(
        title: title,
        categoryId: categoryId,
        file: file,
        fileName: fileName,
        fileType: fileType,
        description: description,
        tagIds: tagIds,
        documentNumber: documentNumber,
        documentDate: documentDate,
        issuer: issuer,
        recipient: recipient,
        createdBy: currentUserId,
      );

      if (newDocument != null) {
        _allDocuments.add(newDocument);
        _applyFilters();
        debugPrint('تم رفع مستند أرشيف جديد: ${newDocument.title}');
        return newDocument;
      }
      return null;
    } catch (e) {
      _error.value = 'خطأ في رفع مستند الأرشيف: $e';
      debugPrint('خطأ في رفع مستند الأرشيف: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء مستند أرشيف جديد
  Future<bool> createDocument(ArchiveDocument document) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newDocument = await _apiService.createDocument(document);
      if (newDocument != null) {
        _allDocuments.add(newDocument);
        _applyFilters();
        debugPrint('تم إنشاء مستند أرشيف جديد: ${newDocument.title}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء مستند الأرشيف: $e';
      debugPrint('خطأ في إنشاء مستند الأرشيف: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث مستند أرشيف
  Future<bool> updateDocument(ArchiveDocument document) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedDocument = await _apiService.updateDocument(document);
      if (updatedDocument != null) {
        final index = _allDocuments.indexWhere((d) => d.id == document.id);
        if (index != -1) {
          _allDocuments[index] = updatedDocument;
          _applyFilters();
        }
        debugPrint('تم تحديث مستند الأرشيف: ${updatedDocument.title}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث مستند الأرشيف: $e';
      debugPrint('خطأ في تحديث مستند الأرشيف: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مستند أرشيف
  Future<bool> deleteDocument(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteDocument(id);
      _allDocuments.removeWhere((d) => d.id == id);
      _applyFilters();
      debugPrint('تم حذف مستند الأرشيف');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف مستند الأرشيف: $e';
      debugPrint('خطأ في حذف مستند الأرشيف: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على المستندات حسب الفئة
  Future<void> getDocumentsByCategory(int categoryId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final documents = await _apiService.getDocumentsByCategory(categoryId);
      _allDocuments.assignAll(documents);
      _applyFilters();
      debugPrint('تم تحميل ${documents.length} مستند للفئة $categoryId');
    } catch (e) {
      _error.value = 'خطأ في تحميل مستندات الفئة: $e';
      debugPrint('خطأ في تحميل مستندات الفئة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allDocuments.where((document) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!document.title.toLowerCase().contains(query) &&
            !(document.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح الفئة
      if (_categoryFilter.value != null && document.categoryId != _categoryFilter.value) {
        return false;
      }

      // مرشح النشط فقط (افتراض أن جميع المستندات نشطة إذا لم تكن هناك خاصية isActive)
      // if (_showActiveOnly.value && !document.isActive) {
      //   return false;
      // }

      return true;
    }).toList();

    _filteredDocuments.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح الفئة
  void setCategoryFilter(int? categoryId) {
    _categoryFilter.value = categoryId;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _categoryFilter.value = null;
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllDocuments();
  }
}
