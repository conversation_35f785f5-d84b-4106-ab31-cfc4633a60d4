using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.Auth;
using BCrypt.Net;
using TaskModel = webApi.Models.Task;
using TaskStatusModel = webApi.Models.TaskStatus;

namespace webApi.Services;

/// <summary>
/// خدمة إنشاء البيانات الافتراضية في قاعدة البيانات
/// </summary>
public class DatabaseSeeder
{
    private readonly TasksDbContext _context;
    private readonly ILogger<DatabaseSeeder> _logger;

    public DatabaseSeeder(TasksDbContext context, ILogger<DatabaseSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// إنشاء البيانات الافتراضية
    /// </summary>
    public async System.Threading.Tasks.Task SeedAsync()
    {
        try
        {
            // إنشاء الصلاحيات أولاً
            await CreatePermissionsAsync();
            await _context.SaveChangesAsync();

            // ثم إنشاء المستخدمين الافتراضيين
            await CreateDefaultUsersDirectAsync();
            await _context.SaveChangesAsync();

            _logger.LogInformation("تم إنشاء البيانات الافتراضية بنجاح");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في إنشاء البيانات الافتراضية");
            throw;
        }
    }

    /// <summary>
    /// إنشاء الأدوار الافتراضية
    /// </summary>
    private async System.Threading.Tasks.Task CreatePermissionsAsync()
    {
        var permissions = new[]
        {
            new Permission { Id = 1, Name = "User", Description = "مستخدم عادي", CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new Permission { Id = 2, Name = "Supervisor", Description = "مشرف", CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new Permission { Id = 3, Name = "Manager", Description = "مدير", CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new Permission { Id = 4, Name = "Admin", Description = "مدير عام", CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new Permission { Id = 5, Name = "SuperAdmin", Description = "مدير النظام", CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() }
        };

        foreach (var permission in permissions)
        {
            if (!await _context.Permissions.AnyAsync(p => p.Id == permission.Id))
            {
                _context.Permissions.Add(permission);
            }
        }
    }

    /// <summary>
    /// إنشاء الأقسام الافتراضية
    /// </summary>
    private async System.Threading.Tasks.Task CreateDepartmentsAsync()
    {
        var departments = new[]
        {
            new Department 
            { 
                Id = 1, 
                Name = "تقنية المعلومات", 
                Description = "قسم تقنية المعلومات والتطوير", 
                IsActive = true, 
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() 
            },
            new Department 
            { 
                Id = 2, 
                Name = "الموارد البشرية", 
                Description = "قسم الموارد البشرية", 
                IsActive = true, 
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() 
            },
            new Department 
            { 
                Id = 3, 
                Name = "المالية", 
                Description = "قسم المالية والمحاسبة", 
                IsActive = true, 
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() 
            }
        };

        foreach (var department in departments)
        {
            if (!await _context.Departments.AnyAsync(d => d.Id == department.Id))
            {
                _context.Departments.Add(department);
            }
        }
    }

    /// <summary>
    /// إنشاء المستخدمين الافتراضيين
    /// </summary>
    private async System.Threading.Tasks.Task CreateDefaultUsersAsync()
    {
        var users = new[]
        {
            new User
            {
                Name = "مدير النظام",
                FirstName = "مدير",
                LastName = "النظام",
                Email = "<EMAIL>",
                Username = "admin",
                Password = BCrypt.Net.BCrypt.HashPassword("admin123"), // كلمة المرور: admin123
                DepartmentId = 1,
                Role = (int)UserRole.SuperAdmin,
                IsActive = true,
                IsOnline = false,
                IsDeleted = false,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new User
            {
                Name = "مستخدم تجريبي",
                FirstName = "مستخدم",
                LastName = "تجريبي",
                Email = "<EMAIL>",
                Username = "testuser",
                Password = BCrypt.Net.BCrypt.HashPassword("user123"), // كلمة المرور: user123
                DepartmentId = 1,
                Role = (int)UserRole.User,
                IsActive = true,
                IsOnline = false,
                IsDeleted = false,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new User
            {
                Name = "مدير تجريبي",
                FirstName = "مدير",
                LastName = "تجريبي",
                Email = "<EMAIL>",
                Username = "manager",
                Password = BCrypt.Net.BCrypt.HashPassword("manager123"), // كلمة المرور: manager123
                DepartmentId = 1,
                Role = (int)UserRole.Manager,
                IsActive = true,
                IsOnline = false,
                IsDeleted = false,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            }
        };

        foreach (var user in users)
        {
            if (!await _context.Users.AnyAsync(u => u.Email == user.Email))
            {
                _context.Users.Add(user);
                _logger.LogInformation("تم إنشاء المستخدم: {Email}", user.Email);
            }
        }
    }

    /// <summary>
    /// إنشاء أولويات المهام الافتراضية
    /// </summary>
    private async System.Threading.Tasks.Task CreateTaskPrioritiesAsync()
    {
        var priorities = new[]
        {
            new TaskPriority { Id = 1, Name = "منخفضة", Color = "#28a745", Level = 1, CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(), IsActive = true },
            new TaskPriority { Id = 2, Name = "متوسطة", Color = "#ffc107", Level = 2, CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(), IsActive = true },
            new TaskPriority { Id = 3, Name = "عالية", Color = "#fd7e14", Level = 3, CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(), IsActive = true },
            new TaskPriority { Id = 4, Name = "عاجلة", Color = "#dc3545", Level = 4, CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(), IsActive = true }
        };

        foreach (var priority in priorities)
        {
            if (!await _context.TaskPriorities.AnyAsync(p => p.Id == priority.Id))
            {
                _context.TaskPriorities.Add(priority);
            }
        }
    }

    /// <summary>
    /// إنشاء حالات المهام الافتراضية
    /// </summary>
    private async System.Threading.Tasks.Task CreateTaskStatusesAsync()
    {
        var statuses = new[]
        {
            new TaskStatusModel { Id = 1, Name = "جديدة", Color = "#6c757d", OrderIndex = 1, CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new TaskStatusModel { Id = 2, Name = "قيد التنفيذ", Color = "#007bff", OrderIndex = 2, CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new TaskStatusModel { Id = 3, Name = "مكتملة", Color = "#28a745", OrderIndex = 3, CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new TaskStatusModel { Id = 4, Name = "معلقة", Color = "#ffc107", OrderIndex = 4, CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new TaskStatusModel { Id = 5, Name = "ملغاة", Color = "#dc3545", OrderIndex = 5, CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() }
        };

        foreach (var status in statuses)
        {
            if (!await _context.TaskStatuses.AnyAsync(s => s.Id == status.Id))
            {
                _context.TaskStatuses.Add(status);
            }
        }
    }

    /// <summary>
    /// إنشاء أنواع المهام الافتراضية
    /// </summary>
    private async System.Threading.Tasks.Task CreateTaskTypesAsync()
    {
        var types = new[]
        {
            new TaskType { Id = 1, Name = "مهمة عامة", Description = "مهمة عامة", Color = "#6c757d", CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new TaskType { Id = 2, Name = "تطوير", Description = "مهام التطوير والبرمجة", Color = "#007bff", CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new TaskType { Id = 3, Name = "اختبار", Description = "مهام الاختبار والمراجعة", Color = "#28a745", CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new TaskType { Id = 4, Name = "صيانة", Description = "مهام الصيانة والإصلاح", Color = "#ffc107", CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
            new TaskType { Id = 5, Name = "اجتماع", Description = "الاجتماعات والمناقشات", Color = "#17a2b8", CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds() }
        };

        foreach (var type in types)
        {
            if (!await _context.TaskTypes.AnyAsync(t => t.Id == type.Id))
            {
                _context.TaskTypes.Add(type);
            }
        }
    }

    /// <summary>
    /// إنشاء المستخدمين الافتراضيين مباشرة
    /// </summary>
    private async System.Threading.Tasks.Task CreateDefaultUsersDirectAsync()
    {
        // إنشاء المستخدمين مباشرة بدون فحص الجداول الأخرى
        var adminExists = await _context.Users.AnyAsync(u => u.Email == "<EMAIL>");
        if (!adminExists)
        {
            var admin = new User
            {
                Name = "مدير النظام",
                FirstName = "مدير",
                LastName = "النظام",
                Email = "<EMAIL>",
                Username = "admin",
                Password = BCrypt.Net.BCrypt.HashPassword("admin123"),
                DepartmentId = null, // لا نحدد قسم لتجنب مشاكل المفاتيح الخارجية
                Role = 5, // SuperAdmin
                IsActive = true,
                IsOnline = false,
                IsDeleted = false,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };
            _context.Users.Add(admin);
            _logger.LogInformation("تم إنشاء المستخدم: {Email}", admin.Email);
        }

        var userExists = await _context.Users.AnyAsync(u => u.Email == "<EMAIL>");
        if (!userExists)
        {
            var user = new User
            {
                Name = "مستخدم تجريبي",
                FirstName = "مستخدم",
                LastName = "تجريبي",
                Email = "<EMAIL>",
                Username = "testuser",
                Password = BCrypt.Net.BCrypt.HashPassword("user123"),
                DepartmentId = null,
                Role = 1, // User
                IsActive = true,
                IsOnline = false,
                IsDeleted = false,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };
            _context.Users.Add(user);
            _logger.LogInformation("تم إنشاء المستخدم: {Email}", user.Email);
        }

        var managerExists = await _context.Users.AnyAsync(u => u.Email == "<EMAIL>");
        if (!managerExists)
        {
            var manager = new User
            {
                Name = "مدير تجريبي",
                FirstName = "مدير",
                LastName = "تجريبي",
                Email = "<EMAIL>",
                Username = "manager",
                Password = BCrypt.Net.BCrypt.HashPassword("manager123"),
                DepartmentId = null,
                Role = 3, // Manager
                IsActive = true,
                IsOnline = false,
                IsDeleted = false,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };
            _context.Users.Add(manager);
            _logger.LogInformation("تم إنشاء المستخدم: {Email}", manager.Email);
        }
    }
}
