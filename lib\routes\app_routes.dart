import 'package:flutter/material.dart';
import 'package:flutter_application_2/screens/home/<USER>';
import 'package:flutter_application_2/screens/home/<USER>';
import 'package:get/get.dart';
import '../screens/home/<USER>';
import '../screens/home/<USER>';
import '../screens/admin/admin_dashboard_screen.dart';
import '../screens/user/user_dashboard_screen.dart';
import '../screens/tasks/task_detail_screen.dart';
import '../screens/tasks/create_task_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/chat/chat_list_screen.dart';
import '../screens/chat/unified_chat_detail_screen.dart';
import '../screens/chat/unified_chat_list_screen.dart';
import '../screens/widgets/dashboard/fullscreen_chart_view.dart';
import '../screens/chat/create_group_chat_screen.dart';
import '../screens/chat/add_members_screen.dart';
import '../screens/chat/search_conversation_screen.dart';
import '../screens/chat/mute_notifications_screen.dart';

import '../screens/dashboard/customizable_dashboard_screen.dart';
import '../screens/dashboard/new_dashboard_screen.dart';
import '../screens/dashboard/monday_dashboard_screen.dart';
import '../screens/reports/reports_screen.dart';
import '../screens/reports/report_details_screen.dart';
import '../screens/reports/custom_report_builder_screen.dart';
import '../screens/reports/report_scheduler_screen.dart';
import '../screens/reports/static_reports_screen.dart';
import '../screens/reports/enhanced_reports_screen.dart';
import '../screens/reports/enhanced_charts_screen.dart';
import '../screens/reports/monday_style_reports_screen.dart';
import '../screens/reports/monday_style_report_viewer_screen.dart';
import '../screens/reports/reporting_dashboard_screen.dart';
import '../screens/reports/report_builder_screen.dart';
import '../screens/reports/exported_reports_screen.dart';
import '../screens/reports/report_viewer_screen.dart';

import '../screens/settings/database_repair_screen.dart';
import '../screens/calendar/calendar_screen.dart';
import '../screens/power_bi/power_bi_screen.dart';
import '../screens/power_bi/dynamic_power_bi_report_screen.dart';
import '../screens/archive/archive_home_screen.dart';

import '../screens/archive/document_upload_screen.dart';
import '../screens/archive/category_management_screen.dart';
import '../screens/archive/tag_management_screen.dart';
import '../screens/archive/document_version_history_screen.dart';
import '../screens/archive/document_browser_screen.dart';
import '../screens/calendar/calendar_event_details.dart';
import '../screens/archive/edit_document_screen.dart';
import '../screens/settings/archive_system_repair_screen.dart';

import '../screens/admin/permission_test_screen.dart';
// إضافة استيراد شاشة البحث الموحد
import '../screens/search/unified_search_screen.dart';
// إضافة استيراد شاشة اختبار المستخدمين
import '../screens/test/test_users_screen.dart';
// إضافة استيراد نماذج التقارير

// إضافة استيراد روابط لوحة المعلومات
import '../bindings/dashboard_binding.dart';
import '../bindings/search_binding.dart';

import '../bindings/text_document_binding.dart';
// إضافة استيراد شاشات المستندات النصية
import '../screens/documents/text_documents_list_screen.dart';
import '../screens/documents/text_document_editor_screen.dart';
import '../screens/documents/advanced_text_document_editor_screen.dart';

// إضافة استيراد الوسطاء
import '../middleware/unified_permission_middleware.dart';

/// تعريف جميع مسارات التطبيق
/// يتم استخدام هذا الملف لتوحيد طريقة التنقل بين الشاشات
class AppRoutes {
  // المسارات الرئيسية
  static const String home = '/home';
  static const String login = '/login';
  static const String register = '/register';
  static const String tasks = '/tasks';
  static const String admin = '/admin';
  static const String userDashboard = '/user-dashboard';
  static const String departmentDashboard = '/Departments';

  // مسارات المهام
  static const String taskDetail = '/task/detail';
  static const String createTask = '/task/create';
  static const String enhancedTaskBoard = '/task/board/enhanced';
  static const String taskStats = '/task/stats';

  // مسارات المحادثات
  static const String chatList = '/chat/list';
  static const String chatDetail = '/chat/detail';
  static const String unifiedChatDetail = '/chat/unified-detail';
  static const String unifiedChatList = '/chat/unified-list';
  static const String createGroupChat = '/chat/create-group';
  static const String addMembers = '/chat/add-members';
  static const String searchConversation = '/chat/search';
  static const String muteNotifications = '/chat/mute-notifications';

  // مسارات التقارير
  static const String reports = '/reports';
  static const String reportDetails = '/report/details';
  static const String createReport = '/report/create';
  static const String reportScheduler = '/report/scheduler';
  static const String staticReports = '/report/static';
  static const String enhancedReports = '/report/enhanced';
  static const String enhancedCharts = '/report/charts';
  static const String powerBI = '/power-bi';
  static const String dynamicPowerBI = '/power-bi/dynamic';

  // مسارات نظام التقارير الجديد
  static const String reportingDashboard = '/reporting/dashboard';
  static const String reportBuilder = '/reporting/builder';
  static const String reportViewer = '/reporting/viewer';
  static const String exportedReports = '/reporting/exported';

  // مسارات نظام التقارير بتصميم Monday.com
  static const String mondayStyleReports = '/reports/monday-style';
  static const String mondayStyleReportViewer = '/report/monday-style/viewer';

  // الاشعارات notifications
  static const String notifications = '/notifications';

  // مسارات الإعدادات
  static const String syncSettings = '/settings/sync';
  static const String databaseRepair = '/settings/database-repair';
  static const String archiveSystemRepair = '/settings/archive-system-repair';

  // مسارات التقويم
  static const String calendar = '/calendar';
  static const String calendarEventDetails = '/CalendarEventDetails';

  // مسارات لوحة المعلومات
  static const String customizableDashboard = '/dashboard/customizable';
  static const String newDashboard = '/dashboard/new';
  static const String mondayDashboard = '/dashboard/monday';
  static const String fullscreenChartView = '/dashboard/chart/fullscreen';

  // مسارات نظام الأرشفة الإلكترونية
  static const String archiveHome = '/archive';
  static const String documentBrowser = '/archive/documents';
  static const String documentUpload = '/archive/upload';
  static const String categoryManagement = '/archive/categories';
  static const String tagManagement = '/archive/tags';
  static const String documentVersionHistory = '/archive/document/versions';
  static const String editDocument = '/archive/document/edit';

  // مسارات الأدوار والصلاحيات
  static const String roles = '/admin/roles';
  static const String permissionTest = '/admin/permission-test';

  // مسارات البحث
  static const String unifiedSearch = '/search';

  // مسارات المستندات النصية
  static const String textDocumentsList = '/documents';
  static const String textDocumentEditor = '/documents/editor';
  static const String textDocumentViewer = '/documents/viewer';
  static const String advancedTextDocumentEditor = '/documents/advanced-editor';

  // مسارات المساعدة
  static const String help = '/help';

  // مسارات الاختبار
  static const String testUsers = '/test/users';

  /// قائمة بجميع صفحات التطبيق
  static final List<GetPage> pages = [
    // المسارات الرئيسية
    GetPage(
      name: home,
      page: () => const HomeScreen(),
      binding: DashboardBinding(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: departmentDashboard,
      page: () => const DepartmentsTab(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: notifications,
      page: () => const NotificationsTab(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    GetPage(name: login, page: () => const LoginScreen()),
    GetPage(name: register, page: () => const RegisterScreen()),
    GetPage(
      name: tasks,
      page: () => const TasksTab(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: admin,
      page: () => const AdminDashboardScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: userDashboard,
      page: () => const UserDashboardScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات المهام
    GetPage(
      name: taskDetail,
      page: () => TaskDetailScreen(
        taskId: Get.arguments['taskId'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: createTask,
      page: () => const CreateTaskScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات المحادثات
    GetPage(
      name: chatList,
      page: () => const ChatListScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: unifiedChatList,
      page: () => const UnifiedChatListScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: chatDetail,
      page: () => UnifiedChatDetailScreen(
        chatGroup: Get.arguments['chatGroup'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: unifiedChatDetail,
      page: () => UnifiedChatDetailScreen(
        chatGroup: Get.arguments['chatGroup'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: createGroupChat,
      page: () => const CreateGroupChatScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: addMembers,
      page: () => AddMembersScreen(
        chatGroup: Get.arguments['chatGroup'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: searchConversation,
      page: () => SearchConversationScreen(
        chatGroup: Get.arguments['chatGroup'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: muteNotifications,
      page: () => MuteNotificationsScreen(
        chatGroup: Get.arguments['chatGroup'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات التقارير
    GetPage(
      name: reports,
      page: () => const ReportsScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: reportDetails,
      page: () => ReportDetailsScreen(
        reportId: Get.arguments['reportId'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: createReport,
      page: () {
        // التحقق من وجود المعلومات المطلوبة وتوفير قيم افتراضية إذا لم تكن موجودة
        final args = Get.arguments ?? {};
        final title = args['title'] as String? ?? 'تقرير جديد';
        final description = args['description'] as String?;
        final reportType = args['reportType'] as String? ?? 'custom';
        final reportId = args['reportId'] as int?;

        return CustomReportBuilderScreen(
          title: title,
          description: description,
          reportType: reportType,
          reportId: reportId,
        );
      },
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: reportScheduler,
      page: () => ReportSchedulerScreen(
        reportId: Get.arguments['reportId'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: staticReports,
      page: () => const StaticReportsScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: enhancedReports,
      page: () => const EnhancedReportsScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: enhancedCharts,
      page: () => const EnhancedChartsScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: powerBI,
      page: () => const PowerBIScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: dynamicPowerBI,
      page: () => const DynamicPowerBIReportScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات نظام التقارير الجديد
    GetPage(
      name: reportingDashboard,
      page: () => const ReportingDashboardScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: reportBuilder,
      page: () => ReportBuilderScreen(
        report: Get.arguments?['report'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: reportViewer,
      page: () => ReportViewerScreen(
        reportId: Get.arguments['reportId'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: exportedReports,
      page: () => const ExportedReportsScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات الإعدادات
    GetPage(
      name: syncSettings,
      page: () {
        // توجيه المستخدم إلى لوحة التحكم الإدارية
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // تأخير التنفيذ حتى يتم بناء الواجهة
          try {
            final tabController =
                Get.find<TabController>(tag: 'admin_tab_controller');
            tabController
                .animateTo(6); // تبويب إعدادات التزامن هو السابع (index 6)
          } catch (e) {
            debugPrint('خطأ في التوجيه إلى تبويب إعدادات التزامن: $e');
          }
        });
        return const AdminDashboardScreen();
      },
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: databaseRepair,
      page: () => const DatabaseRepairScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: archiveSystemRepair,
      page: () => const ArchiveSystemRepairScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات التقويم
    GetPage(
      name: calendar,
      page: () => const CalendarScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: calendarEventDetails,
      page: () => CalendarEventDetails(
        event: Get.arguments['event'],
        onEventUpdated: Get.arguments['onEventUpdated'],
        onEventDeleted: Get.arguments['onEventDeleted'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات لوحة المعلومات
    GetPage(
      name: customizableDashboard,
      page: () => const CustomizableDashboardScreen(),
      binding: DashboardBinding(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: newDashboard,
      page: () => const NewDashboardScreen(),
      binding: DashboardBinding(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: mondayDashboard,
      page: () => const MondayDashboardScreen(),
      binding: DashboardBinding(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسار عرض المخطط بملء الشاشة
    GetPage(
      name: fullscreenChartView,
      page: () => FullscreenChartView(
        widget: Get.arguments['widget'],
        settings: Get.arguments['settings'] ?? {},
        onSettingsUpdated: Get.arguments['onSettingsUpdated'],
        title: Get.arguments['title'],
        chartKey: Get.arguments['chartKey'],
        chartType: Get.arguments['chartType'],
        startDate: Get.arguments['startDate'],
        endDate: Get.arguments['endDate'],
        filterType: Get.arguments['filterType'],
        onChartTypeChanged: Get.arguments['onChartTypeChanged'],
        onFilterChanged: Get.arguments['onFilterChanged'],
        chartContent: Get.arguments['chartContent'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات نظام التقارير بتصميم Monday.com
    GetPage(
      name: mondayStyleReports,
      page: () => const MondayStyleReportsScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: mondayStyleReportViewer,
      page: () => MondayStyleReportViewerScreen(
        reportId: Get.arguments?['reportId'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات نظام الأرشفة الإلكترونية
    GetPage(
      name: archiveHome,
      page: () => const ArchiveHomeScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: documentBrowser,
      page: () => const DocumentBrowserScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: documentUpload,
      page: () => const DocumentUploadScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: categoryManagement,
      page: () => const CategoryManagementScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: tagManagement,
      page: () => const TagManagementScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: documentVersionHistory,
      page: () => DocumentVersionHistoryScreen(
        documentId: Get.arguments['documentId'],
        documentTitle: Get.arguments['documentTitle'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: editDocument,
      page: () => EditDocumentScreen(
        document: Get.arguments['document'],
      ),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات الأدوار والصلاحيات
    GetPage(
      name: roles,
      page: () {
        // توجيه المستخدم إلى لوحة التحكم الإدارية - تبويب إدارة الأدوار
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // تأخير التنفيذ حتى يتم بناء الواجهة
          try {
            final tabController =
                Get.find<TabController>(tag: 'admin_tab_controller');
            tabController
                .animateTo(2); // تبويب إدارة الأدوار هو الثالث (index 2)
          } catch (e) {
            debugPrint('خطأ في التوجيه إلى تبويب إدارة الأدوار: $e');
          }
        });
        return const AdminDashboardScreen();
      },
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: permissionTest,
      page: () => const PermissionTestScreen(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات البحث
    GetPage(
      name: unifiedSearch,
      page: () => const UnifiedSearchScreen(),
      binding: SearchBinding(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات المستندات النصية
    GetPage(
      name: textDocumentsList,
      page: () => const TextDocumentsListScreen(),
      binding: TextDocumentBinding(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: textDocumentEditor,
      page: () => TextDocumentEditorScreen(
        documentId: Get.arguments?['documentId'],
        taskId: Get.arguments?['taskId'],
        defaultTitle: Get.arguments?['defaultTitle'],
        defaultType: Get.arguments?['defaultType'],
      ),
      binding: TextDocumentBinding(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: advancedTextDocumentEditor,
      page: () => AdvancedTextDocumentEditorScreen(
        documentId: Get.arguments?['documentId'],
        taskId: Get.arguments?['taskId'],
        defaultTitle: Get.arguments?['defaultTitle'],
        defaultType: Get.arguments?['defaultType'],
      ),
      binding: TextDocumentBinding(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),
    GetPage(
      name: textDocumentViewer,
      page: () => TextDocumentEditorScreen(
        documentId: Get.arguments['documentId'],
      ),
      binding: TextDocumentBinding(),
      middlewares: [UnifiedPermissionMiddleware()],
    ),

    // مسارات الاختبار
    GetPage(
      name: testUsers,
      page: () => const TestUsersScreen(),
      // لا نحتاج middleware للاختبار - يمكن الوصول بدون تسجيل دخول
    ),
  ];
}
