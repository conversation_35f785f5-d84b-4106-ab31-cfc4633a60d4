import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../routes/app_routes.dart';

/// وسطاء الصلاحيات الموحد
/// يتحقق من صلاحيات المستخدم قبل الوصول للصفحات المحمية
class UnifiedPermissionMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    // التحقق من وجود متحكم المصادقة
    if (!Get.isRegistered<AuthController>()) {
      debugPrint('AuthController غير مسجل - إعادة توجيه لصفحة تسجيل الدخول');
      return const RouteSettings(name: AppRoutes.login);
    }

    final authController = Get.find<AuthController>();
    
    // التحقق من تسجيل الدخول
    if (!authController.isLoggedIn) {
      debugPrint('المستخدم غير مسجل الدخول - إعادة توجيه لصفحة تسجيل الدخول');
      return const RouteSettings(name: AppRoutes.login);
    }

    // التحقق من وجود المستخدم الحالي
    if (authController.currentUser.value == null) {
      debugPrint('لا يوجد مستخدم حالي - إعادة توجيه لصفحة تسجيل الدخول');
      return const RouteSettings(name: AppRoutes.login);
    }

    // التحقق من الصلاحيات حسب المسار
    if (route != null && !_hasPermissionForRoute(route, authController)) {
      debugPrint('المستخدم لا يملك صلاحية للوصول إلى: $route');
      // إعادة توجيه للصفحة الرئيسية أو صفحة عدم وجود صلاحية
      return const RouteSettings(name: AppRoutes.home);
    }

    // السماح بالوصول
    return null;
  }

  /// التحقق من صلاحية المستخدم للوصول لمسار معين
  bool _hasPermissionForRoute(String route, AuthController authController) {
    final user = authController.currentUser.value;
    if (user == null) return false;

    // قائمة المسارات التي تتطلب صلاحيات إدارية
    final adminRoutes = [
      AppRoutes.admin,
      AppRoutes.roles,
      AppRoutes.permissionTest,
      AppRoutes.databaseRepair,
      AppRoutes.archiveSystemRepair,
      AppRoutes.syncSettings,
    ];

    // قائمة المسارات التي تتطلب صلاحيات التقارير
    final reportRoutes = [
      AppRoutes.reports,
      AppRoutes.reportDetails,
      AppRoutes.createReport,
      AppRoutes.reportScheduler,
      AppRoutes.staticReports,
      AppRoutes.enhancedReports,
      AppRoutes.enhancedCharts,
      AppRoutes.powerBI,
      AppRoutes.dynamicPowerBI,
      AppRoutes.reportingDashboard,
      AppRoutes.reportBuilder,
      AppRoutes.reportViewer,
      AppRoutes.exportedReports,
      AppRoutes.mondayStyleReports,
      AppRoutes.mondayStyleReportViewer,
    ];

    // قائمة المسارات التي تتطلب صلاحيات الأرشفة
    final archiveRoutes = [
      AppRoutes.archiveHome,
      AppRoutes.documentBrowser,
      AppRoutes.documentUpload,
      AppRoutes.categoryManagement,
      AppRoutes.tagManagement,
      AppRoutes.documentVersionHistory,
      AppRoutes.editDocument,
    ];

    // التحقق من الصلاحيات الإدارية
    if (adminRoutes.contains(route)) {
      return _hasAdminPermission(user);
    }

    // التحقق من صلاحيات التقارير
    if (reportRoutes.contains(route)) {
      return _hasReportPermission(user);
    }

    // التحقق من صلاحيات الأرشفة
    if (archiveRoutes.contains(route)) {
      return _hasArchivePermission(user);
    }

    // المسارات العامة متاحة لجميع المستخدمين المسجلين
    return true;
  }

  /// التحقق من الصلاحيات الإدارية
  bool _hasAdminPermission(dynamic user) {
    // التحقق من دور المستخدم
    if (user.role == 'admin' || user.role == 'super_admin') {
      return true;
    }

    // التحقق من نوع UserRole إذا كان متاحاً
    if (user.role != null) {
      final role = user.role;
      if (role.toString().contains('admin') || role.toString().contains('superAdmin')) {
        return true;
      }
    }

    return false;
  }

  /// التحقق من صلاحيات التقارير
  bool _hasReportPermission(dynamic user) {
    // المديرون لديهم صلاحية كاملة
    if (user.role == 'admin' || user.role == 'super_admin') {
      return true;
    }

    // التحقق من نوع UserRole إذا كان متاحاً
    if (user.role != null) {
      final role = user.role;
      if (role.toString().contains('admin') || role.toString().contains('manager')) {
        return true;
      }
    }

    return false;
  }

  /// التحقق من صلاحيات الأرشفة
  bool _hasArchivePermission(dynamic user) {
    // المديرون لديهم صلاحية كاملة
    if (user.role == 'admin' || user.role == 'super_admin') {
      return true;
    }

    // التحقق من نوع UserRole إذا كان متاحاً
    if (user.role != null) {
      final role = user.role;
      if (role.toString().contains('admin') || role.toString().contains('manager')) {
        return true;
      }
    }

    return false;
  }
}
