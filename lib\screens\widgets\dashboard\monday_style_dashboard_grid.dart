import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_styles.dart';
import '../../../models/dashboard_model.dart' as simple_model;
import '../../../models/dashboard_widget_model.dart' as ui_model;
import '../../../models/dashboard_models.dart' as api_model;
import '../../../services/dashboard_service.dart';
import '../../../controllers/auth_controller.dart';
import '../../../utils/dashboard_widget_adapter.dart';
import 'monday_style_chart_widget.dart';
import 'monday_style_add_widget_dialog.dart';

/// شبكة لوحة المعلومات بتصميم Monday.com
///
/// تعرض شبكة لوحة المعلومات بتصميم مشابه لـ Monday.com
class MondayStyleDashboardGrid extends StatefulWidget {
  /// معرف لوحة المعلومات
  final String dashboardId;

  /// هل في وضع التعديل
  final bool isEditing;

  const MondayStyleDashboardGrid({
    super.key,
    required this.dashboardId,
    required this.isEditing,
  });

  @override
  State<MondayStyleDashboardGrid> createState() => _MondayStyleDashboardGridState();
}

class _MondayStyleDashboardGridState extends State<MondayStyleDashboardGrid> {
  late final DashboardService _dashboardService;

  // العناصر المعروضة في الشبكة
  List<simple_model.SimpleDashboardWidget> _widgets = [];

  // حالة التحميل
  bool _isLoading = true;

  // رسالة الخطأ
  String? _errorMessage;

  // أبعاد الشبكة
  final int _gridColumns = 12;
  final int _gridRows = 12;

  // أبعاد الخلية
  double _cellWidth = 0;
  final double _cellHeight = 80;

  @override
  void initState() {
    super.initState();
    _initController();
  }

  /// تهيئة المتحكم
  void _initController() {
    try {
      // التحقق من وجود الخدمة أو تسجيلها
      if (!Get.isRegistered<DashboardService>()) {
        // إنشاء خدمة لوحة المعلومات وتهيئتها
        final dashboardService = DashboardService();
        // تسجيل الخدمة بشكل دائم
        Get.put(dashboardService, permanent: true);
        _dashboardService = dashboardService;
      } else {
        // الحصول على الخدمة الموجودة
        _dashboardService = Get.find<DashboardService>();
      }

      // التأكد من وجود متحكم المصادقة
      if (!Get.isRegistered<AuthController>()) {
        // إنشاء متحكم المصادقة وتهيئته
        final authController = AuthController();
        // تسجيل المتحكم بشكل دائم
        Get.put(authController, permanent: true);
      }

      // تحميل العناصر
      _loadWidgets();
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تهيئة الخدمة: $e';
        _isLoading = false;
      });

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ',
        'خطأ في تهيئة الخدمة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withAlpha(180),
        colorText: Colors.white,
        margin: const EdgeInsets.all(8),
      );
    }
  }

  @override
  void didUpdateWidget(MondayStyleDashboardGrid oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.dashboardId != widget.dashboardId) {
      _loadWidgets();
    }
  }

  /// تحميل العناصر
  Future<void> _loadWidgets() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل العناصر من الخدمة
      final dashboard = _dashboardService.currentDashboardObs.value;

      if (dashboard != null) {
        setState(() {
          // تحويل من DashboardWidget (dashboard_models.dart) إلى SimpleDashboardWidget (dashboard_model.dart)
          _widgets = (dashboard.dashboardWidgets ?? []).map((w) =>
            simple_model.SimpleDashboardWidget(
              id: w.id.toString(),
              title: w.title,
              type: w.type,
              settings: w.config != null ? jsonDecode(w.config!) : {},
              row: w.positionY,
              column: w.positionX,
              width: w.width,
              height: w.height,
              isExpandable: true,
              isExpanded: false,
              isRefreshable: true,
            )
          ).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'لم يتم العثور على لوحة المعلومات';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل العناصر: $e';
        _isLoading = false;
      });
    }
  }

  /// إضافة عنصر جديد
  Future<void> _addWidget() async {
    // فتح حوار إضافة عنصر جديد
    final result = await Get.dialog<ui_model.DashboardWidget>(
      MondayStyleAddWidgetDialog(
        dashboardId: widget.dashboardId,
      ),
    );

    if (result != null) {
      // تحويل العنصر من نموذج dashboard_widget_model.dart إلى نموذج dashboard_model.dart
      final dashboardWidget = DashboardWidgetAdapter.convertToDashboardModel(result);

      // تحديد موقع مناسب للعنصر الجديد
      final position = _findSuitablePosition(dashboardWidget.width, dashboardWidget.height);

      // تحديث موقع العنصر
      final updatedWidget = dashboardWidget.copyWith(
        row: position.dy.toInt(),
        column: position.dx.toInt(),
      );

      setState(() {
        _widgets.add(updatedWidget);
      });

      // حفظ التغييرات - تحديث لوحة المعلومات الحالية
      final currentDashboard = _dashboardService.currentDashboardObs.value;
      if (currentDashboard != null) {
        // تحويل العناصر من SimpleDashboardWidget إلى DashboardWidget (dashboard_models.dart)
        final convertedWidgets = _widgets.map((w) => api_model.DashboardWidget(
          id: int.tryParse(w.id) ?? 0,
          dashboardId: currentDashboard.id,
          title: w.title,
          type: w.type,
          positionX: w.column,
          positionY: w.row,
          width: w.width,
          height: w.height,
          config: jsonEncode(w.settings),
          createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        )).toList();

        final updatedDashboard = currentDashboard.copyWith(
          dashboardWidgets: convertedWidgets,
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        await _dashboardService.updateDashboard(updatedDashboard);
      }
    }
  }

  /// البحث عن موقع مناسب للعنصر الجديد
  Offset _findSuitablePosition(int widgetWidth, int widgetHeight) {
    // البدء من الموقع (0, 0)
    int rowIndex = 0;
    int columnIndex = 0;
    bool positionFound = false;

    // البحث عن موقع مناسب
    while (!positionFound) {
      bool hasOverlap = false;

      // التحقق من عدم وجود تداخل مع العناصر الموجودة
      for (final existingWidget in _widgets) {
        if (_checkOverlap(
          columnIndex, rowIndex, widgetWidth, widgetHeight,
          existingWidget.column, existingWidget.row,
          existingWidget.width, existingWidget.height
        )) {
          hasOverlap = true;
          break;
        }
      }

      // إذا لم يكن هناك تداخل، فقد وجدنا موقعًا مناسبًا
      if (!hasOverlap) {
        positionFound = true;
      } else {
        // تجربة الموقع التالي
        columnIndex++;

        // إذا وصلنا إلى نهاية الصف، ننتقل إلى الصف التالي
        if (columnIndex + widgetWidth > _gridColumns) {
          columnIndex = 0;
          rowIndex++;
        }
      }
    }

    return Offset(columnIndex.toDouble(), rowIndex.toDouble());
  }

  /// التحقق من وجود تداخل بين عنصرين
  bool _checkOverlap(
    int x1, int y1, int width1, int height1,
    int x2, int y2, int width2, int height2
  ) {
    // التحقق من عدم وجود تداخل أفقي
    bool noHorizontalOverlap = x1 + width1 <= x2 || x2 + width2 <= x1;

    // التحقق من عدم وجود تداخل رأسي
    bool noVerticalOverlap = y1 + height1 <= y2 || y2 + height2 <= y1;

    // إذا لم يكن هناك تداخل أفقي أو رأسي، فلا يوجد تداخل
    return !(noHorizontalOverlap || noVerticalOverlap);
  }

  /// تعديل عنصر
  Future<void> _editWidget(simple_model.SimpleDashboardWidget widget) async {
    // تحويل العنصر من نموذج dashboard_model.dart إلى نموذج dashboard_widget_model.dart
    final widgetModel = DashboardWidgetAdapter.convertToWidgetModel(widget, this.widget.dashboardId);

    final result = await Get.dialog<ui_model.DashboardWidget>(
      MondayStyleAddWidgetDialog(
        dashboardId: this.widget.dashboardId,
        widget: widgetModel,
        isEditing: true,
      ),
    );

    if (result != null) {
      // تحويل العنصر من نموذج dashboard_widget_model.dart إلى نموذج dashboard_model.dart
      final dashboardWidget = DashboardWidgetAdapter.convertToDashboardModel(result);

      setState(() {
        final index = _widgets.indexWhere((w) => w.id == widget.id);
        if (index != -1) {
          _widgets[index] = dashboardWidget;
        }
      });

      // حفظ التغييرات - تحديث لوحة المعلومات الحالية
      final currentDashboard = _dashboardService.currentDashboardObs.value;
      if (currentDashboard != null) {
        // تحويل العناصر من SimpleDashboardWidget إلى DashboardWidget (dashboard_models.dart)
        final convertedWidgets = _widgets.map((w) => api_model.DashboardWidget(
          id: int.tryParse(w.id) ?? 0,
          dashboardId: currentDashboard.id,
          title: w.title,
          type: w.type,
          positionX: w.column,
          positionY: w.row,
          width: w.width,
          height: w.height,
          config: jsonEncode(w.settings),
          createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        )).toList();

        final updatedDashboard = currentDashboard.copyWith(
          dashboardWidgets: convertedWidgets,
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        await _dashboardService.updateDashboard(updatedDashboard);
      }
    }
  }

  /// حذف عنصر
  Future<void> _deleteWidget(String widgetId) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('حذف العنصر'),
        content: const Text('هل أنت متأكد من حذف هذا العنصر؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _widgets.removeWhere((w) => w.id == widgetId);
      });

      // حفظ التغييرات - تحديث لوحة المعلومات الحالية
      final currentDashboard = _dashboardService.currentDashboardObs.value;
      if (currentDashboard != null) {
        // تحويل العناصر من SimpleDashboardWidget إلى DashboardWidget (dashboard_models.dart)
        final convertedWidgets = _widgets.map((w) => api_model.DashboardWidget(
          id: int.tryParse(w.id) ?? 0,
          dashboardId: currentDashboard.id,
          title: w.title,
          type: w.type,
          positionX: w.column,
          positionY: w.row,
          width: w.width,
          height: w.height,
          config: jsonEncode(w.settings),
          createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        )).toList();

        final updatedDashboard = currentDashboard.copyWith(
          dashboardWidgets: convertedWidgets,
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        await _dashboardService.updateDashboard(updatedDashboard);
      }
    }
  }

  /// تحريك عنصر
  Future<void> _moveWidget(String widgetId, Offset newPosition) async {
    final index = _widgets.indexWhere((w) => w.id == widgetId);
    if (index != -1) {
      setState(() {
        _widgets[index] = _widgets[index].copyWith(
          row: newPosition.dy.toInt(),
          column: newPosition.dx.toInt(),
        );
      });

      // حفظ التغييرات - تحديث لوحة المعلومات الحالية
      final currentDashboard = _dashboardService.currentDashboardObs.value;
      if (currentDashboard != null) {
        // تحويل العناصر من SimpleDashboardWidget إلى DashboardWidget (dashboard_models.dart)
        final convertedWidgets = _widgets.map((w) => api_model.DashboardWidget(
          id: int.tryParse(w.id) ?? 0,
          dashboardId: currentDashboard.id,
          title: w.title,
          type: w.type,
          positionX: w.column,
          positionY: w.row,
          width: w.width,
          height: w.height,
          config: jsonEncode(w.settings),
          createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        )).toList();

        final updatedDashboard = currentDashboard.copyWith(
          dashboardWidgets: convertedWidgets,
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        await _dashboardService.updateDashboard(updatedDashboard);
      }
    }
  }

  /// تغيير حجم عنصر
  Future<void> _resizeWidget(String widgetId, Size newSize) async {
    final index = _widgets.indexWhere((w) => w.id == widgetId);
    if (index != -1) {
      setState(() {
        _widgets[index] = _widgets[index].copyWith(
          width: newSize.width.toInt(),
          height: newSize.height.toInt(),
        );
      });

      // حفظ التغييرات - تحديث لوحة المعلومات الحالية
      final currentDashboard = _dashboardService.currentDashboardObs.value;
      if (currentDashboard != null) {
        // تحويل العناصر من SimpleDashboardWidget إلى DashboardWidget (dashboard_models.dart)
        final convertedWidgets = _widgets.map((w) => api_model.DashboardWidget(
          id: int.tryParse(w.id) ?? 0,
          dashboardId: currentDashboard.id,
          title: w.title,
          type: w.type,
          positionX: w.column,
          positionY: w.row,
          width: w.width,
          height: w.height,
          config: jsonEncode(w.settings),
          createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        )).toList();

        final updatedDashboard = currentDashboard.copyWith(
          dashboardWidgets: convertedWidgets,
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        await _dashboardService.updateDashboard(updatedDashboard);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // حساب عرض الخلية بناءً على عرض الشاشة
    _cellWidth = MediaQuery.of(context).size.width / _gridColumns;

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: AppStyles.bodyMedium.copyWith(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadWidgets,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        // خلفية الشبكة
        _buildGridBackground(),

        // العناصر
        ..._widgets.map((widget) => _buildWidget(widget)),

        // زر إضافة عنصر جديد
        if (widget.isEditing)
          Positioned(
            right: 16,
            bottom: 16,
            child: FloatingActionButton(
              onPressed: _addWidget,
              tooltip: 'إضافة عنصر',
              child: const Icon(Icons.add),
            ),
          ),
      ],
    );
  }

  /// بناء خلفية الشبكة
  Widget _buildGridBackground() {
    return CustomPaint(
      size: Size(
        MediaQuery.of(context).size.width,
        _gridRows * _cellHeight,
      ),
      painter: _GridPainter(
        columns: _gridColumns,
        rows: _gridRows,
        cellWidth: _cellWidth,
        cellHeight: _cellHeight,
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white.withAlpha(13) // 0.05 * 255 = 13
            : Colors.black.withAlpha(13), // 0.05 * 255 = 13
      ),
    );
  }

  /// بناء عنصر
  Widget _buildWidget(simple_model.SimpleDashboardWidget widget) {
    // حساب الموقع بناءً على أبعاد الشبكة
    final left = widget.column * _cellWidth;
    final top = widget.row * _cellHeight;

    // حساب الحجم بناءً على أبعاد الشبكة
    final width = widget.width * _cellWidth;
    final height = widget.height * _cellHeight;

    return Positioned(
      left: left,
      top: top,
      width: width,
      height: height,
      child: MondayStyleChartWidget(
        widget: widget,
        isEditing: this.widget.isEditing,
        onDelete: () => _deleteWidget(widget.id),
        onEdit: () => _editWidget(widget),
        onMove: (newPosition) {
          // تحويل الموضع إلى مؤشرات الصف والعمود
          final newRowIndex = (newPosition.dy / _cellHeight).round();
          final newColumnIndex = (newPosition.dx / _cellWidth).round();

          // التحقق من أن الموقع الجديد ضمن حدود الشبكة
          if (newColumnIndex >= 0 &&
              newColumnIndex + widget.width <= _gridColumns &&
              newRowIndex >= 0 &&
              newRowIndex + widget.height <= _gridRows) {

            // التحقق من عدم وجود تداخل مع العناصر الأخرى
            bool canMove = true;
            for (final otherWidget in _widgets) {
              if (otherWidget.id != widget.id && _checkOverlap(
                newColumnIndex, newRowIndex, widget.width, widget.height,
                otherWidget.column, otherWidget.row,
                otherWidget.width, otherWidget.height
              )) {
                canMove = false;
                break;
              }
            }

            if (canMove) {
              _moveWidget(widget.id, Offset(newColumnIndex.toDouble(), newRowIndex.toDouble()));
            }
          }
        },
        onResize: (newSize) {
          // تحويل الحجم إلى عرض وارتفاع
          final newWidth = (newSize.width / _cellWidth).round();
          final newHeight = (newSize.height / _cellHeight).round();

          // التحقق من أن الحجم الجديد ضمن حدود الشبكة
          if (newWidth > 0 &&
              widget.column + newWidth <= _gridColumns &&
              newHeight > 0 &&
              widget.row + newHeight <= _gridRows) {

            // التحقق من عدم وجود تداخل مع العناصر الأخرى
            bool canResize = true;
            for (final otherWidget in _widgets) {
              if (otherWidget.id != widget.id && _checkOverlap(
                widget.column, widget.row, newWidth, newHeight,
                otherWidget.column, otherWidget.row,
                otherWidget.width, otherWidget.height
              )) {
                canResize = false;
                break;
              }
            }

            if (canResize) {
              _resizeWidget(widget.id, Size(newWidth.toDouble(), newHeight.toDouble()));
            }
          }
        },
      ),
    );
  }
}

/// رسام الشبكة
class _GridPainter extends CustomPainter {
  final int columns;
  final int rows;
  final double cellWidth;
  final double cellHeight;
  final Color color;

  _GridPainter({
    required this.columns,
    required this.rows,
    required this.cellWidth,
    required this.cellHeight,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1;

    // رسم الخطوط الأفقية
    for (int i = 0; i <= rows; i++) {
      final y = i * cellHeight;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // رسم الخطوط العمودية
    for (int i = 0; i <= columns; i++) {
      final x = i * cellWidth;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
