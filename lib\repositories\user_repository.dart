import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/api/users_api_service.dart';

/// مستودع المستخدمين - متوافق مع ASP.NET Core API
class UserRepository {
  final UsersApiService _apiService = UsersApiService();

  /// الحصول على مستخدم بالمعرف
  Future<User?> getUserById(int userId) async {
    try {
      return await _apiService.getUserById(userId);
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدم $userId: $e');
      return null;
    }
  }

  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    try {
      return await _apiService.getAllUsers();
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين: $e');
      return [];
    }
  }

  /// الحصول على المستخدمين النشطين
  Future<List<User>> getActiveUsers() async {
    try {
      final allUsers = await _apiService.getAllUsers();
      return allUsers.where((user) => !user.isDeleted).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين النشطين: $e');
      return [];
    }
  }

  /// الحصول على مستخدمين القسم
  Future<List<User>> getUsersByDepartment(int departmentId) async {
    try {
      final allUsers = await _apiService.getAllUsers();
      return allUsers.where((user) => 
        user.departmentId == departmentId && !user.isDeleted
      ).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على مستخدمي القسم $departmentId: $e');
      return [];
    }
  }

  /// الحصول على المستخدمين حسب الدور
  Future<List<User>> getUsersByRole(String role) async {
    try {
      return await _apiService.getUsersByRole(role);
    } catch (e) {
      debugPrint('خطأ في الحصول على مستخدمي الدور $role: $e');
      return [];
    }
  }

  /// إنشاء مستخدم جديد
  Future<User?> createUser(User user) async {
    try {
      return await _apiService.createUser(user);
    } catch (e) {
      debugPrint('خطأ في إنشاء المستخدم: $e');
      return null;
    }
  }

  /// تحديث مستخدم
  Future<User?> updateUser(User user) async {
    try {
      return await _apiService.updateUser(user.id, user);
    } catch (e) {
      debugPrint('خطأ في تحديث المستخدم ${user.id}: $e');
      return null;
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(int userId) async {
    try {
      return await _apiService.deleteUser(userId);
    } catch (e) {
      debugPrint('خطأ في حذف المستخدم $userId: $e');
      return false;
    }
  }

  /// البحث عن المستخدمين
  Future<List<User>> searchUsers(String query) async {
    try {
      final allUsers = await _apiService.getAllUsers();
      return allUsers.where((user) => 
        (user.name.toLowerCase().contains(query.toLowerCase()) ||
         user.email.toLowerCase().contains(query.toLowerCase())) &&
        !user.isDeleted
      ).toList();
    } catch (e) {
      debugPrint('خطأ في البحث عن المستخدمين: $e');
      return [];
    }
  }

  /// تفعيل مستخدم
  Future<bool> activateUser(int userId) async {
    try {
      final user = await getUserById(userId);
      if (user != null) {
        final updatedUser = user.copyWith(isDeleted: false);
        final result = await updateUser(updatedUser);
        return result != null;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تفعيل المستخدم $userId: $e');
      return false;
    }
  }

  /// إلغاء تفعيل مستخدم
  Future<bool> deactivateUser(int userId) async {
    try {
      final user = await getUserById(userId);
      if (user != null) {
        final updatedUser = user.copyWith(isDeleted: true);
        final result = await updateUser(updatedUser);
        return result != null;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في إلغاء تفعيل المستخدم $userId: $e');
      return false;
    }
  }

  /// تغيير كلمة مرور المستخدم
  Future<bool> changeUserPassword(String currentPassword, String newPassword) async {
    try {
      return await _apiService.changePassword(currentPassword, newPassword);
    } catch (e) {
      debugPrint('خطأ في تغيير كلمة المرور: $e');
      return false;
    }
  }

  /// إعادة تعيين كلمة مرور المستخدم
  Future<bool> resetUserPassword(int userId, String newPassword) async {
    try {
      return await _apiService.resetPassword(userId, newPassword);
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين كلمة مرور المستخدم $userId: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات المستخدمين
  Future<Map<String, dynamic>> getUsersStatistics() async {
    try {
      final allUsers = await _apiService.getAllUsers();
      final activeUsers = allUsers.where((user) => !user.isDeleted).length;
      final inactiveUsers = allUsers.where((user) => user.isDeleted).length;
      
      // تجميع حسب الأدوار
      final roleStats = <String, int>{};
      for (final user in allUsers.where((user) => !user.isDeleted)) {
        roleStats[user.role.displayName] = (roleStats[user.role.displayName] ?? 0) + 1;
      }
      
      // تجميع حسب الأقسام
      final departmentStats = <int, int>{};
      for (final user in allUsers.where((user) => !user.isDeleted && user.departmentId != null)) {
        final deptId = user.departmentId!;
        departmentStats[deptId] = (departmentStats[deptId] ?? 0) + 1;
      }
      
      return {
        'total': allUsers.length,
        'active': activeUsers,
        'inactive': inactiveUsers,
        'byRole': roleStats,
        'byDepartment': departmentStats,
      };
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المستخدمين: $e');
      return {};
    }
  }
}
