import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_application_2/controllers/archive_documents_controller.dart';
import 'package:flutter_application_2/controllers/archive_categories_controller.dart';
import 'package:flutter_application_2/controllers/archive_tags_controller.dart';

import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/responsive_helper.dart';
import 'package:flutter_application_2/screens/widgets/custom_app_bar.dart';
import 'package:flutter_application_2/screens/widgets/common/loading_indicator.dart';

import 'document_browser_screen.dart';
import 'document_upload_screen.dart';
import 'category_management_screen.dart';
import 'tag_management_screen.dart';

/// شاشة الصفحة الرئيسية للأرشيف الإلكتروني
class ArchiveHomeScreen extends StatefulWidget {
  const ArchiveHomeScreen({super.key});

  @override
  State<ArchiveHomeScreen> createState() => _ArchiveHomeScreenState();
}

class _ArchiveHomeScreenState extends State<ArchiveHomeScreen> {
  final ArchiveDocumentsController _documentsController = Get.find<ArchiveDocumentsController>();
  final ArchiveCategoriesController _categoriesController = Get.find<ArchiveCategoriesController>();
  final ArchiveTagsController _tagsController = Get.find<ArchiveTagsController>();

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات الأولية
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Future.wait([
        _documentsController.loadAllDocuments(),
        _categoriesController.loadAllCategories(),
        _tagsController.loadAllTags(),
      ]);
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = ResponsiveHelper.isLargeScreen(context);
    final isMediumScreen = ResponsiveHelper.isMediumScreen(context);
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'الأرشيف الإلكتروني',
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _buildBody(context, isLargeScreen, isMediumScreen, isSmallScreen),
    );
  }

  Widget _buildBody(BuildContext context, bool isLargeScreen, bool isMediumScreen, bool isSmallScreen) {
    if (isSmallScreen) {
      return _buildMobileLayout();
    } else if (isMediumScreen) {
      return _buildTabletLayout();
    } else {
      return _buildDesktopLayout();
    }
  }

  /// تخطيط الجوال
  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقات الإحصائيات
          _buildStatsCards(),
          const SizedBox(height: 24),

          // الإجراءات السريعة
          _buildQuickActions(),
          const SizedBox(height: 24),

          // الوثائق الحديثة
          _buildRecentDocuments(),
        ],
      ),
    );
  }

  /// تخطيط الجهاز اللوحي
  Widget _buildTabletLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقات الإحصائيات
          _buildStatsCards(),
          const SizedBox(height: 24),

          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الإجراءات السريعة
              Expanded(
                flex: 1,
                child: _buildQuickActions(),
              ),
              const SizedBox(width: 16),

              // الوثائق الحديثة
              Expanded(
                flex: 2,
                child: _buildRecentDocuments(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// تخطيط الحاسوب
  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقات الإحصائيات
          _buildStatsCards(),
          const SizedBox(height: 32),

          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الإجراءات السريعة
              Expanded(
                flex: 1,
                child: _buildQuickActions(),
              ),
              const SizedBox(width: 24),

              // الوثائق الحديثة
              Expanded(
                flex: 2,
                child: _buildRecentDocuments(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatsCards() {
    return Obx(() {
      final totalDocuments = _documentsController.allDocuments.length;
      final totalCategories = _categoriesController.allCategories.length;
      final totalTags = _tagsController.allTags.length;

      return GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: ResponsiveHelper.isSmallScreen(context) ? 1 : 3,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 2.5,
        children: [
          _buildStatCard(
            title: 'إجمالي الوثائق',
            value: totalDocuments.toString(),
            icon: Icons.description,
            color: AppColors.primary,
          ),
          _buildStatCard(
            title: 'التصنيفات',
            value: totalCategories.toString(),
            icon: Icons.folder,
            color: AppColors.primary,
          ),
          _buildStatCard(
            title: 'الوسوم',
            value: totalTags.toString(),
            icon: Icons.local_offer,
            color: AppColors.primary,
          ),
        ],
      );
    });
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    value,
                    style: AppStyles.headline6.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  Text(
                    title,
                    style: AppStyles.caption,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإجراءات السريعة',
              style: AppStyles.headline6,
            ),
            const SizedBox(height: 16),
            _buildQuickActionButton(
              title: 'تصفح الوثائق',
              icon: Icons.search,
              onTap: () => Get.to(() => const DocumentBrowserScreen()),
            ),
            const SizedBox(height: 8),
            _buildQuickActionButton(
              title: 'رفع وثيقة جديدة',
              icon: Icons.upload_file,
              onTap: () => Get.to(() => const DocumentUploadScreen()),
            ),
            const SizedBox(height: 8),
            _buildQuickActionButton(
              title: 'إدارة التصنيفات',
              icon: Icons.folder_open,
              onTap: () => Get.to(() => const CategoryManagementScreen()),
            ),
            const SizedBox(height: 8),
            _buildQuickActionButton(
              title: 'إدارة الوسوم',
              icon: Icons.local_offer,
              onTap: () => Get.to(() => const TagManagementScreen()),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر إجراء سريع
  Widget _buildQuickActionButton({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: AppColors.primary),
            const SizedBox(width: 12),
            Text(title, style: AppStyles.subtitle1),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الوثائق الحديثة
  Widget _buildRecentDocuments() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الوثائق الحديثة',
              style: AppStyles.headline6,
            ),
            const SizedBox(height: 16),
            Obx(() {
              if (_documentsController.allDocuments.isEmpty) {
                return const Center(
                  child: Text('لا توجد وثائق'),
                );
              }

              // أخذ أحدث 5 وثائق
              final recentDocs = _documentsController.allDocuments.take(5).toList();

              return ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: recentDocs.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final document = recentDocs[index];
                  return ListTile(
                    leading: const Icon(Icons.description),
                    title: Text(document.title),
                    subtitle: document.description != null
                        ? Text(
                            document.description!,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          )
                        : null,
                    onTap: () {
                      Get.to(() => DocumentBrowserScreen(
                        initialDocumentId: document.id,
                      ));
                    },
                  );
                },
              );
            }),
          ],
        ),
      ),
    );
  }
}
