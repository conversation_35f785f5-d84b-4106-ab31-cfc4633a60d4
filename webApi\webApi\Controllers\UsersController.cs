﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Attributes;

namespace webApi.Controllers
{
    /// <summary>
    /// Controller for managing users in the task management system
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    [Authorize] // يتطلب المصادقة لجميع العمليات
    public class UsersController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public UsersController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع المستخدمين مع دعم Pagination والبحث
        /// </summary>
        /// <param name="page">رقم الصفحة (افتراضي: 1)</param>
        /// <param name="pageSize">حجم الصفحة (افتراضي: 50)</param>
        /// <param name="search">نص البحث</param>
        /// <param name="searchQuery">نص البحث (بديل)</param>
        /// <param name="orderBy">حقل الترتيب</param>
        /// <param name="sortBy">حقل الترتيب (بديل)</param>
        /// <param name="orderDirection">اتجاه الترتيب (ASC/DESC)</param>
        /// <param name="sortOrder">اتجاه الترتيب (بديل)</param>
        /// <param name="role">فلتر حسب الدور</param>
        /// <param name="departmentId">فلتر حسب القسم</param>
        /// <param name="isActive">فلتر حسب الحالة النشطة</param>
        /// <returns>قائمة مقسمة من المستخدمين</returns>
        /// <response code="200">إرجاع قائمة المستخدمين مع معلومات Pagination</response>
        [HttpGet]
        [SupervisorOrAbove] // يتطلب صلاحية مشرف أو أعلى
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetUsers(
            int page = 1,
            int pageSize = 50,
            string? search = null,
            string? searchQuery = null,
            string? orderBy = null,
            string? sortBy = null,
            string orderDirection = "ASC",
            string? sortOrder = null,
            int? role = null,
            int? departmentId = null,
            bool? isActive = null)
        {
            try
            {
                // توحيد معاملات البحث والترتيب
                var searchTerm = search ?? searchQuery;
                var sortField = orderBy ?? sortBy ?? "Name";
                var sortDir = orderDirection ?? sortOrder ?? "ASC";

                // بناء الاستعلام الأساسي
                var query = _context.Users
                    .Include(u => u.Department)
                    .Include(u => u.RoleNavigation)
                    .Where(u => !u.IsDeleted);

                // تطبيق البحث
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(u =>
                        u.Name.Contains(searchTerm) ||
                        u.Email.Contains(searchTerm) ||
                        (u.Username != null && u.Username.Contains(searchTerm)) ||
                        (u.FirstName != null && u.FirstName.Contains(searchTerm)) ||
                        (u.LastName != null && u.LastName.Contains(searchTerm)) ||
                        (u.Department != null && u.Department.Name.Contains(searchTerm)));
                }

                // تطبيق الفلاتر
                if (role.HasValue)
                    query = query.Where(u => u.Role == role.Value);

                if (departmentId.HasValue)
                    query = query.Where(u => u.DepartmentId == departmentId.Value);

                if (isActive.HasValue)
                    query = query.Where(u => u.IsActive == isActive.Value);

                // حساب إجمالي السجلات
                var totalRecords = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

                // تطبيق الترتيب
                query = ApplyUserSorting(query, sortField, sortDir);

                // تطبيق Pagination
                var users = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // إرجاع النتيجة بتنسيق موحد
                var result = new
                {
                    data = users,
                    totalRecords = totalRecords,
                    totalPages = totalPages,
                    currentPage = page,
                    pageSize = pageSize,
                    hasNextPage = page < totalPages,
                    hasPreviousPage = page > 1
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب المستخدمين",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get a specific user by ID
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User details</returns>
        /// <response code="200">Returns the user</response>
        /// <response code="404">If the user is not found</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<User>> GetUser(int id)
        {
            var user = await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .FirstOrDefaultAsync(u => u.Id == id && !u.IsDeleted);

            if (user == null)
            {
                return NotFound();
            }

            return user;
        }

        /// <summary>
        /// Update a user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="user">Updated user data</param>
        /// <returns>No content</returns>
        /// <response code="204">User updated successfully</response>
        /// <response code="400">Invalid request</response>
        /// <response code="404">User not found</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutUser(int id, User user)
        {
            if (id != user.Id)
            {
                return BadRequest();
            }

            _context.Entry(user).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!UserExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// Create a new user
        /// </summary>
        /// <param name="user">User data</param>
        /// <returns>Created user</returns>
        /// <response code="201">User created successfully</response>
        /// <response code="400">Invalid request</response>
        [HttpPost]
        [ManagerOrAbove] // يتطلب صلاحية مدير أو أعلى
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<User>> PostUser(User user)
        {
            user.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            user.IsActive = true;
            user.IsDeleted = false;

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetUser", new { id = user.Id }, user);
        }

        /// <summary>
        /// Delete a user (soft delete)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>No content</returns>
        /// <response code="204">User deleted successfully</response>
        /// <response code="404">User not found</response>
        [HttpDelete("{id}")]
        [AdminOnly] // يتطلب صلاحية مدير عام فقط
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // Soft delete instead of hard delete
            user.IsDeleted = true;
            user.IsActive = false;
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// Get users by department
        /// </summary>
        /// <param name="departmentId">Department ID</param>
        /// <returns>List of users in the department</returns>
        /// <response code="200">Returns the list of users</response>
        [HttpGet("department/{departmentId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<User>>> GetUsersByDepartment(int departmentId)
        {
            return await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .Where(u => u.DepartmentId == departmentId && !u.IsDeleted && u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
        }

        /// <summary>
        /// Get users by role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>List of users with the specified role</returns>
        /// <response code="200">Returns the list of users</response>
        [HttpGet("role/{roleId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<User>>> GetUsersByRole(int roleId)
        {
            return await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .Where(u => u.Role == roleId && !u.IsDeleted && u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
        }

        /// <summary>
        /// Search users by name or email
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>List of matching users</returns>
        /// <response code="200">Returns the list of matching users</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<User>>> SearchUsers([FromQuery] string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                // إرجاع جميع المستخدمين النشطين بدون pagination
                return await _context.Users
                    .Include(u => u.Department)
                    .Include(u => u.RoleNavigation)
                    .Where(u => !u.IsDeleted && u.IsActive)
                    .OrderBy(u => u.Name)
                    .ToListAsync();
            }

            return await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .Where(u => (u.Name.Contains(searchTerm) ||
                           u.Email.Contains(searchTerm) ||
                           (u.Username != null && u.Username.Contains(searchTerm))) &&
                           !u.IsDeleted && u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
        }

        private bool UserExists(int id)
        {
            return _context.Users.Any(e => e.Id == id && !e.IsDeleted);
        }

        /// <summary>
        /// تطبيق الترتيب على استعلام المستخدمين
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="sortField">حقل الترتيب</param>
        /// <param name="sortDirection">اتجاه الترتيب</param>
        /// <returns>الاستعلام مع الترتيب المطبق</returns>
        private IQueryable<User> ApplyUserSorting(IQueryable<User> query, string sortField, string sortDirection)
        {
            var isDescending = sortDirection.ToUpper() == "DESC";

            return sortField.ToLower() switch
            {
                "id" => isDescending ? query.OrderByDescending(u => u.Id) : query.OrderBy(u => u.Id),
                "name" => isDescending ? query.OrderByDescending(u => u.Name) : query.OrderBy(u => u.Name),
                "firstname" => isDescending ? query.OrderByDescending(u => u.FirstName) : query.OrderBy(u => u.FirstName),
                "lastname" => isDescending ? query.OrderByDescending(u => u.LastName) : query.OrderBy(u => u.LastName),
                "email" => isDescending ? query.OrderByDescending(u => u.Email) : query.OrderBy(u => u.Email),
                "username" => isDescending ? query.OrderByDescending(u => u.Username) : query.OrderBy(u => u.Username),
                "role" => isDescending ? query.OrderByDescending(u => u.Role) : query.OrderBy(u => u.Role),
                "departmentid" => isDescending ? query.OrderByDescending(u => u.DepartmentId) : query.OrderBy(u => u.DepartmentId),
                "isactive" => isDescending ? query.OrderByDescending(u => u.IsActive) : query.OrderBy(u => u.IsActive),
                "createdat" => isDescending ? query.OrderByDescending(u => u.CreatedAt) : query.OrderBy(u => u.CreatedAt),
                _ => query.OrderBy(u => u.Name) // الترتيب الافتراضي
            };
        }
    }
}
