import 'package:flutter/material.dart';
import '../enums/task_enums.dart';
import '../models/task_models.dart';
import '../enums/task_enums.dart' as task_enums;

import '../models/user_model.dart';
import '../constants/app_colors.dart';
import '../utils/date_formatter.dart';

/// مساعدات لشاشة تفاصيل المهمة
class TaskDetailHelpers {
  
  /// تحويل معرف الحالة إلى TaskStatus enum
  static task_enums.TaskStatus getTaskStatusFromId(int statusId) {
    return task_enums.TaskStatus.fromId(statusId);
  }

  /// تحويل معرف الأولوية إلى TaskPriority enum
  static task_enums.TaskPriority getTaskPriorityFromLevel(int priorityLevel) {
    return TaskPriority.fromLevel(priorityLevel);
  }

  /// الحصول على أيقونة الحالة
  static IconData getStatusIcon(int statusId) {
    final status = getTaskStatusFromId(statusId);
    switch (status) {
      case task_enums.TaskStatus.pending:
        return Icons.schedule;
      case task_enums.TaskStatus.inProgress:
        return Icons.play_circle;
      case task_enums.TaskStatus.waitingForInfo:
        return Icons.info_outline;
      case task_enums.TaskStatus.completed:
        return Icons.check_circle;
      case task_enums.TaskStatus.cancelled:
        return Icons.cancel;
      case task_enums.TaskStatus.news:
        return Icons.fiber_new;
      default:
        return Icons.help_outline;
    }
  }

  /// الحصول على لون الحالة
  static Color getStatusColor(int statusId) {
    final status = getTaskStatusFromId(statusId);
    switch (status) {
      case task_enums.TaskStatus.pending:
        return Colors.orange;
      case task_enums.TaskStatus.inProgress:
        return Colors.blue;
      case task_enums.TaskStatus.waitingForInfo:
        return Colors.amber;
      case task_enums.TaskStatus.completed:
        return Colors.green;
      case task_enums.TaskStatus.cancelled:
        return Colors.red;
      case task_enums.TaskStatus.news:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على نص الحالة
  static String getStatusText(int statusId) {
    final status = getTaskStatusFromId(statusId);
    return status.displayNameAr;
  }

  /// الحصول على نص الأولوية
  static String getPriorityText(int priorityLevel) {
    final priority = getTaskPriorityFromLevel(priorityLevel);
    return priority.displayNameAr;
  }

  /// الحصول على لون الأولوية
  static Color getPriorityColor(int priorityLevel) {
    final priority = getTaskPriorityFromLevel(priorityLevel);
    switch (priority) {
      case task_enums.TaskPriority.low:
        return Colors.green;
      case task_enums.TaskPriority.medium:
        return Colors.orange;
      case task_enums.TaskPriority.high:
        return Colors.red;
      case task_enums.TaskPriority.urgent:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// تحويل Unix timestamp إلى DateTime
  static DateTime timestampToDateTime(int timestamp) {
    return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
  }

  /// تحويل DateTime إلى Unix timestamp
  static int dateTimeToTimestamp(DateTime dateTime) {
    return dateTime.millisecondsSinceEpoch ~/ 1000;
  }

  /// تنسيق التاريخ من Unix timestamp
  static String formatTimestamp(int timestamp) {
    final dateTime = timestampToDateTime(timestamp);
    return DateFormatter.formatDateTime(dateTime);
  }

  /// تنسيق التاريخ القصير من Unix timestamp
  static String formatShortTimestamp(int timestamp) {
    final dateTime = timestampToDateTime(timestamp);
    return DateFormatter.formatShortDate(dateTime);
  }

  /// التحقق من انتهاء موعد المهمة
  static bool isTaskOverdue(Task task) {
    if (task.dueDate == null) return false;
    final dueDateTime = timestampToDateTime(task.dueDate!);
    return DateTime.now().isAfter(dueDateTime) && task.status != 4; // 4 = completed
  }

  /// حساب نسبة التقدم
  static double calculateProgress(int completionPercentage) {
    return (completionPercentage / 100.0).clamp(0.0, 1.0);
  }

  /// تنسيق الوقت المقدر/الفعلي
  static String formatTime(int? timeInMinutes) {
    if (timeInMinutes == null) return 'غير محدد';
    
    if (timeInMinutes < 60) {
      return '$timeInMinutes دقيقة';
    } else {
      final hours = timeInMinutes ~/ 60;
      final minutes = timeInMinutes % 60;
      if (minutes == 0) {
        return '$hours ساعة';
      } else {
        return '$hours ساعة و $minutes دقيقة';
      }
    }
  }

  /// التحقق من صحة الوصف
  static String getValidDescription(String? description) {
    return description?.isNotEmpty == true ? description! : 'لا يوجد وصف';
  }

  /// تحويل معرف المستخدم إلى نص
  static String userIdToString(int userId) {
    return userId.toString();
  }

  /// تحويل نص إلى معرف مستخدم
  static int stringToUserId(String userIdString) {
    return int.tryParse(userIdString) ?? 0;
  }

  /// إنشاء مستخدم افتراضي للمعاينة
  static User createDefaultUser(int id, String name) {
    return User(
      id: id,
      name: name,
      email: '$<EMAIL>',
      role: UserRole.user,
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
    );
  }

  /// إنشاء مستخدم افتراضي من معرف نصي
  static User createDefaultUserFromString(String userIdString, String name) {
    final id = stringToUserId(userIdString);
    return createDefaultUser(id, name);
  }

  /// تحويل قائمة معرفات نصية إلى قائمة معرفات رقمية
  static List<int> stringIdsToIntIds(List<String> stringIds) {
    return stringIds.map((id) => stringToUserId(id)).toList();
  }

  /// تحويل قائمة معرفات رقمية إلى قائمة معرفات نصية
  static List<String> intIdsToStringIds(List<int> intIds) {
    return intIds.map((id) => userIdToString(id)).toList();
  }

  /// الحصول على لون الحالة من AppColors
  static Color getAppStatusColor(int statusId) {
    try {
      return getStatusColor(statusId);
    } catch (e) {
      return AppColors.textSecondary;
    }
  }

  /// الحصول على لون الأولوية من AppColors
  static Color getAppPriorityColor(int priorityLevel) {
    try {
      return getPriorityColor(priorityLevel);
    } catch (e) {
      return AppColors.textSecondary;
    }
  }

  /// تحويل نوع محتوى الرسالة إلى أيقونة
  static IconData getMessageContentTypeIcon(int contentType) {
    switch (contentType) {
      case 1: // text
        return Icons.message;
      case 2: // image
        return Icons.image;
      case 3: // file
        return Icons.attach_file;
      case 4: // voice
        return Icons.mic;
      case 5: // video
        return Icons.videocam;
      case 6: // location
        return Icons.location_on;
      default:
        return Icons.help_outline;
    }
  }

  /// التحقق من صحة معرف المهمة
  static bool isValidTaskId(String taskId) {
    final id = int.tryParse(taskId);
    return id != null && id > 0;
  }

  /// تحويل معرف المهمة من نص إلى رقم
  static int parseTaskId(String taskId) {
    return int.tryParse(taskId) ?? 0;
  }

  /// تحويل معرف المهمة من رقم إلى نص
  static String taskIdToString(int taskId) {
    return taskId.toString();
  }

  /// حساب الوقت المتبقي للمهمة
  static String getTimeRemaining(Task task) {
    if (task.dueDate == null) return 'غير محدد';
    
    final dueDateTime = timestampToDateTime(task.dueDate!);
    final now = DateTime.now();
    
    if (now.isAfter(dueDateTime)) {
      final overdue = now.difference(dueDateTime);
      if (overdue.inDays > 0) {
        return 'متأخر ${overdue.inDays} يوم';
      } else if (overdue.inHours > 0) {
        return 'متأخر ${overdue.inHours} ساعة';
      } else {
        return 'متأخر ${overdue.inMinutes} دقيقة';
      }
    } else {
      final remaining = dueDateTime.difference(now);
      if (remaining.inDays > 0) {
        return 'باقي ${remaining.inDays} يوم';
      } else if (remaining.inHours > 0) {
        return 'باقي ${remaining.inHours} ساعة';
      } else {
        return 'باقي ${remaining.inMinutes} دقيقة';
      }
    }
  }

  /// التحقق من إمكانية تعديل المهمة
  static bool canEditTask(Task task, User currentUser) {
    // يمكن للمنشئ أو المكلف أو المدير تعديل المهمة
    return task.creatorId == currentUser.id ||
           task.assigneeId == currentUser.id ||
           currentUser.role.isManagerOrAbove;
  }

  /// التحقق من إمكانية حذف المهمة
  static bool canDeleteTask(Task task, User currentUser) {
    // يمكن للمنشئ أو المدير حذف المهمة
    return task.creatorId == currentUser.id ||
           currentUser.role.isManagerOrAbove;
  }
}
