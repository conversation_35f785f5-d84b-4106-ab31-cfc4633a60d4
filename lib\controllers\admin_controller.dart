import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/system_models.dart';
import '../models/activity_log_models.dart';
import '../models/system_setting_models.dart' as setting_models;
import '../services/api/user_api_service.dart';
import '../services/api/activity_logs_api_service.dart';
import '../services/api/backups_api_service.dart';
import '../services/api/system_settings_api_service.dart';
import '../services/api/api_service.dart';



/// متحكم الإدارة العامة
/// 
/// يوفر وظائف إدارية شاملة للنظام بما في ذلك إدارة المستخدمين والسجلات والنسخ الاحتياطية
class AdminController extends GetxController {
  // الخدمات
  final UserApiService _userApiService = UserApiService();
  final ActivityLogsApiService _activityLogsApiService = ActivityLogsApiService();
  final BackupsApiService _backupsApiService = BackupsApiService();
  final SystemSettingsApiService _systemSettingsApiService = SystemSettingsApiService();
  final ApiService _apiService = ApiService();

  // قوائم البيانات
  final RxList<User> _users = <User>[].obs;
  final RxList<SystemLog> _systemLogs = <SystemLog>[].obs;
  final RxList<ActivityLog> _activityLogs = <ActivityLog>[].obs;
  final RxList<Backup> _backups = <Backup>[].obs;
  final RxList<setting_models.SystemSetting> _systemSettings = <setting_models.SystemSetting>[].obs;

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المستخدم المحدد لإدارة الصلاحيات
  final Rx<User?> selectedPermissionUser = Rx<User?>(null);

  // قائمة مؤقتة للصلاحيات (سيتم استبدالها بنظام صلاحيات حقيقي)
  final RxList<dynamic> _permissions = <dynamic>[].obs;

  // Getters للبيانات
  List<User> get users => _users;
  List<SystemLog> get systemLogs => _systemLogs;
  List<ActivityLog> get logs => _activityLogs;
  List<Backup> get backups => _backups;
  List<setting_models.SystemSetting> get systemSettings => _systemSettings;
  List<dynamic> get permissions => _permissions;

  // Getters للحالة
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  RxBool get isLoadingObs => _isLoading;
  RxString get errorObs => _error;

  @override
  void onInit() {
    super.onInit();
    debugPrint('تم تهيئة AdminController');
  }

  /// تحميل جميع المستخدمين
  Future<void> loadAllUsers() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final users = await _userApiService.getAllUsers();
      _users.assignAll(users);
      debugPrint('تم تحميل ${users.length} مستخدم');
    } catch (e) {
      _error.value = 'خطأ في تحميل المستخدمين: $e';
      debugPrint('خطأ في تحميل المستخدمين: $e');

      // في حالة فشل التحميل، نضع قائمة فارغة لتجنب الأخطاء
      _users.clear();
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل سجلات النظام
  Future<void> loadSystemLogs() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // محاولة تحميل سجلات النظام من API
      final response = await _apiService.get('/api/SystemLogs');
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as List;
        final logs = data.map((json) => SystemLog.fromJson(json)).toList();
        _systemLogs.assignAll(logs);
        debugPrint('تم تحميل ${logs.length} سجل نظام');
      } else {
        // في حالة عدم توفر الخدمة، إنشاء سجلات وهمية للاختبار
        _systemLogs.assignAll(_createMockSystemLogs());
        debugPrint('تم تحميل سجلات النظام الوهمية للاختبار');
      }
    } catch (e) {
      // في حالة الخطأ، إنشاء سجلات وهمية
      _systemLogs.assignAll(_createMockSystemLogs());
      _error.value = 'تم تحميل البيانات الوهمية بسبب: $e';
      debugPrint('خطأ في تحميل سجلات النظام، تم استخدام البيانات الوهمية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء سجلات نظام وهمية للاختبار
  List<SystemLog> _createMockSystemLogs() {
    final now = DateTime.now();
    return [
      SystemLog(
        id: 1,
        logType: 'system',
        logLevel: 'info',
        message: 'تم تسجيل دخول المستخدم',
        createdAt: now.subtract(const Duration(minutes: 5)).millisecondsSinceEpoch ~/ 1000,
        userId: 1,
        details: 'تم تسجيل دخول المستخدم بنجاح من AuthController',
      ),
      SystemLog(
        id: 2,
        logType: 'security',
        logLevel: 'warning',
        message: 'محاولة وصول غير مصرح بها',
        createdAt: now.subtract(const Duration(minutes: 15)).millisecondsSinceEpoch ~/ 1000,
        userId: null,
        details: 'محاولة وصول غير مصرح بها من SecurityService',
      ),
      SystemLog(
        id: 3,
        logType: 'system',
        logLevel: 'error',
        message: 'فشل في الاتصال بقاعدة البيانات',
        createdAt: now.subtract(const Duration(hours: 1)).millisecondsSinceEpoch ~/ 1000,
        userId: null,
        details: 'فشل في الاتصال بقاعدة البيانات من DatabaseService',
      ),
    ];
  }

  /// تحميل سجلات النشاط
  Future<void> loadActivityLogs() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final logs = await _activityLogsApiService.getAllActivityLogs();
      _activityLogs.assignAll(logs);
      debugPrint('تم تحميل ${logs.length} سجل نشاط');
    } catch (e) {
      _error.value = 'خطأ في تحميل سجلات النشاط: $e';
      debugPrint('خطأ في تحميل سجلات النشاط: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل النسخ الاحتياطية
  Future<void> loadBackups() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final backups = await _backupsApiService.getAllBackups();
      _backups.assignAll(backups);
      debugPrint('تم تحميل ${backups.length} نسخة احتياطية');
    } catch (e) {
      _error.value = 'خطأ في تحميل النسخ الاحتياطية: $e';
      debugPrint('خطأ في تحميل النسخ الاحتياطية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل إعدادات النظام
  Future<void> loadSystemSettings() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final settings = await _systemSettingsApiService.getAllSettings();
      _systemSettings.assignAll(settings);
      debugPrint('تم تحميل ${settings.length} إعداد نظام');
    } catch (e) {
      _error.value = 'خطأ في تحميل إعدادات النظام: $e';
      debugPrint('خطأ في تحميل إعدادات النظام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء مستخدم جديد
  Future<bool> createUser(User user) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newUser = await _userApiService.createUser(user);
      if (newUser != null) {
        _users.add(newUser);
        debugPrint('تم إنشاء مستخدم جديد: ${newUser.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء المستخدم: $e';
      debugPrint('خطأ في إنشاء المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث مستخدم
  Future<bool> updateUser(User user) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedUser = await _userApiService.updateUser(user);
      if (updatedUser != null) {
        final index = _users.indexWhere((u) => u.id == user.id);
        if (index != -1) {
          _users[index] = updatedUser;
        }
        debugPrint('تم تحديث المستخدم: ${updatedUser.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث المستخدم: $e';
      debugPrint('خطأ في تحديث المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(int userId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _userApiService.deleteUser(userId);
      _users.removeWhere((u) => u.id == userId);
      debugPrint('تم حذف المستخدم: $userId');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف المستخدم: $e';
      debugPrint('خطأ في حذف المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تبديل حالة نشاط المستخدم
  Future<bool> toggleUserActive(int userId, bool isActive) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _userApiService.updateUserActiveStatus(userId, isActive);
      if (success) {
        final index = _users.indexWhere((u) => u.id == userId);
        if (index != -1) {
          _users[index] = _users[index].copyWith(isActive: isActive);
        }
        debugPrint('تم تحديث حالة نشاط المستخدم $userId إلى: $isActive');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث حالة نشاط المستخدم: $e';
      debugPrint('خطأ في تحديث حالة نشاط المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء نسخة احتياطية
  Future<bool> createBackup(String description) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final backup = await _backupsApiService.createBackup(description);
      _backups.insert(0, backup);
      debugPrint('تم إنشاء نسخة احتياطية: ${backup.fileName}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء النسخة الاحتياطية: $e';
      debugPrint('خطأ في إنشاء النسخة الاحتياطية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// استعادة نسخة احتياطية
  Future<bool> restoreBackup(int backupId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _backupsApiService.restoreBackup(backupId);
      debugPrint('تم استعادة النسخة الاحتياطية: $backupId');
      return true;
    } catch (e) {
      _error.value = 'خطأ في استعادة النسخة الاحتياطية: $e';
      debugPrint('خطأ في استعادة النسخة الاحتياطية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث إعداد نظام
  Future<bool> updateSystemSetting(String key, String value) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _systemSettingsApiService.updateSettingValue(key, value);
      final index = _systemSettings.indexWhere((s) => s.settingKey == key);
      if (index != -1) {
        _systemSettings[index] = _systemSettings[index].copyWith(settingValue: value);
      }
      debugPrint('تم تحديث إعداد النظام: $key = $value');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث إعداد النظام: $e';
      debugPrint('خطأ في تحديث إعداد النظام: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف نسخة احتياطية
  Future<bool> deleteBackup(int backupId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _backupsApiService.deleteBackup(backupId);
      _backups.removeWhere((b) => b.id == backupId);
      debugPrint('تم حذف النسخة الاحتياطية: $backupId');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف النسخة الاحتياطية: $e';
      debugPrint('خطأ في حذف النسخة الاحتياطية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تصدير جدول إلى CSV
  Future<String?> exportTableToCsv(String tableName, {String? description}) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // تنفيذ محلي مؤقت - في التطبيق الحقيقي سيتم استدعاء API
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${tableName}_export_$timestamp.csv';

      debugPrint('تم تصدير الجدول $tableName إلى: $fileName');
      if (description != null) {
        debugPrint('الوصف: $description');
      }

      return fileName;
    } catch (e) {
      _error.value = 'خطأ في تصدير الجدول: $e';
      debugPrint('خطأ في تصدير الجدول: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// مسح الأخطاء
  void clearError() {
    _error.value = '';
  }

  /// إعادة تحميل جميع البيانات
  Future<void> refreshAllData() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // تحميل البيانات بشكل متوازي مع معالجة الأخطاء
      await Future.wait([
        loadAllUsers().catchError((e) {
          debugPrint('خطأ في تحميل المستخدمين: $e');
          return null;
        }),
        loadSystemLogs().catchError((e) {
          debugPrint('خطأ في تحميل سجلات النظام: $e');
          return null;
        }),
        loadActivityLogs().catchError((e) {
          debugPrint('خطأ في تحميل سجلات النشاط: $e');
          return null;
        }),
        loadBackups().catchError((e) {
          debugPrint('خطأ في تحميل النسخ الاحتياطية: $e');
          return null;
        }),
        loadSystemSettings().catchError((e) {
          debugPrint('خطأ في تحميل إعدادات النظام: $e');
          return null;
        }),
      ]);

      debugPrint('تم تحديث جميع البيانات الإدارية');
    } catch (e) {
      _error.value = 'خطأ في تحديث البيانات: $e';
      debugPrint('خطأ في تحديث البيانات الإدارية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل البيانات الأساسية فقط (للتحميل السريع)
  Future<void> loadEssentialData() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // تحميل البيانات الأساسية فقط
      await Future.wait([
        loadAllUsers(),
        loadSystemSettings(),
      ]);
      debugPrint('تم تحميل البيانات الأساسية');
    } catch (e) {
      _error.value = 'خطأ في تحميل البيانات الأساسية: $e';
      debugPrint('خطأ في تحميل البيانات الأساسية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل صلاحيات المستخدم (مؤقت - سيتم تطوير نظام صلاحيات حقيقي)
  Future<void> loadUserPermissions(dynamic userId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // مؤقت - إنشاء صلاحيات وهمية للاختبار
      _permissions.clear();
      debugPrint('تم تحميل صلاحيات المستخدم: $userId (مؤقت)');
    } catch (e) {
      _error.value = 'خطأ في تحميل صلاحيات المستخدم: $e';
      debugPrint('خطأ في تحميل صلاحيات المستخدم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// منح صلاحية (مؤقت - سيتم تطوير نظام صلاحيات حقيقي)
  Future<void> grantPermission(dynamic userId, dynamic type, dynamic scope, {String? description}) async {
    try {
      debugPrint('تم منح صلاحية للمستخدم $userId: $type - $scope');
      if (description != null) {
        debugPrint('الوصف: $description');
      }
    } catch (e) {
      debugPrint('خطأ في منح الصلاحية: $e');
      rethrow;
    }
  }

  /// إلغاء صلاحية (مؤقت - سيتم تطوير نظام صلاحيات حقيقي)
  Future<void> revokePermission(dynamic userId, dynamic type, dynamic scope, {String? description}) async {
    try {
      debugPrint('تم إلغاء صلاحية للمستخدم $userId: $type - $scope');
      if (description != null) {
        debugPrint('الوصف: $description');
      }
    } catch (e) {
      debugPrint('خطأ في إلغاء الصلاحية: $e');
      rethrow;
    }
  }

  /// الحصول على قيمة إعداد نظام
  Future<String?> getSettingValue(String key, {String? defaultValue}) async {
    try {
      // البحث في الإعدادات المحملة أولاً
      final setting = _systemSettings.firstWhereOrNull((s) => s.settingKey == key);
      if (setting != null) {
        return setting.settingValue;
      }

      // إذا لم توجد، تحميل الإعدادات أولاً
      await loadSystemSettings();
      final loadedSetting = _systemSettings.firstWhereOrNull((s) => s.settingKey == key);

      return loadedSetting?.settingValue ?? defaultValue;
    } catch (e) {
      debugPrint('خطأ في الحصول على قيمة الإعداد $key: $e');
      return defaultValue;
    }
  }

  /// حفظ إعداد نظام
  Future<bool> saveSetting(String key, String value, {String? description}) async {
    try {
      final success = await updateSystemSetting(key, value);
      if (success && description != null) {
        debugPrint('تم حفظ الإعداد $key: $description');
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حفظ الإعداد $key: $e');
      return false;
    }
  }

  /// إنشاء صلاحيات الأدوار الافتراضية
  Future<void> createDefaultRolePermissions() async {
    try {
      debugPrint('تم إنشاء صلاحيات الأدوار الافتراضية');
      // TODO: تنفيذ إنشاء صلاحيات الأدوار الافتراضية
    } catch (e) {
      debugPrint('خطأ في إنشاء صلاحيات الأدوار الافتراضية: $e');
      rethrow;
    }
  }

  /// إنشاء الإعدادات الافتراضية
  Future<void> createDefaultSettings() async {
    try {
      debugPrint('تم إنشاء الإعدادات الافتراضية');
      // TODO: تنفيذ إنشاء الإعدادات الافتراضية
    } catch (e) {
      debugPrint('خطأ في إنشاء الإعدادات الافتراضية: $e');
      rethrow;
    }
  }
}
