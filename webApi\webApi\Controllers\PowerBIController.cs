using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم Power BI
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class PowerBIController(TasksDbContext context) : ControllerBase
    {
        private readonly TasksDbContext _context = context;

        /// <summary>
        /// الحصول على تقارير المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة تقارير المستخدم</returns>
        [HttpGet("my-reports")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<object>>> GetMyReports([FromQuery] int userId = 1)
        {
            var reports = await _context.Reports
                .Where(r => r.CreatedBy == userId && !r.IsDeleted && r.ReportType == "PowerBI")
                .OrderByDescending(r => r.CreatedAt)
                .Select(r => new
                {
                    r.Id,
                    r.Title,
                    r.Description,
                    r.Parameters,
                    CreatedById = r.CreatedBy.ToString(),
                    CreatedAt = r.CreatedAt,
                    UpdatedAt = r.UpdatedAt,
                    IsPublic = r.IsPublic,
                    IsShared = false // TODO: تنفيذ منطق المشاركة
                })
                .ToListAsync();

            var result = reports.Select(r =>
            {
                var parameters = r.Parameters != null
                    ? JsonSerializer.Deserialize<Dictionary<string, object>>(r.Parameters)
                    : new Dictionary<string, object>();

                return new
                {
                    r.Id,
                    r.Title,
                    r.Description,
                    ChartType = parameters?.ContainsKey("chartType") == true ? parameters["chartType"]?.ToString() ?? "bar" : "bar",
                    r.CreatedById,
                    r.CreatedAt,
                    r.UpdatedAt,
                    TableName = parameters?.ContainsKey("tableName") == true ? parameters["tableName"]?.ToString() ?? "" : "",
                    ColumnNames = parameters?.ContainsKey("columnNames") == true ? parameters["columnNames"] ?? new string[0] : new string[0],
                    XAxisColumn = parameters?.ContainsKey("xAxisColumn") == true ? parameters["xAxisColumn"]?.ToString() ?? "" : "",
                    YAxisColumn = parameters?.ContainsKey("yAxisColumn") == true ? parameters["yAxisColumn"]?.ToString() ?? "" : "",
                    r.IsPublic,
                    r.IsShared
                };
            });

            return Ok(result);
        }

        /// <summary>
        /// الحصول على التقارير المشتركة
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة التقارير المشتركة</returns>
        [HttpGet("shared-reports")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<object>>> GetSharedReports([FromQuery] int userId = 1)
        {
            // TODO: تنفيذ منطق التقارير المشتركة
            var reports = await _context.Reports
                .Where(r => r.IsPublic && !r.IsDeleted && r.ReportType == "PowerBI")
                .OrderByDescending(r => r.CreatedAt)
                .Select(r => new
                {
                    r.Id,
                    r.Title,
                    r.Description,
                    r.Parameters,
                    CreatedById = r.CreatedBy.ToString(),
                    CreatedAt = r.CreatedAt,
                    UpdatedAt = r.UpdatedAt,
                    IsPublic = r.IsPublic,
                    IsShared = true
                })
                .ToListAsync();

            var result = reports.Select(r =>
            {
                var parameters = r.Parameters != null
                    ? JsonSerializer.Deserialize<Dictionary<string, object>>(r.Parameters)
                    : new Dictionary<string, object>();

                return new
                {
                    r.Id,
                    r.Title,
                    r.Description,
                    ChartType = parameters?.ContainsKey("chartType") == true ? parameters["chartType"]?.ToString() ?? "bar" : "bar",
                    r.CreatedById,
                    r.CreatedAt,
                    r.UpdatedAt,
                    r.IsPublic,
                    r.IsShared
                };
            });

            return Ok(result);
        }

        /// <summary>
        /// الحصول على تقرير محدد
        /// </summary>
        /// <param name="id">معرف التقرير</param>
        /// <returns>التقرير</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<object>> GetReport(int id)
        {
            var report = await _context.Reports
                .FirstOrDefaultAsync(r => r.Id == id && !r.IsDeleted);

            if (report == null)
            {
                return NotFound();
            }

            var parameters = report.Parameters != null
                ? JsonSerializer.Deserialize<Dictionary<string, object>>(report.Parameters)
                : new Dictionary<string, object>();

            var result = new
            {
                Id = report.Id.ToString(),
                report.Title,
                report.Description,
                ChartType = parameters?.ContainsKey("chartType") == true ? parameters["chartType"]?.ToString() ?? "bar" : "bar",
                CreatedById = report.CreatedBy.ToString(),
                CreatedAt = report.CreatedAt,
                UpdatedAt = report.UpdatedAt,
                TableName = parameters?.ContainsKey("tableName") == true ? parameters["tableName"]?.ToString() ?? "" : "",
                ColumnNames = parameters?.ContainsKey("columnNames") == true ? parameters["columnNames"] ?? new string[0] : new string[0],
                XAxisColumn = parameters?.ContainsKey("xAxisColumn") == true ? parameters["xAxisColumn"]?.ToString() ?? "" : "",
                YAxisColumn = parameters?.ContainsKey("yAxisColumn") == true ? parameters["yAxisColumn"]?.ToString() ?? "" : "",
                YAxisAggregateFunction = parameters?.ContainsKey("yAxisAggregateFunction") == true ? parameters["yAxisAggregateFunction"]?.ToString() ?? "none" : "none",
                SizeColumn = parameters?.ContainsKey("sizeColumn") == true ? parameters["sizeColumn"]?.ToString() : null,
                ColorColumn = parameters?.ContainsKey("colorColumn") == true ? parameters["colorColumn"]?.ToString() : null,
                FilterCriteria = parameters?.ContainsKey("filterCriteria") == true ? parameters["filterCriteria"]?.ToString() : null,
                RelatedTables = parameters?.ContainsKey("relatedTables") == true ? parameters["relatedTables"] : null,
                JoinConditions = parameters?.ContainsKey("joinConditions") == true ? parameters["joinConditions"] : null,
                JoinTypes = parameters?.ContainsKey("joinTypes") == true ? parameters["joinTypes"] : null,
                IsPublic = report.IsPublic,
                IsShared = false
            };

            return Ok(result);
        }

        /// <summary>
        /// إنشاء تقرير جديد
        /// </summary>
        /// <param name="reportData">بيانات التقرير</param>
        /// <returns>التقرير المُنشأ</returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<object>> CreateReport([FromBody] JsonElement reportData)
        {
            try
            {
                var parameters = new Dictionary<string, object>();
                
                if (reportData.TryGetProperty("chartType", out var chartType))
                    parameters["chartType"] = chartType.GetString() ?? "bar";
                if (reportData.TryGetProperty("tableName", out var tableName))
                    parameters["tableName"] = tableName.GetString() ?? "";
                if (reportData.TryGetProperty("columnNames", out var columnNames))
                    parameters["columnNames"] = columnNames.EnumerateArray().Select(x => x.GetString()).ToArray();
                if (reportData.TryGetProperty("xAxisColumn", out var xAxisColumn))
                    parameters["xAxisColumn"] = xAxisColumn.GetString() ?? "";
                if (reportData.TryGetProperty("yAxisColumn", out var yAxisColumn))
                    parameters["yAxisColumn"] = yAxisColumn.GetString() ?? "";
                if (reportData.TryGetProperty("yAxisAggregateFunction", out var yAxisAggregateFunction))
                    parameters["yAxisAggregateFunction"] = yAxisAggregateFunction.GetString() ?? "none";
                if (reportData.TryGetProperty("sizeColumn", out var sizeColumn))
                    parameters["sizeColumn"] = sizeColumn.GetString() ?? "";
                if (reportData.TryGetProperty("colorColumn", out var colorColumn))
                    parameters["colorColumn"] = colorColumn.GetString() ?? "";
                if (reportData.TryGetProperty("filterCriteria", out var filterCriteria))
                    parameters["filterCriteria"] = filterCriteria.GetString() ?? "";
                if (reportData.TryGetProperty("relatedTables", out var relatedTables))
                    parameters["relatedTables"] = relatedTables.EnumerateArray().Select(x => x.GetString()).ToArray();
                if (reportData.TryGetProperty("joinConditions", out var joinConditions))
                    parameters["joinConditions"] = joinConditions.EnumerateArray().Select(x => x.GetString()).ToArray();
                if (reportData.TryGetProperty("joinTypes", out var joinTypes))
                    parameters["joinTypes"] = joinTypes.EnumerateArray().Select(x => x.GetString()).ToArray();

                var report = new Report
                {
                    Title = reportData.GetProperty("title").GetString() ?? "",
                    Description = reportData.TryGetProperty("description", out var desc) ? desc.GetString() : null,
                    ReportType = "PowerBI",
                    Query = "", // سيتم إنشاؤه ديناميكياً
                    Parameters = JsonSerializer.Serialize(parameters),
                    CreatedBy = reportData.TryGetProperty("createdById", out var createdBy) 
                        ? int.Parse(createdBy.GetString() ?? "1") : 1,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    IsPublic = reportData.TryGetProperty("isPublic", out var isPublic) && isPublic.GetBoolean(),
                    IsDeleted = false
                };

                _context.Reports.Add(report);
                await _context.SaveChangesAsync();

                var result = new
                {
                    Id = report.Id.ToString(),
                    report.Title,
                    report.Description,
                    ChartType = parameters?.ContainsKey("chartType") == true ? parameters["chartType"]?.ToString() ?? "bar" : "bar",
                    CreatedById = report.CreatedBy.ToString(),
                    CreatedAt = report.CreatedAt,
                    UpdatedAt = report.UpdatedAt,
                    TableName = parameters?.ContainsKey("tableName") == true ? parameters["tableName"]?.ToString() ?? "" : "",
                    ColumnNames = parameters?.ContainsKey("columnNames") == true ? parameters["columnNames"] ?? new string[0] : new string[0],
                    XAxisColumn = parameters?.ContainsKey("xAxisColumn") == true ? parameters["xAxisColumn"]?.ToString() ?? "" : "",
                    YAxisColumn = parameters?.ContainsKey("yAxisColumn") == true ? parameters["yAxisColumn"]?.ToString() ?? "" : "",
                    YAxisAggregateFunction = parameters?.ContainsKey("yAxisAggregateFunction") == true ? parameters["yAxisAggregateFunction"]?.ToString() ?? "none" : "none",
                    SizeColumn = parameters?.ContainsKey("sizeColumn") == true ? parameters["sizeColumn"]?.ToString() : null,
                    ColorColumn = parameters?.ContainsKey("colorColumn") == true ? parameters["colorColumn"]?.ToString() : null,
                    FilterCriteria = parameters?.ContainsKey("filterCriteria") == true ? parameters["filterCriteria"]?.ToString() : null,
                    RelatedTables = parameters?.ContainsKey("relatedTables") == true ? parameters["relatedTables"] : null,
                    JoinConditions = parameters?.ContainsKey("joinConditions") == true ? parameters["joinConditions"] : null,
                    JoinTypes = parameters?.ContainsKey("joinTypes") == true ? parameters["joinTypes"] : null,
                    IsPublic = report.IsPublic,
                    IsShared = false
                };

                return CreatedAtAction(nameof(GetReport), new { id = report.Id }, result);
            }
            catch (Exception ex)
            {
                return BadRequest($"خطأ في إنشاء التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث تقرير
        /// </summary>
        /// <param name="id">معرف التقرير</param>
        /// <param name="reportData">بيانات التقرير المحدثة</param>
        /// <returns>التقرير المحدث</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<object>> UpdateReport(int id, [FromBody] JsonElement reportData)
        {
            try
            {
                var report = await _context.Reports.FindAsync(id);
                if (report == null || report.IsDeleted)
                {
                    return NotFound();
                }

                var parameters = new Dictionary<string, object>();
                
                if (reportData.TryGetProperty("chartType", out var chartType))
                    parameters["chartType"] = chartType.GetString() ?? "bar";
                if (reportData.TryGetProperty("tableName", out var tableName))
                    parameters["tableName"] = tableName.GetString() ?? "";
                if (reportData.TryGetProperty("columnNames", out var columnNames))
                    parameters["columnNames"] = columnNames.EnumerateArray().Select(x => x.GetString()).ToArray();
                if (reportData.TryGetProperty("xAxisColumn", out var xAxisColumn))
                    parameters["xAxisColumn"] = xAxisColumn.GetString() ?? "";
                if (reportData.TryGetProperty("yAxisColumn", out var yAxisColumn))
                    parameters["yAxisColumn"] = yAxisColumn.GetString() ?? "";
                if (reportData.TryGetProperty("yAxisAggregateFunction", out var yAxisAggregateFunction))
                    parameters["yAxisAggregateFunction"] = yAxisAggregateFunction.GetString() ?? "none";
                if (reportData.TryGetProperty("sizeColumn", out var sizeColumn))
                    parameters["sizeColumn"] = sizeColumn.GetString() ?? "";
                if (reportData.TryGetProperty("colorColumn", out var colorColumn))
                    parameters["colorColumn"] = colorColumn.GetString() ?? "";
                if (reportData.TryGetProperty("filterCriteria", out var filterCriteria))
                    parameters["filterCriteria"] = filterCriteria.GetString() ?? "";

                report.Title = reportData.GetProperty("title").GetString() ?? report.Title;
                if (reportData.TryGetProperty("description", out var desc))
                    report.Description = desc.GetString();
                report.Parameters = JsonSerializer.Serialize(parameters);
                report.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                if (reportData.TryGetProperty("isPublic", out var isPublic))
                    report.IsPublic = isPublic.GetBoolean();

                await _context.SaveChangesAsync();

                var result = new
                {
                    Id = report.Id.ToString(),
                    report.Title,
                    report.Description,
                    ChartType = parameters?.ContainsKey("chartType") == true ? parameters["chartType"]?.ToString() ?? "bar" : "bar",
                    CreatedById = report.CreatedBy.ToString(),
                    CreatedAt = report.CreatedAt,
                    UpdatedAt = report.UpdatedAt,
                    TableName = parameters?.ContainsKey("tableName") == true ? parameters["tableName"]?.ToString() ?? "" : "",
                    ColumnNames = parameters?.ContainsKey("columnNames") == true ? parameters["columnNames"] ?? new string[0] : new string[0],
                    XAxisColumn = parameters?.ContainsKey("xAxisColumn") == true ? parameters["xAxisColumn"]?.ToString() ?? "" : "",
                    YAxisColumn = parameters?.ContainsKey("yAxisColumn") == true ? parameters["yAxisColumn"]?.ToString() ?? "" : "",
                    IsPublic = report.IsPublic,
                    IsShared = false
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"خطأ في تحديث التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف تقرير
        /// </summary>
        /// <param name="id">معرف التقرير</param>
        /// <returns>نتيجة الحذف</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteReport(int id)
        {
            var report = await _context.Reports.FindAsync(id);
            if (report == null || report.IsDeleted)
            {
                return NotFound();
            }

            report.IsDeleted = true;
            report.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// الحصول على الجداول المتاحة
        /// </summary>
        /// <returns>قائمة الجداول</returns>
        [HttpGet("tables")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<IEnumerable<string>> GetAvailableTables()
        {
            var tables = new List<string>
            {
                "tasks",
                "users",
                "departments",
                "task_types",
                "task_statuses",
                "task_priorities",
                "task_comments",
                "attachments",
                "calendar_events",
                "notifications",
                "activity_logs"
            };

            return Ok(tables);
        }

        /// <summary>
        /// الحصول على أعمدة جدول محدد
        /// </summary>
        /// <param name="tableName">اسم الجدول</param>
        /// <returns>قائمة الأعمدة</returns>
        [HttpGet("tables/{tableName}/columns")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public ActionResult<IEnumerable<object>> GetTableColumns(string tableName)
        {
            var columns = tableName.ToLower() switch
            {
                "tasks" => new[]
                {
                    new { name = "id", type = "int", displayName = "المعرف" },
                    new { name = "title", type = "string", displayName = "العنوان" },
                    new { name = "description", type = "string", displayName = "الوصف" },
                    new { name = "status_id", type = "int", displayName = "الحالة" },
                    new { name = "priority_id", type = "int", displayName = "الأولوية" },
                    new { name = "assignee_id", type = "int", displayName = "المكلف" },
                    new { name = "creator_id", type = "int", displayName = "المنشئ" },
                    new { name = "department_id", type = "int", displayName = "القسم" },
                    new { name = "task_type_id", type = "int", displayName = "نوع المهمة" },
                    new { name = "created_at", type = "datetime", displayName = "تاريخ الإنشاء" },
                    new { name = "due_date", type = "datetime", displayName = "تاريخ الاستحقاق" },
                    new { name = "completed_at", type = "datetime", displayName = "تاريخ الإكمال" },
                    new { name = "progress", type = "int", displayName = "نسبة الإنجاز" }
                },
                "users" => new[]
                {
                    new { name = "id", type = "int", displayName = "المعرف" },
                    new { name = "name", type = "string", displayName = "الاسم" },
                    new { name = "email", type = "string", displayName = "البريد الإلكتروني" },
                    new { name = "role", type = "string", displayName = "الدور" },
                    new { name = "department_id", type = "int", displayName = "القسم" },
                    new { name = "is_active", type = "boolean", displayName = "نشط" },
                    new { name = "created_at", type = "datetime", displayName = "تاريخ الإنشاء" }
                },
                "departments" => new[]
                {
                    new { name = "id", type = "int", displayName = "المعرف" },
                    new { name = "name", type = "string", displayName = "الاسم" },
                    new { name = "description", type = "string", displayName = "الوصف" },
                    new { name = "manager_id", type = "int", displayName = "المدير" },
                    new { name = "created_at", type = "datetime", displayName = "تاريخ الإنشاء" }
                },
                "task_statuses" => new[]
                {
                    new { name = "id", type = "int", displayName = "المعرف" },
                    new { name = "name", type = "string", displayName = "الاسم" },
                    new { name = "color", type = "string", displayName = "اللون" },
                    new { name = "is_final", type = "boolean", displayName = "حالة نهائية" }
                },
                "task_priorities" => new[]
                {
                    new { name = "id", type = "int", displayName = "المعرف" },
                    new { name = "name", type = "string", displayName = "الاسم" },
                    new { name = "level", type = "int", displayName = "المستوى" },
                    new { name = "color", type = "string", displayName = "اللون" }
                },
                "task_types" => new[]
                {
                    new { name = "id", type = "int", displayName = "المعرف" },
                    new { name = "name", type = "string", displayName = "الاسم" },
                    new { name = "description", type = "string", displayName = "الوصف" },
                    new { name = "color", type = "string", displayName = "اللون" }
                },
                _ => Array.Empty<object>()
            };

            if (!columns.Any())
            {
                return NotFound($"الجدول '{tableName}' غير موجود");
            }

            return Ok(columns);
        }

        /// <summary>
        /// الحصول على بيانات الرسم البياني
        /// </summary>
        /// <param name="reportData">بيانات التقرير</param>
        /// <returns>بيانات الرسم البياني</returns>
        [HttpPost("chart-data")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<object>> GetChartData([FromBody] JsonElement reportData)
        {
            try
            {
                var tableName = reportData.GetProperty("tableName").GetString();
                var chartType = reportData.GetProperty("chartType").GetString();
                var xAxisColumn = reportData.GetProperty("xAxisColumn").GetString();
                var yAxisColumn = reportData.GetProperty("yAxisColumn").GetString();

                // تنفيذ استعلام بسيط للحصول على البيانات
                var data = await GetSampleChartData(tableName, chartType, xAxisColumn, yAxisColumn);

                return Ok(new { data, chartType, xAxisColumn, yAxisColumn });
            }
            catch (Exception ex)
            {
                return BadRequest($"خطأ في الحصول على بيانات الرسم البياني: {ex.Message}");
            }
        }

        /// <summary>
        /// اقتراح علاقات بين الجداول
        /// </summary>
        /// <param name="table1">الجدول الأول</param>
        /// <param name="table2">الجدول الثاني</param>
        /// <returns>قائمة العلاقات المقترحة</returns>
        [HttpGet("suggest-relations")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<IEnumerable<object>> SuggestTableRelations([FromQuery] string table1, [FromQuery] string table2)
        {
            var relations = new List<object>();

            // علاقات شائعة بين الجداول
            if ((table1 == "tasks" && table2 == "users") || (table1 == "users" && table2 == "tasks"))
            {
                relations.Add(new
                {
                    table1 = "tasks",
                    table2 = "users",
                    joinCondition = "tasks.assignee_id = users.id",
                    description = "ربط المهام بالمستخدمين المكلفين"
                });
            }

            if ((table1 == "tasks" && table2 == "departments") || (table1 == "departments" && table2 == "tasks"))
            {
                relations.Add(new
                {
                    table1 = "tasks",
                    table2 = "departments",
                    joinCondition = "tasks.department_id = departments.id",
                    description = "ربط المهام بالأقسام"
                });
            }

            if ((table1 == "tasks" && table2 == "task_statuses") || (table1 == "task_statuses" && table2 == "tasks"))
            {
                relations.Add(new
                {
                    table1 = "tasks",
                    table2 = "task_statuses",
                    joinCondition = "tasks.status_id = task_statuses.id",
                    description = "ربط المهام بحالاتها"
                });
            }

            return Ok(relations);
        }

        /// <summary>
        /// الحصول على بيانات عينة للرسم البياني
        /// </summary>
        private async Task<object> GetSampleChartData(string? tableName, string? chartType, string? xAxisColumn, string? yAxisColumn)
        {
            // بيانات عينة للاختبار
            if (tableName == "tasks")
            {
                if (xAxisColumn == "status_id" && yAxisColumn == "id")
                {
                    var tasksByStatus = await _context.Tasks
                        .Include(t => t.StatusNavigation)
                        .Where(t => !t.IsDeleted)
                        .GroupBy(t => t.StatusNavigation.Name)
                        .Select(g => new { label = g.Key, value = g.Count() })
                        .ToListAsync();

                    return tasksByStatus;
                }

                if (xAxisColumn == "priority_id" && yAxisColumn == "id")
                {
                    var tasksByPriority = await _context.Tasks
                        .Include(t => t.PriorityNavigation)
                        .Where(t => !t.IsDeleted)
                        .GroupBy(t => t.PriorityNavigation.Name)
                        .Select(g => new { label = g.Key, value = g.Count() })
                        .ToListAsync();

                    return tasksByPriority;
                }
            }

            // بيانات افتراضية
            return new[]
            {
                new { label = "عينة 1", value = 10 },
                new { label = "عينة 2", value = 20 },
                new { label = "عينة 3", value = 15 }
            };
        }
    }
}
