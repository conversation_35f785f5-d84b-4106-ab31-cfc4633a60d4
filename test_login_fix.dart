import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'lib/controllers/auth_controller.dart';
import 'lib/services/storage_service.dart';

/// ملف اختبار لتجربة إصلاح مشكلة تسجيل الدخول
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة الخدمات
  final storageService = StorageService.instance;
  Get.put(storageService, permanent: true);
  
  final authController = AuthController();
  Get.put(authController, permanent: true);
  
  // انتظار تهيئة الخدمات
  await Future.delayed(const Duration(seconds: 2));
  
  print('=== اختبار تسجيل الدخول ===');
  
  // اختبار 1: بيانات خاطئة
  print('\n1. اختبار بيانات خاطئة:');
  final result1 = await authController.login('<EMAIL>', 'wrongpassword');
  print('النتيجة: $result1');
  print('رسالة الخطأ: ${authController.error.value}');
  
  // اختبار 2: بيانات فارغة
  print('\n2. اختبار بيانات فارغة:');
  final result2 = await authController.login('', '');
  print('النتيجة: $result2');
  print('رسالة الخطأ: ${authController.error.value}');
  
  // اختبار 3: بريد إلكتروني غير صحيح
  print('\n3. اختبار بريد إلكتروني غير صحيح:');
  final result3 = await authController.login('invalid-email', 'password');
  print('النتيجة: $result3');
  print('رسالة الخطأ: ${authController.error.value}');
  
  print('\n=== انتهى الاختبار ===');
}
