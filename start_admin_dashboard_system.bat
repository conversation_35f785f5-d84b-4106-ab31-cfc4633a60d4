@echo off
echo ========================================
echo   نظام لوحة التحكم الإدارية
echo ========================================
echo.

echo 1. تنظيف العمليات السابقة...
taskkill /f /im webApi.exe 2>nul
taskkill /f /im flutter.exe 2>nul

echo.
echo 2. بدء خادم API...
start "API Server" cmd /k "cd /d %~dp0webApi\webApi && dotnet run"

echo.
echo 3. انتظار تشغيل الخادم...
timeout /t 5 /nobreak >nul

echo.
echo 4. بدء تطبيق Flutter...
start "Flutter App" cmd /k "flutter run -d windows"

echo.
echo ========================================
echo تم تشغيل النظام بنجاح!
echo ========================================
echo.
echo للوصول إلى لوحة التحكم الإدارية:
echo 1. سجل الدخول كمدير نظام
echo 2. اذهب إلى القائمة الجانبية
echo 3. اختر "لوحة التحكم الإدارية"
echo.
pause
