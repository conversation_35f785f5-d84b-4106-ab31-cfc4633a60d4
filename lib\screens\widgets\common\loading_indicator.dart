import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

/// مؤشر التحميل المخصص
/// يعرض مؤشر تحميل دائري مع رسالة اختيارية
class LoadingIndicator extends StatelessWidget {
  /// الرسالة المعروضة تحت مؤشر التحميل
  final String? message;
  
  /// حجم مؤشر التحميل
  final double size;
  
  /// لون مؤشر التحميل
  final Color? color;
  
  /// إنشاء مؤشر التحميل
  const LoadingIndicator({
    super.key,
    this.message,
    this.size = 24.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.primary,
            ),
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: 16),
          Text(
            message!,
            style: AppStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}
