-- إنشاء المستخدم الافتراضي لحل مشكلة تسجيل الدخول
-- يجب تشغيل هذا السكريبت على قاعدة البيانات databasetasks

USE [databasetasks]
GO

-- التحقق من وجود جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'users')
BEGIN
    PRINT 'جدول المستخدمين غير موجود. يرجى إنشاء قاعدة البيانات أولاً.'
    RETURN
END

-- حذف المستخدم الموجود إذا كان موجوداً (للتأكد من إعادة الإنشاء)
DELETE FROM users WHERE email = '<EMAIL>'

-- تعطيل فحص المفاتيح الخارجية مؤقتاً
ALTER TABLE users NOCHECK CONSTRAINT ALL

-- إنشاء مستخدم مدير النظام
INSERT INTO users (
    name,
    first_name,
    last_name,
    email,
    username,
    password,
    role,
    is_active,
    is_online,
    is_deleted,
    created_at
)
VALUES (
    N'مدير النظام',
    N'مدير',
    N'النظام',
    '<EMAIL>',
    'admin',
    '$2a$11$8K1p/a0dL2LkqvQOuiOX2uy7lQompaqtbyrd4sztgJSGHINfS4IA6', -- admin123
    5, -- SuperAdmin
    1, -- Active
    0, -- Not online
    0, -- Not deleted
    DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()) -- Current timestamp
)

-- إعادة تفعيل فحص المفاتيح الخارجية
ALTER TABLE users CHECK CONSTRAINT ALL

-- التحقق من نجاح الإنشاء
IF @@ROWCOUNT > 0
BEGIN
    PRINT 'تم إنشاء مستخدم مدير النظام بنجاح'
    PRINT 'البريد الإلكتروني: <EMAIL>'
    PRINT 'كلمة المرور: admin123'
    PRINT 'الدور: SuperAdmin'
END
ELSE
BEGIN
    PRINT 'فشل في إنشاء المستخدم'
END

-- عرض المستخدمين الموجودين
SELECT 
    id,
    name,
    email,
    username,
    role,
    is_active,
    created_at
FROM users
WHERE email = '<EMAIL>'

GO
