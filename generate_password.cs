using System;
using BCrypt.Net;

class Program
{
    static void Main()
    {
        string password = "admin123";
        string hashedPassword = BCrypt.HashPassword(password);
        
        Console.WriteLine($"Original Password: {password}");
        Console.WriteLine($"Hashed Password: {hashedPassword}");
        
        // Test verification
        bool isValid = BCrypt.Verify(password, hashedPassword);
        Console.WriteLine($"Verification Test: {isValid}");
        
        // Test with existing hash
        string existingHash = "$2a$11$8K1p/a0dL2LkqvQOuiOX2uy7lQompaqtbyrd4sztgJSGHINfS4IA6";
        bool isExistingValid = BCrypt.Verify(password, existingHash);
        Console.WriteLine($"Existing Hash Valid: {isExistingValid}");
    }
}
