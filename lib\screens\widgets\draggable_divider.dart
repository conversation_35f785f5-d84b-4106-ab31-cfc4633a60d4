import 'package:flutter/material.dart';
import '../../utils/mouse_event_handler.dart';

/// مكون فاصل قابل للسحب يسمح بتغيير حجم الأقسام المجاورة
/// يمكن استخدامه أفقياً أو عمودياً
class DraggableDivider extends StatefulWidget {
  /// اتجاه الفاصل (أفقي أو عمودي)
  final Axis direction;

  /// سمك الفاصل
  final double thickness;

  /// لون الفاصل
  final Color color;

  /// لون الفاصل عند السحب
  final Color? activeColor;

  /// دالة يتم استدعاؤها عند تغيير الموضع
  final Function(double delta)? onDrag;

  /// مؤشر الفأرة عند التحويم فوق الفاصل
  final MouseCursor cursor;

  const DraggableDivider({
    super.key,
    this.direction = Axis.vertical,
    this.thickness = 8.0,
    this.color = const Color(0xFFE0E0E0),
    this.activeColor,
    this.onDrag,
    this.cursor = SystemMouseCursors.resizeColumn,
  });

  @override
  State<DraggableDivider> createState() => _DraggableDividerState();
}

class _DraggableDividerState extends State<DraggableDivider> {
  bool _isDragging = false;

  @override
  Widget build(BuildContext context) {
    // تحديد الأبعاد بناءً على الاتجاه
    final isVertical = widget.direction == Axis.vertical;

    return SafeMouseRegion(
      cursor: widget.cursor,
      child: GestureDetector(
        // تتبع حركة السحب
        onHorizontalDragStart: isVertical ? _onDragStart : null,
        onHorizontalDragUpdate: isVertical ? _onDragUpdate : null,
        onHorizontalDragEnd: isVertical ? _onDragEnd : null,

        onVerticalDragStart: !isVertical ? _onDragStart : null,
        onVerticalDragUpdate: !isVertical ? _onDragUpdate : null,
        onVerticalDragEnd: !isVertical ? _onDragEnd : null,

        child: Container(
          width: isVertical ? widget.thickness : double.infinity,
          height: isVertical ? double.infinity : widget.thickness,
          color: _isDragging ? (widget.activeColor ?? widget.color.withAlpha(204)) : widget.color,
          alignment: Alignment.center,
          child: Center(
            child: Container(
              width: isVertical ? 2 : 20,
              height: isVertical ? 20 : 2,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onDragStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
    });
  }

  void _onDragUpdate(DragUpdateDetails details) {
    if (widget.onDrag != null) {
      // تحسين حساسية السحب لجعله أكثر سلاسة
      final rawDelta = widget.direction == Axis.vertical
          ? details.delta.dx
          : details.delta.dy;

      // تطبيق معامل تنعيم للحركة
      // استخدام معامل أصغر للحركات الصغيرة وأكبر للحركات الكبيرة
      final smoothingFactor = rawDelta.abs() < 1.0 ? 0.5 : 1.0;
      final delta = rawDelta * smoothingFactor;

      widget.onDrag!(delta);
    }
  }

  void _onDragEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });
  }
}
