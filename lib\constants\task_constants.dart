/// ثوابت المهام
///
/// تحتوي على الثوابت المستخدمة في نظام المهام
library;

class TaskConstants {
  // حالات المهام
  static const int statusPending = 1;
  static const int statusInProgress = 2;
  static const int statusWaitingForInfo = 3;
  static const int statusCompleted = 4;
  static const int statusCancelled = 5;
  static const int statusNews = 6;

  // أولويات المهام
  static const int priorityLow = 1;
  static const int priorityMedium = 2;
  static const int priorityHigh = 3;
  static const int priorityUrgent = 4;

  // خريطة أسماء الحالات
  static const Map<int, String> statusNames = {
    statusPending: 'قيد الانتظار',
    statusInProgress: 'قيد التنفيذ',
    statusWaitingForInfo: 'في انتظار معلومات',
    statusCompleted: 'مكتملة',
    statusCancelled: 'ملغاة',
    statusNews: 'جديدة',
  };

  // خريطة أسماء الأولويات
  static const Map<int, String> priorityNames = {
    priorityLow: 'منخفضة',
    priorityMedium: 'متوسطة',
    priorityHigh: 'عالية',
    priorityUrgent: 'عاجلة',
  };

  // قائمة جميع الحالات
  static const List<int> allStatuses = [
    statusPending,
    statusInProgress,
    statusWaitingForInfo,
    statusCompleted,
    statusCancelled,
    statusNews,
  ];

  // قائمة جميع الأولويات
  static const List<int> allPriorities = [
    priorityLow,
    priorityMedium,
    priorityHigh,
    priorityUrgent,
  ];

  /// الحصول على اسم الحالة
  static String getStatusName(int status) {
    return statusNames[status] ?? 'غير محدد';
  }

  /// الحصول على اسم الأولوية
  static String getPriorityName(int priority) {
    return priorityNames[priority] ?? 'غير محدد';
  }
}
