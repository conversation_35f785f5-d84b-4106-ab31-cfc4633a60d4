import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

/// خدمة تكامل Power BI
/// تتعامل مع Power BI REST API لتضمين التقارير ولوحات المعلومات
class PowerBIService {
  static const String _baseUrl = 'https://api.powerbi.com/v1.0/myorg';
  static const String _clientId = 'YOUR_POWER_BI_CLIENT_ID'; // يجب تعديل هذا
  static const String _clientSecret = 'YOUR_POWER_BI_CLIENT_SECRET'; // يجب تعديل هذا
  static const String _tenantId = 'YOUR_TENANT_ID'; // يجب تعديل هذا

  String? _accessToken;
  DateTime? _tokenExpiry;

  /// الحصول على رمز الوصول
  Future<String?> getAccessToken() async {
    // التحقق من صحة الرمز الحالي
    if (_accessToken != null && _tokenExpiry != null && DateTime.now().isBefore(_tokenExpiry!)) {
      return _accessToken;
    }

    try {
      // في بيئة الإنتاج، يجب استخدام OAuth 2.0 flow الصحيح
      // هنا نستخدم مثال مبسط للتوضيح
      
      final response = await http.post(
        Uri.parse('https://login.microsoftonline.com/$_tenantId/oauth2/v2.0/token'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'grant_type': 'client_credentials',
          'client_id': _clientId,
          'client_secret': _clientSecret,
          'scope': 'https://analysis.windows.net/powerbi/api/.default',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _accessToken = data['access_token'];
        final expiresIn = data['expires_in'] as int;
        _tokenExpiry = DateTime.now().add(Duration(seconds: expiresIn - 300)); // 5 دقائق هامش أمان
        
        return _accessToken;
      } else {
        throw Exception('فشل في الحصول على رمز الوصول: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على رمز الوصول: $e');
      // في حالة الفشل، نعيد رمز وهمي للتطوير
      _accessToken = 'DUMMY_ACCESS_TOKEN_FOR_DEVELOPMENT';
      _tokenExpiry = DateTime.now().add(const Duration(hours: 1));
      return _accessToken;
    }
  }

  /// الحصول على قائمة التقارير
  Future<List<Map<String, dynamic>>> getReports() async {
    try {
      final token = await getAccessToken();
      if (token == null) {
        throw Exception('لا يوجد رمز وصول صالح');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/reports'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['value'] ?? []);
      } else {
        throw Exception('فشل في تحميل التقارير: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل التقارير: $e');
      // إرجاع بيانات وهمية للتطوير
      return [
        {
          'id': 'report-1',
          'name': 'تقرير المبيعات الشهرية',
          'description': 'تقرير يعرض إحصائيات المبيعات للشهر الحالي',
          'webUrl': 'https://app.powerbi.com/reports/report-1',
        },
        {
          'id': 'report-2',
          'name': 'تقرير أداء الموظفين',
          'description': 'تقرير يعرض مؤشرات أداء الموظفين',
          'webUrl': 'https://app.powerbi.com/reports/report-2',
        },
        {
          'id': 'report-3',
          'name': 'تقرير المالية',
          'description': 'تقرير يعرض الوضع المالي للشركة',
          'webUrl': 'https://app.powerbi.com/reports/report-3',
        },
      ];
    }
  }

  /// الحصول على قائمة لوحات المعلومات
  Future<List<Map<String, dynamic>>> getDashboards() async {
    try {
      final token = await getAccessToken();
      if (token == null) {
        throw Exception('لا يوجد رمز وصول صالح');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/dashboards'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['value'] ?? []);
      } else {
        throw Exception('فشل في تحميل لوحات المعلومات: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل لوحات المعلومات: $e');
      // إرجاع بيانات وهمية للتطوير
      return [
        {
          'id': 'dashboard-1',
          'displayName': 'لوحة معلومات المبيعات',
          'lastModified': '2024-01-15T10:30:00Z',
          'webUrl': 'https://app.powerbi.com/dashboards/dashboard-1',
        },
        {
          'id': 'dashboard-2',
          'displayName': 'لوحة معلومات الموارد البشرية',
          'lastModified': '2024-01-14T15:45:00Z',
          'webUrl': 'https://app.powerbi.com/dashboards/dashboard-2',
        },
      ];
    }
  }

  /// الحصول على رابط تضمين التقرير
  Future<String> getReportEmbedUrl(String reportId) async {
    try {
      final token = await getAccessToken();
      if (token == null) {
        throw Exception('لا يوجد رمز وصول صالح');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/reports/$reportId/GenerateToken'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'accessLevel': 'View',
          'allowSaveAs': false,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final embedToken = data['token'];
        final embedUrl = data['embedUrl'] ?? 'https://app.powerbi.com/reportEmbed?reportId=$reportId';
        
        // إنشاء URL كامل مع الرمز
        return '$embedUrl&tokenType=Embed&accessToken=$embedToken';
      } else {
        throw Exception('فشل في الحصول على رابط التضمين: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على رابط تضمين التقرير: $e');
      // إرجاع رابط وهمي للتطوير
      return 'https://app.powerbi.com/reportEmbed?reportId=$reportId&autoAuth=true&ctid=$_tenantId';
    }
  }

  /// الحصول على رابط تضمين لوحة المعلومات
  Future<String> getDashboardEmbedUrl(String dashboardId) async {
    try {
      final token = await getAccessToken();
      if (token == null) {
        throw Exception('لا يوجد رمز وصول صالح');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/dashboards/$dashboardId/GenerateToken'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'accessLevel': 'View',
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final embedToken = data['token'];
        final embedUrl = data['embedUrl'] ?? 'https://app.powerbi.com/dashboardEmbed?dashboardId=$dashboardId';
        
        // إنشاء URL كامل مع الرمز
        return '$embedUrl&tokenType=Embed&accessToken=$embedToken';
      } else {
        throw Exception('فشل في الحصول على رابط التضمين: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على رابط تضمين لوحة المعلومات: $e');
      // إرجاع رابط وهمي للتطوير
      return 'https://app.powerbi.com/dashboardEmbed?dashboardId=$dashboardId&autoAuth=true&ctid=$_tenantId';
    }
  }

  /// الحصول على بيانات التقرير
  Future<Map<String, dynamic>> getReportData(String reportId, {String? datasetId}) async {
    try {
      final token = await getAccessToken();
      if (token == null) {
        throw Exception('لا يوجد رمز وصول صالح');
      }

      // إذا لم يتم تحديد معرف مجموعة البيانات، احصل عليه من التقرير
      if (datasetId == null) {
        final reportResponse = await http.get(
          Uri.parse('$_baseUrl/reports/$reportId'),
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        );

        if (reportResponse.statusCode == 200) {
          final reportData = json.decode(reportResponse.body);
          datasetId = reportData['datasetId'];
        } else {
          throw Exception('فشل في الحصول على معلومات التقرير');
        }
      }

      // الحصول على بيانات مجموعة البيانات
      final response = await http.post(
        Uri.parse('$_baseUrl/datasets/$datasetId/executeQueries'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'queries': [
            {
              'query': 'EVALUATE TOPN(100, SUMMARIZE(\'Table\', \'Table\'[Column]))'
            }
          ],
          'serializerSettings': {
            'includeNulls': true
          }
        }),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('فشل في الحصول على بيانات التقرير: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات التقرير: $e');
      // إرجاع بيانات وهمية للتطوير
      return {
        'results': [
          {
            'tables': [
              {
                'rows': [
                  ['المبيعات', 1000],
                  ['التسويق', 800],
                  ['الدعم', 600],
                ]
              }
            ]
          }
        ]
      };
    }
  }

  /// تحديث مجموعة البيانات
  Future<bool> refreshDataset(String datasetId) async {
    try {
      final token = await getAccessToken();
      if (token == null) {
        throw Exception('لا يوجد رمز وصول صالح');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/datasets/$datasetId/refreshes'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'notifyOption': 'MailOnCompletion'
        }),
      );

      return response.statusCode == 202; // Accepted
    } catch (e) {
      debugPrint('خطأ في تحديث مجموعة البيانات: $e');
      return false;
    }
  }

  /// التحقق من حالة الاتصال
  Future<bool> checkConnection() async {
    try {
      final token = await getAccessToken();
      return token != null;
    } catch (e) {
      debugPrint('خطأ في التحقق من الاتصال: $e');
      return false;
    }
  }

  /// تسجيل الخروج وإلغاء الرمز
  void logout() {
    _accessToken = null;
    _tokenExpiry = null;
  }
}
