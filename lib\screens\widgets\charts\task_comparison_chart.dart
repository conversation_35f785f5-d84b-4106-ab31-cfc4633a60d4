import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../../models/task_model.dart';
import '../../../models/task_status_enum.dart';
import '../../../constants/app_colors.dart';

/// كلاس لتمثيل بيانات الوقت
class _TimeData {
  final String taskName;
  final double timeInHours;

  _TimeData(this.taskName, this.timeInHours);
}

/// كلاس لتمثيل بيانات المهام الفرعية
class _SubtaskData {
  final String taskName;
  final int subtaskCount;

  _SubtaskData(this.taskName, this.subtaskCount);
}

/// مكون رسم بياني للمقارنة بين المهام
/// يدعم مقارنة المهام حسب معايير مختلفة مثل التقدم والوقت المستغرق
class TaskComparisonChart extends StatelessWidget {
  /// قائمة المهام المراد مقارنتها
  final List<Task> tasks;

  /// معيار المقارنة
  /// يمكن أن يكون 'progress' للتقدم، 'time' للوقت المستغرق، 'subtasks' لعدد المهام الفرعية
  final String comparisonMetric;

  /// بيانات إضافية للمقارنة
  /// مثل الوقت المستغرق لكل مهمة (عندما يكون معيار المقارنة هو 'time')
  final Map<String, dynamic>? additionalData;

  /// عنوان الرسم البياني
  final String? title;

  /// إظهار الشبكة
  final bool showGrid;

  /// إظهار التسميات التوضيحية
  final bool showLegend;

  /// إنشاء مكون رسم بياني للمقارنة بين المهام
  const TaskComparisonChart({
    super.key,
    required this.tasks,
    required this.comparisonMetric,
    this.additionalData,
    this.title,
    this.showGrid = true,
    this.showLegend = true,
  });

  @override
  Widget build(BuildContext context) {
    if (tasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام للمقارنة'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Text(
              title!,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

        Expanded(
          child: _buildChart(),
        ),

        if (showLegend)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: _buildLegend(),
          ),
      ],
    );
  }

  /// بناء الرسم البياني
  Widget _buildChart() {
    switch (comparisonMetric) {
      case 'progress':
        return _buildProgressComparisonChart();
      case 'time':
        return _buildTimeComparisonChart();
      case 'subtasks':
        return _buildSubtasksComparisonChart();
      default:
        return _buildProgressComparisonChart();
    }
  }

  /// بناء رسم بياني لمقارنة التقدم
  Widget _buildProgressComparisonChart() {
    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        title: AxisTitle(text: 'المهام'),
        majorGridLines: MajorGridLines(width: showGrid ? 1 : 0),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: 'نسبة التقدم (%)'),
        minimum: 0,
        maximum: 100,
        majorGridLines: MajorGridLines(width: showGrid ? 1 : 0),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CartesianSeries>[
        ColumnSeries<Task, String>(
          dataSource: tasks,
          xValueMapper: (Task task, _) => _getShortTaskName(task.title),
          yValueMapper: (Task task, _) => task.completionPercentage.toDouble(),
          pointColorMapper: (Task task, _) => _getColorForTask(task),
          dataLabelSettings: const DataLabelSettings(isVisible: true),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
          ),
        ),
      ],
    );
  }

  /// بناء رسم بياني لمقارنة الوقت المستغرق
  Widget _buildTimeComparisonChart() {
    // إنشاء بيانات الوقت المستغرق
    final List<_TimeData> timeData = [];
    for (var task in tasks) {
      final timeInHours = _getTimeForTask(task.id.toString());
      timeData.add(_TimeData(_getShortTaskName(task.title), timeInHours));
    }

    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        title: AxisTitle(text: 'المهام'),
        majorGridLines: MajorGridLines(width: showGrid ? 1 : 0),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: 'الوقت المستغرق (ساعات)'),
        minimum: 0,
        maximum: _getMaxTimeValue() * 1.2,
        majorGridLines: MajorGridLines(width: showGrid ? 1 : 0),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CartesianSeries>[
        ColumnSeries<_TimeData, String>(
          dataSource: timeData,
          xValueMapper: (_TimeData data, _) => data.taskName,
          yValueMapper: (_TimeData data, _) => data.timeInHours,
          color: Colors.orange,
          dataLabelSettings: const DataLabelSettings(isVisible: true),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
          ),
        ),
      ],
    );
  }

  /// بناء رسم بياني لمقارنة عدد المهام الفرعية
  Widget _buildSubtasksComparisonChart() {
    // إنشاء بيانات المهام الفرعية
    final List<_SubtaskData> subtaskData = [];
    for (var task in tasks) {
      final subtaskCount = _getSubtasksCountForTask(task.id.toString());
      subtaskData.add(_SubtaskData(_getShortTaskName(task.title), subtaskCount));
    }

    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        title: AxisTitle(text: 'المهام'),
        majorGridLines: MajorGridLines(width: showGrid ? 1 : 0),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: 'عدد المهام الفرعية'),
        minimum: 0,
        maximum: _getMaxSubtasksValue() * 1.2,
        majorGridLines: MajorGridLines(width: showGrid ? 1 : 0),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CartesianSeries>[
        ColumnSeries<_SubtaskData, String>(
          dataSource: subtaskData,
          xValueMapper: (_SubtaskData data, _) => data.taskName,
          yValueMapper: (_SubtaskData data, _) => data.subtaskCount.toDouble(),
          color: Colors.purple,
          dataLabelSettings: const DataLabelSettings(isVisible: true),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
          ),
        ),
      ],
    );
  }

  /// الحصول على الوقت المستغرق لمهمة معينة
  double _getTimeForTask(String taskId) {
    if (additionalData != null &&
        additionalData!.containsKey('taskMinutes') &&
        additionalData!['taskMinutes'] is Map) {
      final taskMinutes = additionalData!['taskMinutes'] as Map;
      if (taskMinutes.containsKey(taskId)) {
        // تحويل الدقائق إلى ساعات
        return (taskMinutes[taskId] as num).toDouble() / 60;
      }
    }
    return 0;
  }

  /// الحصول على عدد المهام الفرعية لمهمة معينة
  int _getSubtasksCountForTask(String taskId) {
    if (additionalData != null &&
        additionalData!.containsKey('subtaskCounts') &&
        additionalData!['subtaskCounts'] is Map) {
      final subtaskCounts = additionalData!['subtaskCounts'] as Map;
      if (subtaskCounts.containsKey(taskId)) {
        return (subtaskCounts[taskId] as num).toInt();
      }
    }
    return 0;
  }

  /// الحصول على أقصى قيمة للوقت المستغرق
  double _getMaxTimeValue() {
    double maxValue = 0;
    for (var task in tasks) {
      final timeValue = _getTimeForTask(task.id.toString());
      if (timeValue > maxValue) maxValue = timeValue;
    }
    return maxValue > 0 ? maxValue : 10; // قيمة افتراضية إذا لم تكن هناك بيانات
  }

  /// الحصول على أقصى قيمة لعدد المهام الفرعية
  int _getMaxSubtasksValue() {
    int maxValue = 0;
    for (var task in tasks) {
      final subtaskCount = _getSubtasksCountForTask(task.id.toString());
      if (subtaskCount > maxValue) maxValue = subtaskCount;
    }
    return maxValue > 0 ? maxValue : 5; // قيمة افتراضية إذا لم تكن هناك بيانات
  }

  /// الحصول على لون مناسب للمهمة بناءً على حالتها
  Color _getColorForTask(Task task) {
    return AppColors.getTaskStatusColor(task.status);
  }

  /// الحصول على اسم مختصر للمهمة
  String _getShortTaskName(String title) {
    if (title.length <= 10) return title;
    return '${title.substring(0, 8)}...';
  }

  /// بناء مفتاح الرسم البياني
  Widget _buildLegend() {
    if (comparisonMetric == 'progress') {
      return Wrap(
        spacing: 16,
        runSpacing: 8,
        children: TaskStatus.values.map((status) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: AppColors.getTaskStatusColor(status.index),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 4),
              Text(
                _getStatusName(status),
                style: const TextStyle(fontSize: 12),
              ),
            ],
          );
        }).toList(),
      );
    } else if (comparisonMetric == 'time') {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: Colors.orange,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 4),
          const Text(
            'الوقت المستغرق (ساعات)',
            style: TextStyle(fontSize: 12),
          ),
        ],
      );
    } else if (comparisonMetric == 'subtasks') {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: Colors.purple,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 4),
          const Text(
            'عدد المهام الفرعية',
            style: TextStyle(fontSize: 12),
          ),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  /// الحصول على اسم الحالة بالعربية
  String _getStatusName(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return 'قيد الانتظار';
      case TaskStatus.inProgress:
        return 'قيد التنفيذ';
      case TaskStatus.completed:
        return 'مكتملة';
      case TaskStatus.waitingForInfo:
        return 'في انتظار معلومات';
      case TaskStatus.cancelled:
        return 'ملغاة';
      default:
        return status.toString().split('.').last;
    }
  }
}
