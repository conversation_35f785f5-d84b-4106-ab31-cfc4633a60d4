import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../models/notification_models.dart';
import '../services/api/notifications_api_service.dart';

/// متحكم الإشعارات - متوافق مع ASP.NET Core API
class NotificationController extends GetxController {
  final NotificationsApiService _apiService = NotificationsApiService();

  // قوائم البيانات
  final RxList<Notification> _notifications = <Notification>[].obs;
  final RxList<Notification> _unreadNotifications = <Notification>[].obs;

  // حالات التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // Getters
  List<Notification> get notifications => _notifications;
  List<Notification> get unreadNotifications => _unreadNotifications;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  int get unreadCount => _unreadNotifications.length;

  @override
  void onInit() {
    super.onInit();
    // تحميل الإشعارات عند التهيئة
    loadNotifications(1); // TODO: الحصول على معرف المستخدم الحالي
  }

  /// تحميل جميع الإشعارات للمستخدم
  Future<void> loadNotifications(int userId) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // استخدام getNotificationsByUserId
      final notificationModels = await _apiService.getNotificationsByUserId(userId);

      // تحويل NotificationModel إلى Notification
      final notifications = notificationModels.map((model) => Notification(
        id: model.id,
        title: model.title,
        content: model.content,
        isRead: model.isRead,
        createdAt: model.createdAt,
        userId: model.userId,
        type: NotificationType.fromValue(model.type),
      )).toList();

      _notifications.assignAll(notifications);

      // تحديث الإشعارات غير المقروءة
      _updateUnreadNotifications();

      debugPrint('تم تحميل ${notifications.length} إشعار');
    } catch (e) {
      _error.value = 'خطأ في تحميل الإشعارات: $e';
      debugPrint(_error.value);
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الإشعارات غير المقروءة فقط
  Future<void> loadUnreadNotifications(int userId) async {
    try {
      final unreadNotificationModels = await _apiService.getUnreadNotifications();

      // تحويل NotificationModel إلى Notification
      final unreadNotifications = unreadNotificationModels.map((model) => Notification(
        id: model.id,
        title: model.title,
        content: model.content,
        isRead: model.isRead,
        createdAt: model.createdAt,
        userId: model.userId,
        type: NotificationType.fromValue(model.type),
      )).toList();

      _unreadNotifications.assignAll(unreadNotifications);
      debugPrint('تم تحميل ${unreadNotifications.length} إشعار غير مقروء');
    } catch (e) {
      debugPrint('خطأ في تحميل الإشعارات غير المقروءة: $e');
    }
  }

  /// إنشاء إشعار جديد
  Future<bool> createNotification({
    required int userId,
    required String title,
    required String message,
    String? type,
    String? actionUrl,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // إنشاء NotificationModel مباشرة
      final notificationModel = NotificationModel(
        id: 0, // سيتم تعيينه من الخادم
        userId: userId,
        title: title,
        content: message,
        type: type ?? 'general',
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      final createdNotification = await _apiService.createNotification(notificationModel);

      // تحويل إلى Notification للقائمة المحلية
      final notification = Notification(
        id: createdNotification.id,
        title: createdNotification.title,
        content: createdNotification.content,
        isRead: createdNotification.isRead,
        createdAt: createdNotification.createdAt,
        userId: createdNotification.userId,
        type: NotificationType.fromValue(createdNotification.type),
        actionUrl: actionUrl,
      );

      _notifications.insert(0, notification);

      if (!notification.isRead) {
        _unreadNotifications.insert(0, notification);
      }

      debugPrint('تم إنشاء إشعار جديد: ${notification.title}');
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء الإشعار: $e');
      return false;
    }
  }

  /// تحديد إشعار كمقروء
  Future<bool> markAsRead(int notificationId) async {
    try {
      await _apiService.markAsRead(notificationId);
      
      // تحديث الحالة محلياً
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        final notification = _notifications[index];
        final updatedNotification = notification.copyWith(isRead: true);
        _notifications[index] = updatedNotification;
        
        // إزالة من قائمة غير المقروءة
        _unreadNotifications.removeWhere((n) => n.id == notificationId);
      }
      
      debugPrint('تم تحديد الإشعار كمقروء');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديد الإشعار كمقروء: $e');
      return false;
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<bool> markAllAsRead(int userId) async {
    try {
      await _apiService.markAllAsRead();

      // تحديث الحالة محلياً
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].isRead) {
          _notifications[i] = _notifications[i].copyWith(isRead: true);
        }
      }

      // مسح قائمة غير المقروءة
      _unreadNotifications.clear();

      debugPrint('تم تحديد جميع الإشعارات كمقروءة');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديد جميع الإشعارات كمقروءة: $e');
      return false;
    }
  }

  /// حذف إشعار
  Future<bool> deleteNotification(int notificationId) async {
    try {
      await _apiService.deleteNotification(notificationId);
      
      // إزالة من القوائم محلياً
      _notifications.removeWhere((n) => n.id == notificationId);
      _unreadNotifications.removeWhere((n) => n.id == notificationId);
      
      debugPrint('تم حذف الإشعار');
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف الإشعار: $e');
      return false;
    }
  }

  /// تحديث قائمة الإشعارات غير المقروءة
  void _updateUnreadNotifications() {
    final unread = _notifications.where((n) => !n.isRead).toList();
    _unreadNotifications.assignAll(unread);
  }

  /// تحديث الإشعارات
  Future<void> refreshNotifications(int userId) async {
    await loadNotifications(userId);
  }

  /// مسح رسائل الخطأ
  void clearError() {
    _error.value = '';
  }
}
