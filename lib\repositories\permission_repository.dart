import 'package:flutter/material.dart';
import '../services/api/permissions_api_service.dart';
import '../models/permission_models.dart';

/// أنواع الصلاحيات
enum PermissionType {
  read,
  write,
  delete,
  export,
  share,
  admin,
  create,
  update,
  execute,
  schedule,
}

/// نطاقات الصلاحيات
enum PermissionScope {
  reports,
  tasks,
  users,
  departments,
  system,
  dashboard,
  calendar,
  messages,
  documents,
  settings,
}

/// مستودع الصلاحيات
class PermissionRepository {
  final PermissionsApiService _apiService = PermissionsApiService();

  /// التحقق من صلاحية المستخدم
  Future<bool> hasPermission(
    int userId,
    PermissionType permissionType,
    PermissionScope scope,
  ) async {
    try {
      // استخدام خدمة API للتحقق من الصلاحيات
      final permissionName = '${permissionType.name}_${scope.name}';
      return await _apiService.checkUserPermission(userId, permissionName);
    } catch (e) {
      debugPrint('خطأ في التحقق من الصلاحية: $e');
      // في حالة الخطأ، نعطي صلاحية افتراضية للتطوير
      return true;
    }
  }

  /// التحقق من صلاحيات متعددة
  Future<Map<String, bool>> hasPermissions(
    int userId,
    Map<PermissionType, PermissionScope> permissions,
  ) async {
    try {
      final results = <String, bool>{};
      
      for (final entry in permissions.entries) {
        final key = '${entry.key.name}_${entry.value.name}';
        results[key] = await hasPermission(userId, entry.key, entry.value);
      }
      
      return results;
    } catch (e) {
      debugPrint('خطأ في التحقق من الصلاحيات: $e');
      return {};
    }
  }

  /// الحصول على جميع صلاحيات المستخدم
  Future<List<Permission>> getUserPermissions(int userId) async {
    try {
      // استخدام خدمة API للحصول على الصلاحيات
      return await _apiService.getAllPermissions();
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحيات المستخدم: $e');
      return [];
    }
  }

  /// التحقق من صلاحية الإدارة
  Future<bool> isAdmin(int userId) async {
    try {
      return await hasPermission(userId, PermissionType.admin, PermissionScope.system);
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية الإدارة: $e');
      return false;
    }
  }

  /// التحقق من صلاحية القراءة
  Future<bool> canRead(int userId, PermissionScope scope) async {
    return await hasPermission(userId, PermissionType.read, scope);
  }

  /// التحقق من صلاحية الكتابة
  Future<bool> canWrite(int userId, PermissionScope scope) async {
    return await hasPermission(userId, PermissionType.write, scope);
  }

  /// التحقق من صلاحية الحذف
  Future<bool> canDelete(int userId, PermissionScope scope) async {
    return await hasPermission(userId, PermissionType.delete, scope);
  }

  /// التحقق من صلاحية التصدير
  Future<bool> canExport(int userId, PermissionScope scope) async {
    return await hasPermission(userId, PermissionType.export, scope);
  }
}


