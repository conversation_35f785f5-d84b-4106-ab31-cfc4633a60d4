import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/auth_models.dart';

/// خدمة التخزين المحلي
/// 
/// توفر واجهة موحدة للتعامل مع التخزين المحلي
class StorageService {
  static StorageService? _instance;
  static SharedPreferences? _preferences;

  StorageService._internal();

  static StorageService get instance {
    _instance ??= StorageService._internal();
    return _instance!;
  }

  /// تهيئة الخدمة
  static Future<StorageService> init() async {
    _preferences = await SharedPreferences.getInstance();
    return instance;
  }

  /// التحقق من التهيئة
  void _ensureInitialized() {
    if (_preferences == null) {
      throw Exception('StorageService لم يتم تهيئته. استدعي StorageService.init() أولاً');
    }
  }

  /// حفظ نص
  Future<bool> setString(String key, String value) async {
    _ensureInitialized();
    try {
      return await _preferences!.setString(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ النص $key: $e');
      return false;
    }
  }

  /// حفظ نص (alias لـ setString)
  Future<bool> saveString(String key, String value) async {
    return await setString(key, value);
  }

  /// الحصول على نص
  Future<String?> getString(String key) async {
    _ensureInitialized();
    try {
      return _preferences!.getString(key);
    } catch (e) {
      debugPrint('خطأ في قراءة النص $key: $e');
      return null;
    }
  }

  /// حفظ رقم صحيح
  Future<bool> setInt(String key, int value) async {
    _ensureInitialized();
    try {
      return await _preferences!.setInt(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ الرقم $key: $e');
      return false;
    }
  }

  /// الحصول على رقم صحيح
  Future<int?> getInt(String key) async {
    _ensureInitialized();
    try {
      return _preferences!.getInt(key);
    } catch (e) {
      debugPrint('خطأ في قراءة الرقم $key: $e');
      return null;
    }
  }

  /// حفظ رقم عشري
  Future<bool> setDouble(String key, double value) async {
    _ensureInitialized();
    try {
      return await _preferences!.setDouble(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ الرقم العشري $key: $e');
      return false;
    }
  }

  /// الحصول على رقم عشري
  Future<double?> getDouble(String key) async {
    _ensureInitialized();
    try {
      return _preferences!.getDouble(key);
    } catch (e) {
      debugPrint('خطأ في قراءة الرقم العشري $key: $e');
      return null;
    }
  }

  /// حفظ قيمة منطقية
  Future<bool> setBool(String key, bool value) async {
    _ensureInitialized();
    try {
      return await _preferences!.setBool(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ القيمة المنطقية $key: $e');
      return false;
    }
  }

  /// الحصول على قيمة منطقية
  Future<bool?> getBool(String key) async {
    _ensureInitialized();
    try {
      return _preferences!.getBool(key);
    } catch (e) {
      debugPrint('خطأ في قراءة القيمة المنطقية $key: $e');
      return null;
    }
  }

  /// حفظ قائمة نصوص
  Future<bool> setStringList(String key, List<String> value) async {
    _ensureInitialized();
    try {
      return await _preferences!.setStringList(key, value);
    } catch (e) {
      debugPrint('خطأ في حفظ قائمة النصوص $key: $e');
      return false;
    }
  }

  /// الحصول على قائمة نصوص
  Future<List<String>?> getStringList(String key) async {
    _ensureInitialized();
    try {
      return _preferences!.getStringList(key);
    } catch (e) {
      debugPrint('خطأ في قراءة قائمة النصوص $key: $e');
      return null;
    }
  }

  /// حفظ كائن JSON
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    _ensureInitialized();
    try {
      final jsonString = jsonEncode(value);
      return await _preferences!.setString(key, jsonString);
    } catch (e) {
      debugPrint('خطأ في حفظ JSON $key: $e');
      return false;
    }
  }

  /// الحصول على كائن JSON
  Future<Map<String, dynamic>?> getJson(String key) async {
    _ensureInitialized();
    try {
      final jsonString = _preferences!.getString(key);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في قراءة JSON $key: $e');
      return null;
    }
  }

  /// حفظ قائمة كائنات JSON
  Future<bool> setJsonList(String key, List<Map<String, dynamic>> value) async {
    _ensureInitialized();
    try {
      final jsonString = jsonEncode(value);
      return await _preferences!.setString(key, jsonString);
    } catch (e) {
      debugPrint('خطأ في حفظ قائمة JSON $key: $e');
      return false;
    }
  }

  /// الحصول على قائمة كائنات JSON
  Future<List<Map<String, dynamic>>?> getJsonList(String key) async {
    _ensureInitialized();
    try {
      final jsonString = _preferences!.getString(key);
      if (jsonString != null) {
        final decoded = jsonDecode(jsonString) as List<dynamic>;
        return decoded.map((item) => item as Map<String, dynamic>).toList();
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في قراءة قائمة JSON $key: $e');
      return null;
    }
  }

  /// حذف مفتاح
  Future<bool> remove(String key) async {
    _ensureInitialized();
    try {
      return await _preferences!.remove(key);
    } catch (e) {
      debugPrint('خطأ في حذف المفتاح $key: $e');
      return false;
    }
  }

  /// التحقق من وجود مفتاح
  Future<bool> containsKey(String key) async {
    _ensureInitialized();
    try {
      return _preferences!.containsKey(key);
    } catch (e) {
      debugPrint('خطأ في التحقق من المفتاح $key: $e');
      return false;
    }
  }

  /// مسح جميع البيانات
  Future<bool> clear() async {
    _ensureInitialized();
    try {
      return await _preferences!.clear();
    } catch (e) {
      debugPrint('خطأ في مسح البيانات: $e');
      return false;
    }
  }

  /// الحصول على جميع المفاتيح
  Future<Set<String>> getKeys() async {
    _ensureInitialized();
    try {
      return _preferences!.getKeys();
    } catch (e) {
      debugPrint('خطأ في الحصول على المفاتيح: $e');
      return <String>{};
    }
  }

  /// إعادة تحميل البيانات
  Future<void> reload() async {
    _ensureInitialized();
    try {
      await _preferences!.reload();
    } catch (e) {
      debugPrint('خطأ في إعادة تحميل البيانات: $e');
    }
  }

  // طرق مساعدة للبيانات الشائعة

  /// حفظ معلومات المستخدم
  Future<bool> setUserData(Map<String, dynamic> userData) async {
    return await setJson('user_data', userData);
  }

  /// الحصول على معلومات المستخدم
  Future<Map<String, dynamic>?> getUserData() async {
    return await getJson('user_data');
  }

  /// حفظ رمز المصادقة
  Future<bool> setAuthToken(String token) async {
    return await setString('auth_token', token);
  }

  /// الحصول على رمز المصادقة
  Future<String?> getAuthToken() async {
    return await getString('auth_token');
  }

  /// حذف رمز المصادقة
  Future<bool> removeAuthToken() async {
    return await remove('auth_token');
  }

  /// حفظ رمز المصادقة (alias)
  Future<bool> saveToken(String token) async {
    return await setAuthToken(token);
  }

  /// الحصول على رمز المصادقة (alias)
  Future<String?> getToken() async {
    return await getAuthToken();
  }

  /// حفظ إعدادات التطبيق
  Future<bool> setAppSettings(Map<String, dynamic> settings) async {
    return await setJson('app_settings', settings);
  }

  /// الحصول على إعدادات التطبيق
  Future<Map<String, dynamic>?> getAppSettings() async {
    return await getJson('app_settings');
  }

  // طرق خاصة بجلسة المصادقة

  /// حفظ بيانات الجلسة
  Future<bool> saveSessionData(dynamic sessionData) async {
    try {
      Map<String, dynamic> data;
      if (sessionData is Map<String, dynamic>) {
        data = sessionData;
      } else {
        // إذا كان sessionData كائن، نحوله إلى JSON
        data = sessionData.toJson();
      }
      return await setJson('session_data', data);
    } catch (e) {
      debugPrint('خطأ في حفظ بيانات الجلسة: $e');
      return false;
    }
  }

  /// الحصول على بيانات الجلسة
  Future<SessionData?> getSessionData() async {
    try {
      final data = await getJson('session_data');
      if (data != null) {
        // نحاول إنشاء SessionData من البيانات المحفوظة
        try {
          return SessionData.fromJson(data);
        } catch (e) {
          debugPrint('خطأ في تحويل بيانات الجلسة: $e');
          return null;
        }
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في قراءة بيانات الجلسة: $e');
      return null;
    }
  }

  /// مسح بيانات الجلسة
  Future<bool> clearSessionData() async {
    return await remove('session_data');
  }

  // طرق خاصة بحفظ الملفات

  /// حفظ صورة الملف الشخصي
  ///
  /// يحفظ صورة الملف الشخصي في مجلد التطبيق ويعيد المسار
  Future<String?> saveProfileImage(File imageFile, dynamic userId) async {
    try {
      // تحويل userId إلى string
      final userIdStr = userId.toString();

      // الحصول على مجلد التطبيق
      final appDir = await getApplicationDocumentsDirectory();
      final profileImagesDir = Directory(path.join(appDir.path, 'profile_images'));

      // إنشاء المجلد إذا لم يكن موجوداً
      if (!await profileImagesDir.exists()) {
        await profileImagesDir.create(recursive: true);
      }

      // تحديد اسم الملف الجديد
      final extension = path.extension(imageFile.path);
      final fileName = '${userIdStr}_profile$extension';
      final newPath = path.join(profileImagesDir.path, fileName);

      // نسخ الملف إلى المجلد الجديد
      final newFile = await imageFile.copy(newPath);

      debugPrint('تم حفظ صورة الملف الشخصي في: ${newFile.path}');
      return newFile.path;
    } catch (e) {
      debugPrint('خطأ في حفظ صورة الملف الشخصي: $e');
      return null;
    }
  }

  /// حذف صورة الملف الشخصي
  Future<bool> deleteProfileImage(dynamic userId) async {
    try {
      // تحويل userId إلى string
      final userIdStr = userId.toString();

      final appDir = await getApplicationDocumentsDirectory();
      final profileImagesDir = Directory(path.join(appDir.path, 'profile_images'));

      if (await profileImagesDir.exists()) {
        // البحث عن ملف الصورة
        final files = await profileImagesDir.list().toList();
        for (final file in files) {
          if (file is File && path.basename(file.path).startsWith('${userIdStr}_profile')) {
            await file.delete();
            debugPrint('تم حذف صورة الملف الشخصي: ${file.path}');
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في حذف صورة الملف الشخصي: $e');
      return false;
    }
  }
}
