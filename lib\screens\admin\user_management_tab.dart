import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:io';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/admin_controller.dart';
import '../../models/user_model.dart';
import '../../utils/file_processor.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/image_helper.dart';
import '../../services/storage_service.dart';

/// تبويب إدارة المستخدمين
///
/// يوفر واجهة لإدارة المستخدمين وصلاحياتهم
class UserManagementTab extends StatefulWidget {
  const UserManagementTab({super.key});

  @override
  State<UserManagementTab> createState() => _UserManagementTabState();
}

class _UserManagementTabState extends State<UserManagementTab>
    with AutomaticKeepAliveClientMixin, WidgetsBindingObserver {
  final AdminController _adminController = Get.find<AdminController>();
  final StorageService _storageService = Get.find<StorageService>();
  final TextEditingController _searchController = TextEditingController();
  final RxList<User> _filteredUsers = <User>[].obs;
  final RxBool _isSearching = false.obs;
  final ImagePicker _imagePicker = ImagePicker();
  bool _isInitialized = false;

  // حالة التحميل المحلية لتجنب التداخل مع حالة التحميل العامة
  final RxBool _isLocalLoading = false.obs;

  // متغيرات PlutoGrid
  PlutoGridStateManager? _plutoGridStateManager;

  // متغيرات التجميع
  final RxBool _isGroupingEnabled = false.obs;
  final RxList<PlutoColumn> _groupedColumns = <PlutoColumn>[].obs;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // تأجيل جميع العمليات إلى ما بعد اكتمال البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupTabListener();
      _initializeData();
    });
  }

  /// إعداد مراقب التبويب
  void _setupTabListener() {
    try {
      final tabController = Get.find<TabController>(tag: 'admin_tab_controller');
      tabController.addListener(() {
        // التحقق من أن التبويب الحالي هو تبويب إدارة المستخدمين (الفهرس 0)
        if (tabController.index == 0 && mounted) {
          debugPrint('تم الانتقال إلى تبويب إدارة المستخدمين');
          // تأجيل العملية لتجنب التداخل مع عملية البناء
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _checkAndRefreshData();
            }
          });
        }
      });
    } catch (e) {
      debugPrint('خطأ في إعداد مراقب التبويب: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed && mounted) {
      // تأجيل إعادة تحميل البيانات عند العودة للتطبيق
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _checkAndRefreshData();
        }
      });
    }
  }

  /// التحقق من البيانات وإعادة تحميلها إذا لزم الأمر
  Future<void> _checkAndRefreshData() async {
    try {
      // التحقق من حالة التحميل المحلية لتجنب التحميل المتكرر
      if (_isLocalLoading.value) {
        debugPrint('البيانات قيد التحميل بالفعل، تم تجاهل الطلب');
        return;
      }

      // إذا كانت قائمة المستخدمين فارغة، قم بتحميلها
      if (_adminController.users.isEmpty) {
        debugPrint('قائمة المستخدمين فارغة، سيتم تحميل البيانات');
        await _initializeData();
      } else {
        // إذا كانت البيانات موجودة، قم بتحديث القائمة المفلترة فقط
        debugPrint('تحديث القائمة المفلترة مع ${_adminController.users.length} مستخدم');
        _filteredUsers.assignAll(_adminController.users);
        _filterUsers();
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من البيانات: $e');
    }
  }

  /// تهيئة البيانات
  Future<void> _initializeData() async {
    if (_isInitialized && _adminController.users.isNotEmpty) {
      _filteredUsers.assignAll(_adminController.users);
      return;
    }

    _isLocalLoading.value = true;
    try {
      // تحميل المستخدمين دائماً للتأكد من الحصول على أحدث البيانات
      await _adminController.loadAllUsers();
      _filteredUsers.assignAll(_adminController.users);
      _isInitialized = true;
      debugPrint('تم تحميل ${_adminController.users.length} مستخدم في UserManagementTab');
    } catch (e) {
      debugPrint('خطأ في تهيئة بيانات المستخدمين: $e');
      // تأجيل عرض الرسالة لتجنب التداخل مع عملية البناء
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Get.snackbar(
            'خطأ في التحميل',
            'حدث خطأ أثناء تحميل بيانات المستخدمين',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        }
      });
    } finally {
      _isLocalLoading.value = false;
    }
  }

  /// إعادة تحميل البيانات
  Future<void> _refreshData() async {
    _isLocalLoading.value = true;
    try {
      await _adminController.loadAllUsers();
      _filteredUsers.assignAll(_adminController.users);
      _filterUsers(); // إعادة تطبيق المرشحات

      Get.snackbar(
        'تم التحديث',
        'تم تحديث بيانات المستخدمين بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات المستخدمين: $e');
      Get.snackbar(
        'خطأ في التحديث',
        'فشل في تحديث بيانات المستخدمين',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } finally {
      _isLocalLoading.value = false;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    super.dispose();
  }

  /// تصفية المستخدمين بناءً على نص البحث
  void _filterUsers() {
    final searchText = _searchController.text.toLowerCase();

    if (searchText.isEmpty) {
      _filteredUsers.assignAll(_adminController.users);
    } else {
      _filteredUsers.assignAll(_adminController.users.where((user) {
        return user.name.toLowerCase().contains(searchText) ||
            user.email.toLowerCase().contains(searchText);
      }).toList());
    }

    // تحديث PlutoGrid إذا كان موجوداً
    if (_plutoGridStateManager != null) {
      final newRows = _createPlutoRows();
      _plutoGridStateManager!.removeAllRows();
      _plutoGridStateManager!.appendRows(newRows);
    }
  }

  /// إنشاء أعمدة PlutoGrid
  List<PlutoColumn> _createPlutoColumns() {
    return [
      // عمود ID (مخفي)
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.number(),
        enableEditingMode: false,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        enableFilterMenuItem: false,
        width: 0,
        minWidth: 0,
        hide: true, // إخفاء العمود
      ),

      // عمود الصورة
      PlutoColumn(
        title: 'الصورة',
        field: 'profile_image',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        enableFilterMenuItem: false,
        width: 80,
        minWidth: 80,
        frozen: PlutoColumnFrozen.start,
        renderer: (rendererContext) {
          final user = _getUserFromRow(rendererContext.row);
          if (user == null) {
            return const Center(
              child: Icon(Icons.person, size: 20, color: Colors.grey),
            );
          }
          return Center(
            child: ImageHelper.buildProfileImage(
              imagePath: user.profileImage,
              radius: 20,
              fallbackText: user.name,
              backgroundColor: AppColors.primary.withAlpha(51),
              textColor: AppColors.primary,
            ),
          );
        },
      ),

      // عمود الاسم
      PlutoColumn(
        title: 'الاسم',
        field: 'name',
        type: PlutoColumnType.text(),
        enableEditingMode: true,
        enableSorting: true,
        enableColumnDrag: true,
        enableContextMenu: true,
        enableFilterMenuItem: true,
        width: 150,
        minWidth: 100,
      ),

      // عمود البريد الإلكتروني
      PlutoColumn(
        title: 'البريد الإلكتروني',
        field: 'email',
        type: PlutoColumnType.text(),
        enableEditingMode: true,
        enableSorting: true,
        enableColumnDrag: true,
        enableContextMenu: true,
        enableFilterMenuItem: true,
        width: 200,
        minWidth: 150,
      ),

      // عمود الدور
      PlutoColumn(
        title: 'الدور',
        field: 'role',
        type: PlutoColumnType.select([
          'مستخدم',
          'مشرف',
          'مدير',
          'مدير عام',
          'مدير النظام',
        ]),
        enableEditingMode: true,
        enableSorting: true,
        enableColumnDrag: true,
        enableContextMenu: true,
        enableFilterMenuItem: true,
        width: 120,
        minWidth: 100,
      ),

      // عمود الحالة
      PlutoColumn(
        title: 'الحالة',
        field: 'status',
        type: PlutoColumnType.select(['نشط', 'معطل']),
        enableEditingMode: true,
        enableSorting: true,
        enableColumnDrag: true,
        enableContextMenu: true,
        enableFilterMenuItem: true,
        width: 100,
        minWidth: 80,
        renderer: (rendererContext) {
          final user = _getUserFromRow(rendererContext.row);
          if (user == null) {
            return const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.help, color: Colors.grey, size: 16),
                SizedBox(width: 4),
                Text('غير محدد'),
              ],
            );
          }
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                user.isActive ? Icons.check_circle : Icons.cancel,
                color: user.isActive ? Colors.green : Colors.red,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(user.isActive ? 'نشط' : 'معطل'),
            ],
          );
        },
      ),

      // عمود تاريخ الإنشاء
      PlutoColumn(
        title: 'تاريخ الإنشاء',
        field: 'created_at',
        type: PlutoColumnType.date(),
        enableEditingMode: false,
        enableSorting: true,
        enableColumnDrag: true,
        enableContextMenu: true,
        enableFilterMenuItem: true,
        width: 120,
        minWidth: 100,
      ),

      // عمود آخر تسجيل دخول
      PlutoColumn(
        title: 'آخر تسجيل دخول',
        field: 'last_login',
        type: PlutoColumnType.date(),
        enableEditingMode: false,
        enableSorting: true,
        enableColumnDrag: true,
        enableContextMenu: true,
        enableFilterMenuItem: true,
        width: 140,
        minWidth: 120,
      ),

      // عمود الإجراءات
      PlutoColumn(
        title: 'الإجراءات',
        field: 'actions',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        enableFilterMenuItem: false,
        width: 150,
        minWidth: 150,
        frozen: PlutoColumnFrozen.end,
        renderer: (rendererContext) {
          final user = _getUserFromRow(rendererContext.row);
          if (user == null) {
            return const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.error, color: Colors.grey, size: 18),
              ],
            );
          }
          return _buildActionsCell(user);
        },
      ),
    ];
  }

  /// الحصول على المستخدم من صف PlutoGrid
  User? _getUserFromRow(PlutoRow row) {
    try {
      final userId = row.cells['id']?.value;
      if (userId == null) return null;

      final userIdInt = userId is int ? userId : int.tryParse(userId.toString());
      if (userIdInt == null) return null;

      // استخدام where().firstOrNull بدلاً من firstWhere لتجنب الاستثناء
      final foundUsers = _filteredUsers.where((user) => user.id == userIdInt);
      return foundUsers.isNotEmpty ? foundUsers.first : null;
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدم من الصف: $e');
      return null;
    }
  }

  /// إنشاء صفوف PlutoGrid
  List<PlutoRow> _createPlutoRows() {
    return _filteredUsers.map((user) {
      return PlutoRow(
        cells: {
          'id': PlutoCell(value: user.id),
          'profile_image': PlutoCell(value: user.profileImage ?? ''),
          'name': PlutoCell(value: user.name),
          'email': PlutoCell(value: user.email),
          'role': PlutoCell(value: _getRoleText(user.role)),
          'status': PlutoCell(value: user.isActive ? 'نشط' : 'معطل'),
          'created_at': PlutoCell(value: _formatDate(user.createdAt)),
          'last_login': PlutoCell(value: user.lastLogin != null
              ? _formatDate(user.lastLogin!)
              : 'لم يسجل الدخول'),
          'actions': PlutoCell(value: ''),
        },
      );
    }).toList();
  }

  /// بناء خلية الإجراءات
  Widget _buildActionsCell(User user) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.edit, size: 18),
          tooltip: 'تعديل',
          onPressed: () => _showEditUserDialog(user),
          padding: const EdgeInsets.all(4),
          constraints: const BoxConstraints(),
        ),
        IconButton(
          icon: Icon(
            user.isActive ? Icons.block : Icons.check_circle,
            size: 18,
            color: user.isActive ? Colors.red : Colors.green,
          ),
          tooltip: user.isActive ? 'تعطيل' : 'تفعيل',
          onPressed: () => _toggleUserActive(user),
          padding: const EdgeInsets.all(4),
          constraints: const BoxConstraints(),
        ),
        IconButton(
          icon: const Icon(Icons.admin_panel_settings, size: 18),
          tooltip: 'الصلاحيات',
          onPressed: () {
            final adminController = Get.find<AdminController>();
            adminController.selectedPermissionUser.value = user;
            Get.find<TabController>(tag: 'admin_tab_controller').animateTo(1);
          },
          padding: const EdgeInsets.all(4),
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // مطلوب لـ AutomaticKeepAliveClientMixin

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildSearchBar(),
          const SizedBox(height: 16),
          Expanded(
            child: _buildUserList(),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'إدارة المستخدمين',
          style: AppStyles.titleLarge,
        ),
        Row(
          children: [
            // زر تحديث البيانات
            Obx(() => IconButton(
              icon: _isLocalLoading.value
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.refresh),
              tooltip: 'تحديث البيانات',
              onPressed: _isLocalLoading.value ? null : _refreshData,
            )),
            const SizedBox(width: 8),
            // زر التجميع مع قائمة منسدلة
            Obx(() => PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'toggle':
                    _toggleGrouping();
                    break;
                  case 'advanced':
                    _showGroupingDialog();
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem<String>(
                  value: 'toggle',
                  child: Row(
                    children: [
                      Icon(_isGroupingEnabled.value ? Icons.group_off : Icons.group),
                      const SizedBox(width: 8),
                      Text(_isGroupingEnabled.value ? 'إلغاء التجميع' : 'تجميع حسب الدور'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'advanced',
                  child: Row(
                    children: [
                      Icon(Icons.settings),
                      SizedBox(width: 8),
                      Text('تجميع متقدم'),
                    ],
                  ),
                ),
              ],
              child: OutlinedButton.icon(
                onPressed: null, // سيتم التحكم من PopupMenuButton
                icon: Icon(_isGroupingEnabled.value ? Icons.group_off : Icons.group),
                label: Text(_isGroupingEnabled.value ? 'مجمع' : 'تجميع'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: _isGroupingEnabled.value ? Colors.orange : Colors.blue,
                ),
              ),
            )),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: _showAddUserDialog,
              icon: const Icon(Icons.person_add),
              label: const Text('إضافة مستخدم'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar() {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'البحث عن مستخدم...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: Obx(() {
          return _isSearching.value
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _isSearching.value = false;
                    _filterUsers();
                  },
                )
              : const SizedBox.shrink();
        }),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      onChanged: (value) {
        _isSearching.value = value.isNotEmpty;
        _filterUsers();
      },
    );
  }

  /// بناء قائمة المستخدمين
  Widget _buildUserList() {
    return Obx(() {
      // التحقق من حالة التحميل المحلية
      if (_isLocalLoading.value) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري تحميل بيانات المستخدمين...'),
            ],
          ),
        );
      }

      // التحقق من وجود خطأ
      if (_adminController.error.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'خطأ في تحميل البيانات',
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 8),
              Text(
                _adminController.error,
                style: AppStyles.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () async {
                  await _initializeData();
                },
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }

      // التحقق من وجود بيانات
      if (_filteredUsers.isEmpty && _adminController.users.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.person_off,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                'لا يوجد مستخدمين',
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 8),
              const Text(
                'لم يتم العثور على أي مستخدمين في النظام',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () async {
                  await _refreshData();
                },
                child: const Text('تحديث'),
              ),
            ],
          ),
        );
      }

      // التحقق من نتائج البحث
      if (_filteredUsers.isEmpty && _isSearching.value) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.search_off,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد نتائج',
                style: AppStyles.titleMedium,
              ),
              const Text(
                'حاول تغيير معايير البحث',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        );
      }

      return ResponsiveHelper.isDesktop(context) ||
              ResponsiveHelper.isTablet(context)
          ? _buildUsersPlutoGrid()
          : _buildUsersMobileList();
    });
  }

  /// بناء جدول المستخدمين باستخدام PlutoGrid للشاشات الكبيرة
  Widget _buildUsersPlutoGrid() {
    return Obx(() {
      // إنشاء الأعمدة والصفوف
      final columns = _createPlutoColumns();
      final rows = _createPlutoRows();

      return PlutoGrid(
        columns: columns,
        rows: rows,
        onLoaded: (PlutoGridOnLoadedEvent event) {
          _plutoGridStateManager = event.stateManager;

          // تطبيق إعدادات PlutoGrid المتقدمة
          _plutoGridStateManager?.setConfiguration(
            PlutoGridConfiguration(
              style: const PlutoGridStyleConfig(
                gridBorderRadius: BorderRadius.all(Radius.circular(8)),
                enableGridBorderShadow: true,
                enableColumnBorderVertical: true,
                enableColumnBorderHorizontal: true,
                enableCellBorderVertical: true,
                enableCellBorderHorizontal: true,
                enableRowColorAnimation: true,
                gridBorderColor: Colors.grey,
                activatedBorderColor: Colors.blue,
                inactivatedBorderColor: Colors.grey,
                borderColor: Colors.grey,
                rowHeight: 50,
                columnHeight: 45,
                columnFilterHeight: 35,
                cellTextStyle: TextStyle(fontSize: 14),
                columnTextStyle: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                iconSize: 18,
                defaultColumnTitlePadding: EdgeInsets.symmetric(horizontal: 8),
                defaultCellPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
              columnSize: const PlutoGridColumnSizeConfig(
                autoSizeMode: PlutoAutoSizeMode.scale,
                resizeMode: PlutoResizeMode.pushAndPull,
                restoreAutoSizeAfterHideColumn: true,
                restoreAutoSizeAfterFrozenColumn: true,
                restoreAutoSizeAfterMoveColumn: true,
                restoreAutoSizeAfterInsertColumn: true,
                restoreAutoSizeAfterRemoveColumn: true,
              ),
              scrollbar: const PlutoGridScrollbarConfig(
                draggableScrollbar: true,
                isAlwaysShown: true,
                scrollbarThickness: 8,
                scrollbarThicknessWhileDragging: 10,
                scrollbarRadius: Radius.circular(4),
                scrollbarRadiusWhileDragging: Radius.circular(6),
              ),
              columnFilter: PlutoGridColumnFilterConfig(
                filters: const [
                  PlutoFilterTypeContains(),
                  PlutoFilterTypeEquals(),
                  PlutoFilterTypeStartsWith(),
                  PlutoFilterTypeEndsWith(),
                  PlutoFilterTypeGreaterThan(),
                  PlutoFilterTypeGreaterThanOrEqualTo(),
                  PlutoFilterTypeLessThan(),
                  PlutoFilterTypeLessThanOrEqualTo(),
                ],
              ),
              enterKeyAction: PlutoGridEnterKeyAction.editingAndMoveDown,
              enableMoveDownAfterSelecting: true,
              enableMoveHorizontalInEditing: true,
              localeText: PlutoGridLocaleText.arabic(),
            ),
          );

          // تفعيل التصفية
          _plutoGridStateManager?.setShowColumnFilter(true);

          // تطبيق التجميع إذا كان مفعلاً
          if (_isGroupingEnabled.value && _groupedColumns.isNotEmpty) {
            _applyGrouping();
          }
        },
        onChanged: (PlutoGridOnChangedEvent event) {
          // التعامل مع تغيير البيانات
          _handleCellChange(event);
        },
        onSorted: (PlutoGridOnSortedEvent event) {
          // التعامل مع الترتيب
          debugPrint('تم ترتيب العمود: ${event.column.field}');
        },
        onRowDoubleTap: (PlutoGridOnRowDoubleTapEvent event) {
          // فتح نافذة التحرير عند النقر المزدوج
          final user = _getUserFromRow(event.row);
          if (user != null) {
            _showEditUserDialog(user);
          }
        },
        createHeader: (stateManager) {
          return Container(
            height: 45,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              border: Border(
                bottom: BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.people, color: AppColors.primary),
                const SizedBox(width: 8),
                Obx(() => Text(
                  'إدارة المستخدمين - ${_filteredUsers.length} مستخدم${_isGroupingEnabled.value ? ' (مجمع)' : ''}',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                )),
                // عرض أعمدة التجميع النشطة
                Obx(() => _isGroupingEnabled.value && _groupedColumns.isNotEmpty
                    ? Container(
                        margin: const EdgeInsets.only(right: 8),
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.group, size: 16, color: Colors.blue),
                            const SizedBox(width: 4),
                            Text(
                              'مجمع حسب: ${_groupedColumns.map((col) => col.title).join(', ')}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.blue,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink()),
                const Spacer(),
                // زر تصدير البيانات
                IconButton(
                  icon: const Icon(Icons.download),
                  tooltip: 'تصدير البيانات',
                  onPressed: _exportUsersData,
                ),
              ],
            ),
          );
        },
      );
    });
  }

  /// التعامل مع تغيير خلايا PlutoGrid
  void _handleCellChange(PlutoGridOnChangedEvent event) {
    final user = _getUserFromRow(event.row);
    if (user == null) {
      debugPrint('خطأ: لم يتم العثور على المستخدم للصف المحدد');
      return;
    }

    final field = event.column.field;
    final newValue = event.value;

    debugPrint('تم تغيير الخلية: $field = $newValue للمستخدم: ${user.name}');

    // التعامل مع التغييرات حسب نوع الحقل
    switch (field) {
      case 'name':
        _updateUserField(user, 'name', newValue);
        break;
      case 'email':
        _updateUserField(user, 'email', newValue);
        break;
      case 'role':
        _updateUserRole(user, newValue);
        break;
      case 'status':
        _updateUserStatus(user, newValue);
        break;
    }
  }

  /// تحديث حقل المستخدم
  void _updateUserField(User user, String field, dynamic value) async {
    try {
      User updatedUser;
      switch (field) {
        case 'name':
          updatedUser = user.copyWith(name: value.toString());
          break;
        case 'email':
          updatedUser = user.copyWith(email: value.toString());
          break;
        default:
          return;
      }

      final result = await _adminController.updateUser(updatedUser);
      if (result) {
        await _refreshData();
        Get.snackbar(
          'تم التحديث',
          'تم تحديث $field بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث $field: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// تحديث دور المستخدم
  void _updateUserRole(User user, String roleText) async {
    UserRole? newRole;
    switch (roleText) {
      case 'مستخدم':
        newRole = UserRole.user;
        break;
      case 'مشرف':
        newRole = UserRole.supervisor;
        break;
      case 'مدير':
        newRole = UserRole.manager;
        break;
      case 'مدير عام':
        newRole = UserRole.admin;
        break;
      case 'مدير النظام':
        newRole = UserRole.superAdmin;
        break;
    }

    if (newRole != null) {
      try {
        final updatedUser = user.copyWith(role: newRole);
        final result = await _adminController.updateUser(updatedUser);
        if (result) {
          await _refreshData();
          Get.snackbar(
            'تم التحديث',
            'تم تحديث دور المستخدم بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
          );
        }
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'فشل في تحديث دور المستخدم: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// تحديث حالة المستخدم
  void _updateUserStatus(User user, String statusText) async {
    final isActive = statusText == 'نشط';
    if (user.isActive != isActive) {
      _toggleUserActive(user);
    }
  }

  /// تصدير بيانات المستخدمين
  void _exportUsersData() async {
    try {
      Get.snackbar(
        'تصدير البيانات',
        'جاري تصدير بيانات المستخدمين...',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      // هنا يمكن إضافة منطق تصدير البيانات الفعلي
      // مثل تصدير إلى CSV أو Excel
      await Future.delayed(const Duration(seconds: 1));

      Get.snackbar(
        'تم التصدير',
        'تم تصدير ${_filteredUsers.length} مستخدم بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        'خطأ في التصدير',
        'فشل في تصدير البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// تبديل حالة التجميع
  void _toggleGrouping() {
    _isGroupingEnabled.value = !_isGroupingEnabled.value;

    if (_isGroupingEnabled.value) {
      // تفعيل التجميع حسب الدور كافتراضي
      _enableGroupingByRole();
    } else {
      // إلغاء التجميع
      _disableGrouping();
    }
  }

  /// تفعيل التجميع حسب الدور
  void _enableGroupingByRole() {
    if (_plutoGridStateManager != null) {
      final roleColumn = _plutoGridStateManager!.columns
          .firstWhere((col) => col.field == 'role');

      _groupedColumns.clear();
      _groupedColumns.add(roleColumn);

      _applyGrouping();

      Get.snackbar(
        'تم تفعيل التجميع',
        'تم تجميع المستخدمين حسب الدور',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// إلغاء التجميع
  void _disableGrouping() {
    if (_plutoGridStateManager != null) {
      _groupedColumns.clear();
      _plutoGridStateManager!.setRowGroup(null);

      Get.snackbar(
        'تم إلغاء التجميع',
        'تم إلغاء تجميع المستخدمين',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// تطبيق التجميع
  void _applyGrouping() {
    if (_plutoGridStateManager != null && _groupedColumns.isNotEmpty) {
      _plutoGridStateManager!.setRowGroup(
        PlutoRowGroupByColumnDelegate(
          columns: _groupedColumns,
          showFirstExpandableIcon: false,
          showCount: true,
          enableCompactCount: true,
        ),
      );
    }
  }

  /// عرض حوار اختيار أعمدة التجميع
  void _showGroupingDialog() {
    final availableColumns = _plutoGridStateManager?.columns
        .where((col) => col.field != 'id' &&
                       col.field != 'profile_image' &&
                       col.field != 'actions')
        .toList() ?? [];

    Get.dialog(
      AlertDialog(
        title: const Text('اختيار أعمدة التجميع'),
        content: SizedBox(
          width: 300,
          height: 400,
          child: Column(
            children: [
              const Text('اختر الأعمدة التي تريد التجميع حسبها:'),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: availableColumns.length,
                  itemBuilder: (context, index) {
                    final column = availableColumns[index];
                    return Obx(() => CheckboxListTile(
                      title: Text(column.title),
                      value: _groupedColumns.contains(column),
                      onChanged: (bool? value) {
                        if (value == true) {
                          if (!_groupedColumns.contains(column)) {
                            _groupedColumns.add(column);
                          }
                        } else {
                          _groupedColumns.remove(column);
                        }
                      },
                    ));
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              if (_groupedColumns.isNotEmpty) {
                _isGroupingEnabled.value = true;
                _applyGrouping();
                Get.snackbar(
                  'تم تطبيق التجميع',
                  'تم تجميع البيانات حسب الأعمدة المحددة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                _isGroupingEnabled.value = false;
                _disableGrouping();
              }
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المستخدمين للشاشات الصغيرة
  Widget _buildUsersMobileList() {
    return ListView.builder(
      itemCount: _filteredUsers.length,
      itemBuilder: (context, index) {
        final user = _filteredUsers[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: ImageHelper.buildProfileImage(
              imagePath: user.profileImage,
              radius: 20,
              fallbackText: user.name,
              backgroundColor: AppColors.primary.withAlpha(51),
              textColor: AppColors.primary,
            ),
            title: Text(user.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(user.email),
                Text('الدور: ${_getRoleText(user.role)}'),
                Row(
                  children: [
                    Icon(
                      user.isActive ? Icons.check_circle : Icons.cancel,
                      color: user.isActive ? Colors.green : Colors.red,
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      user.isActive ? 'نشط' : 'معطل',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showEditUserDialog(user);
                    break;
                  case 'toggle':
                    _toggleUserActive(user);
                    break;
                  case 'permissions':
                    // الانتقال إلى تبويب إدارة الصلاحيات
                    final adminController = Get.find<AdminController>();
                    adminController.selectedPermissionUser.value = user;
                    // الانتقال إلى تبويب الصلاحيات (الثاني)
                    // الانتقال إلى تبويب الصلاحيات (الثاني)
                    Get.find<TabController>(tag: 'admin_tab_controller')
                        .animateTo(1);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem<String>(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'toggle',
                  child: Row(
                    children: [
                      Icon(
                        user.isActive ? Icons.block : Icons.check_circle,
                        color: user.isActive ? Colors.red : Colors.green,
                      ),
                      const SizedBox(width: 8),
                      Text(user.isActive ? 'تعطيل' : 'تفعيل'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'permissions',
                  child: Row(
                    children: [
                      Icon(Icons.admin_panel_settings, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('إدارة الصلاحيات'),
                    ],
                  ),
                ),
              ],
            ),
            onTap: () => _showUserDetailsDialog(user),
          ),
        );
      },
    );
  }

  /// عرض خيارات اختيار الصورة
  Future<void> _showImagePickerOptions(
      BuildContext context, Function(File) onImageSelected) async {
    await showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery(onImageSelected);
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera(onImageSelected);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImageFromGallery(Function(File) onImageSelected) async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      // معالجة الصورة وضغطها
      final processedImage = await _processImage(File(pickedFile.path));
      onImageSelected(processedImage);
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _pickImageFromCamera(Function(File) onImageSelected) async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.camera,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      // معالجة الصورة وضغطها
      final processedImage = await _processImage(File(pickedFile.path));
      onImageSelected(processedImage);
    }
  }

  /// معالجة الصورة وضغطها
  Future<File> _processImage(File imageFile) async {
    try {
      // استخدام FileProcessor لإنشاء صورة مصغرة
      final thumbnailPath = await FileProcessor.createThumbnail(
        imagePath: imageFile.path,
        thumbnailSize: 500, // حجم مناسب لصورة الملف الشخصي
      );

      if (thumbnailPath != null) {
        return File(thumbnailPath);
      }
    } catch (e) {
      debugPrint('Error processing image: $e');
    }

    // إذا فشلت المعالجة، إرجاع الصورة الأصلية
    return imageFile;
  }

  /// عرض حوار إضافة مستخدم جديد
  void _showAddUserDialog() {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final passwordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    UserRole selectedRole = UserRole.user;
    File? selectedImage;

    Get.dialog(
      AlertDialog(
        title: const Text('إضافة مستخدم جديد'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // صورة المستخدم
                  Center(
                    child: Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: AppColors.primary.withAlpha(51),
                          backgroundImage: selectedImage != null
                              ? FileImage(selectedImage!)
                              : null,
                          child: selectedImage == null
                              ? const Icon(
                                  Icons.person,
                                  size: 50,
                                  color: AppColors.primary,
                                )
                              : null,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.camera_alt,
                              color: Colors.white,
                              size: 20,
                            ),
                            onPressed: () {
                              _showImagePickerOptions(context, (image) {
                                setState(() {
                                  selectedImage = image;
                                });
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // حقول البيانات
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'الاسم',
                      hintText: 'أدخل اسم المستخدم',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      hintText: 'أدخل البريد الإلكتروني',
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: passwordController,
                    decoration: const InputDecoration(
                      labelText: 'كلمة المرور',
                      hintText: 'أدخل كلمة المرور',
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: confirmPasswordController,
                    decoration: const InputDecoration(
                      labelText: 'تأكيد كلمة المرور',
                      hintText: 'أدخل كلمة المرور مرة أخرى',
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<UserRole>(
                    value: selectedRole,
                    decoration: const InputDecoration(
                      labelText: 'الدور',
                    ),
                    items: UserRole.values.map((role) {
                      return DropdownMenuItem<UserRole>(
                        value: role,
                        child: Text(_getRoleText(role)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedRole = value;
                        });
                      }
                    },
                  ),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // التحقق من صحة البيانات
              if (nameController.text.isEmpty ||
                  emailController.text.isEmpty ||
                  passwordController.text.isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يرجى ملء جميع الحقول المطلوبة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              if (passwordController.text != confirmPasswordController.text) {
                Get.snackbar(
                  'خطأ',
                  'كلمة المرور وتأكيدها غير متطابقين',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              // حفظ صورة الملف الشخصي إذا تم اختيارها
              String? profileImagePath;
              final tempUserId = DateTime.now().millisecondsSinceEpoch; // معرف مؤقت
              if (selectedImage != null) {
                profileImagePath = await _storageService.saveProfileImage(
                  selectedImage!,
                  tempUserId,
                );
              }

              // إنشاء المستخدم الجديد
              final newUser = User(
                id: 0, // سيتم تعيين ID من الخادم
                name: nameController.text.trim(),
                email: emailController.text.trim(),
                password: passwordController.text,
                profileImage: profileImagePath,
                role: selectedRole,
                isActive: true,
                createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
              );

              try {
                await _adminController.createUser(newUser);

                // تحديث القائمة المحلية فوراً
                await _refreshData();

                Get.back();
                Get.snackbar(
                  'تم بنجاح',
                  'تم إضافة المستخدم بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } catch (e) {
                Get.snackbar(
                  'خطأ',
                  'فشل إضافة المستخدم: $e',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
                        },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار تعديل مستخدم
  void _showEditUserDialog(User user) {
    final nameController = TextEditingController(text: user.name);
    final emailController = TextEditingController(text: user.email);
    final passwordController = TextEditingController();
    UserRole selectedRole = user.role;
    File? selectedImage;

    Get.dialog(
      AlertDialog(
        title: const Text('تعديل المستخدم'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // صورة المستخدم
                  Center(
                    child: Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        selectedImage != null
                            ? CircleAvatar(
                                radius: 50,
                                backgroundColor: AppColors.primary.withAlpha(51),
                                backgroundImage: FileImage(selectedImage!),
                              )
                            : ImageHelper.buildProfileImage(
                                imagePath: user.profileImage,
                                radius: 50,
                                fallbackText: user.name,
                                backgroundColor: AppColors.primary.withAlpha(51),
                                textColor: AppColors.primary,
                                fallbackIcon: Icons.person,
                              ),
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.camera_alt,
                              color: Colors.white,
                              size: 20,
                            ),
                            onPressed: () {
                              _showImagePickerOptions(context, (image) {
                                setState(() {
                                  selectedImage = image;
                                });
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // حقول البيانات
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'الاسم',
                      hintText: 'أدخل اسم المستخدم',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      hintText: 'أدخل البريد الإلكتروني',
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: passwordController,
                    decoration: const InputDecoration(
                      labelText:
                          'كلمة المرور الجديدة (اتركها فارغة للإبقاء على كلمة المرور الحالية)',
                      hintText: 'أدخل كلمة المرور الجديدة',
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<UserRole>(
                    value: selectedRole,
                    decoration: const InputDecoration(
                      labelText: 'الدور',
                    ),
                    items: UserRole.values.map((role) {
                      return DropdownMenuItem<UserRole>(
                        value: role,
                        child: Text(_getRoleText(role)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedRole = value;
                        });
                      }
                    },
                  ),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // التحقق من صحة البيانات
              if (nameController.text.isEmpty || emailController.text.isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يرجى ملء جميع الحقول المطلوبة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              // حفظ صورة الملف الشخصي إذا تم اختيارها
              String? profileImagePath = user.profileImage;
              if (selectedImage != null) {
                profileImagePath = await _storageService.saveProfileImage(
                  selectedImage!,
                  user.id,
                );
              }

              // تحديث المستخدم
              final updatedUser = user.copyWith(
                name: nameController.text.trim(),
                email: emailController.text.trim(),
                password: passwordController.text.isEmpty
                    ? user.password
                    : passwordController.text,
                profileImage: profileImagePath,
                role: selectedRole,
              );

              final result = await _adminController.updateUser(updatedUser);

              if (result) {
                // تحديث القائمة المحلية فوراً
                await _refreshData();

                Get.back();
                Get.snackbar(
                  'تم بنجاح',
                  'تم تحديث المستخدم بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  'فشل تحديث المستخدم: ${_adminController.error}',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار تفاصيل المستخدم
  void _showUserDetailsDialog(User user) {
    Get.dialog(
      AlertDialog(
        title: const Text('تفاصيل المستخدم'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المستخدم
              Center(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: ImageHelper.buildProfileImage(
                    imagePath: user.profileImage,
                    radius: 60,
                    fallbackText: user.name,
                    backgroundColor: AppColors.primary.withAlpha(51),
                    textColor: AppColors.primary,
                    fallbackIcon: Icons.person,
                  ),
                ),
              ),
              _buildDetailItem('الاسم', user.name),
              _buildDetailItem('البريد الإلكتروني', user.email),
              _buildDetailItem('الدور', _getRoleText(user.role)),
              _buildDetailItem('الحالة', user.isActive ? 'نشط' : 'معطل'),
              _buildDetailItem('تاريخ الإنشاء', _formatDate(user.createdAt)),
              _buildDetailItem(
                'آخر تسجيل دخول',
                user.lastLogin != null
                    ? _formatDate(user.lastLogin!)
                    : 'لم يسجل الدخول',
              ),
              if (user.departmentId != null)
                _buildDetailItem('القسم', user.departmentId.toString()),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _showEditUserDialog(user);
            },
            child: const Text('تعديل'),
          ),
        ],
      ),
    );
  }

  /// تبديل حالة نشاط المستخدم
  void _toggleUserActive(User user) async {
    try {
      final result = await _adminController.toggleUserActive(user.id, !user.isActive);

      if (result) {
        // تحديث القائمة المحلية فوراً
        await _refreshData();

        Get.snackbar(
          'نجح',
          'تم تحديث حالة المستخدم بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تحديث حالة المستخدم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل تغيير حالة المستخدم: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// بناء عنصر تفاصيل
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// الحصول على نص الدور
  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return 'مدير النظام';
      case UserRole.admin:
        return 'مدير عام';
      case UserRole.manager:
        return 'مدير';
      case UserRole.supervisor:
        return 'مشرف';
      case UserRole.user:
        return 'مستخدم';
    }
  }

  /// تنسيق التاريخ
  String _formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return '${date.day}/${date.month}/${date.year}';
  }
}
