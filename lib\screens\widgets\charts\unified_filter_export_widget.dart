import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/advanced_filter_options.dart' as filter_options;
import 'package:flutter_application_2/models/chart_enums.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_styles.dart';
import '../../../utils/chart_type_utils.dart';

/// مكون موحد للفلترة والتصدير
///
/// يوفر واجهة موحدة لتصفية وتصدير المخططات مع تقليل تكرار الأيقونات
class UnifiedFilterExportWidget extends StatelessWidget {
  /// عنوان المخطط
  final String title;

  /// نوع المخطط (مفتاح للتصفية)
  final String chartKey;

  /// دالة التصفية
  final Function(DateTime? startDate, DateTime? endDate,
      TimeFilterType filterType, String chartKey) onFilterChanged;

  /// دالة التصدير
  final Function(String format, String chartTitle) onExport;

  /// دالة تغيير نوع المخطط
  final Function(ChartType chartType, String chartKey)? onChartTypeChanged;

  /// دالة تخصيص المخطط
  final Function()? onCustomizeChart;

  /// تاريخ البداية الحالي
  final DateTime? startDate;

  /// تاريخ النهاية الحالي
  final DateTime? endDate;

  /// نوع الفلتر الحالي
  final TimeFilterType filterType;

  /// نوع المخطط الحالي
  final ChartType? currentChartType;

  /// إظهار أيقونة الفلترة
  final bool showFilter;

  /// إظهار أيقونة التصدير
  final bool showExport;

  /// إظهار أيقونة تغيير نوع المخطط
  final bool showChartTypeSelector;

  /// إظهار أيقونة تخصيص المخطط
  final bool showCustomizeButton;

  /// إظهار أيقونة ملء الشاشة
  final bool showFullscreenButton;

  /// دالة يتم استدعاؤها عند النقر على أيقونة ملء الشاشة
  final Function()? onFullscreenPressed;

  /// أنواع المخططات المدعومة
  final List<ChartType> supportedChartTypes;

  /// أيقونة مخصصة للمخطط
  final IconData? chartIcon;

  /// نوع المخطط الحالي
  final ChartType chartType;

  /// خيارات الفلترة المتقدمة
  final filter_options.AdvancedFilterOptions advancedFilterOptions;

  /// إنشاء مكون موحد للفلترة والتصدير
  const UnifiedFilterExportWidget({
    super.key,
    required this.title,
    required this.chartKey,
    required this.onFilterChanged,
    required this.onExport,
    this.onChartTypeChanged,
    this.onCustomizeChart,
    required this.startDate,
    required this.endDate,
    required this.filterType,
    this.currentChartType,
    this.showFilter = true,
    this.showExport = true,
    this.showChartTypeSelector = true,
    this.showCustomizeButton = true,
    this.showFullscreenButton = false,
    this.onFullscreenPressed,
    this.supportedChartTypes = const [
      ChartType.pie,
      ChartType.bar,
      ChartType.line,
      ChartType.area,
    ],
    this.chartIcon,
    required this.chartType,
    required this.advancedFilterOptions,
  });

  /// الحصول على أنواع المخططات المتاحة بناءً على عنوان المخطط
  List<ChartType> _getAvailableChartTypes(String title) {
    List<ChartType> types = ChartTypeUtils.getAvailableChartTypes(title);

    // إذا كانت القائمة المدعومة محددة، استخدمها بدلاً من القائمة الافتراضية
    if (types.isEmpty || title.isEmpty) {
      return supportedChartTypes;
    }

    return types;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // عنوان المخطط
            Expanded(
              child: Text(
                title,
                style: AppStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blueGrey[800],
                  fontSize: 15,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // أيقونة المخطط
            if (chartIcon != null) ...[
              const SizedBox(width: 4),
              Icon(
                chartIcon,
                color: Colors.blueGrey[600],
                size: 20,
              ),
            ],

            // أيقونات تغيير نوع المخطط
            if (showChartTypeSelector && onChartTypeChanged != null) ...[
              _buildChartTypeIcons(context),
            ],

            // أيقونة تخصيص المخطط
            if (showCustomizeButton && onCustomizeChart != null) ...[
              IconButton(
                icon: const Icon(Icons.palette, size: 18),
                tooltip: 'تخصيص المخطط',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: onCustomizeChart,
              ),
            ],

            // أيقونة ملء الشاشة
            if (showFullscreenButton && onFullscreenPressed != null) ...[
              IconButton(
                icon: const Icon(Icons.fullscreen, size: 18),
                tooltip: 'عرض بملء الشاشة',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: onFullscreenPressed,
              ),
            ],

            // أيقونة الفلترة
            if (showFilter) ...[
              IconButton(
                icon: const Icon(Icons.filter_list_alt, size: 18),
                tooltip: 'تصفية',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () => _showFilterDialog(context),
              ),
            ],

            // أيقونة التصدير
            if (showExport) ...[
              IconButton(
                icon: const Icon(Icons.download, size: 18),
                tooltip: 'تصدير',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () => _showExportDialog(context),
              ),
            ],
          ],
        ),

        // مؤشر الفلتر المطبق والمخطط الحالي
        if (startDate != null && endDate != null) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              if (startDate != null && endDate != null)
                _buildFilterIndicator(context),
              if (showChartTypeSelector && onChartTypeChanged != null) ...[
                const SizedBox(width: 8),
                _buildChartTypeIndicator(context),
              ],
            ],
          ),
        ],
      ],
    );
  }

  /// بناء مؤشر الفلتر المطبق
  Widget _buildFilterIndicator(BuildContext context) {
    if (startDate == null || endDate == null) {
      return const SizedBox.shrink();
    }

    String filterText;
    switch (filterType) {
      case TimeFilterType.day:
        filterText = 'اليوم';
        break;
      case TimeFilterType.week:
        filterText = 'الأسبوع';
        break;
      case TimeFilterType.month:
        filterText = 'الشهر';
        break;
      case TimeFilterType.quarter:
        filterText = 'الربع';
        break;
      case TimeFilterType.year:
        filterText = 'السنة';
        break;
      case TimeFilterType.custom:
        filterText =
            '${DateFormat('yyyy/MM/dd').format(startDate!)} - ${DateFormat('yyyy/MM/dd').format(endDate!)}';
        break;
      case TimeFilterType.all:
        filterText = 'الكل';
        break;
    }

    return InkWell(
      onTap: () {
        // إظهار مربع حوار التصفية عند النقر على المؤشر
        _showFilterDialog(context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.blue.withAlpha(30),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.filter_list, size: 12, color: Colors.blue),
            const SizedBox(width: 4),
            Text(
              filterText,
              style: const TextStyle(fontSize: 10, color: Colors.blue),
            ),
            const SizedBox(width: 4),
            // زر إلغاء الفلتر
            InkWell(
              onTap: () {
                // إلغاء الفلتر وإعادة تعيينه إلى الكل
                onFilterChanged(null, null, TimeFilterType.all, chartKey);
              },
              child: const Icon(
                Icons.cancel,
                size: 12,
                color: Colors.blue,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر نوع المخطط الحالي
  Widget _buildChartTypeIndicator(BuildContext context) {
    String chartTypeName;
    switch (chartType) {
      case ChartType.pie:
        chartTypeName = 'دائري';
        break;
      case ChartType.bar:
        chartTypeName = 'شريطي';
        break;
      case ChartType.line:
        chartTypeName = 'خطي';
        break;
      case ChartType.area:
        chartTypeName = 'مساحي';
        break;
      case ChartType.scatter:
        chartTypeName = 'انتشاري';
        break;
      case ChartType.bubble:
        chartTypeName = 'فقاعي';
        break;
      case ChartType.radar:
        chartTypeName = 'راداري';
        break;
      case ChartType.gauge:
        chartTypeName = 'مقياس';
        break;
      case ChartType.funnel:
        chartTypeName = 'قمعي';
        break;
      case ChartType.treemap:
        chartTypeName = 'شجري';
        break;
      case ChartType.heatmap:
        chartTypeName = 'حراري';
        break;
      case ChartType.donut:
        chartTypeName = 'حلقي';
        break;
      case ChartType.gantt:
        chartTypeName = 'جانت';
        break;
      case ChartType.table:
        chartTypeName = 'جدول';
        break;
      case ChartType.waterfall:
        chartTypeName = 'شلال';
        break;
      case ChartType.candlestick:
        chartTypeName = 'شموع';
        break;
      case ChartType.boxplot:
        chartTypeName = 'صندوقي';
        break;
      case ChartType.network:
        chartTypeName = 'علاقات';
        break;
      default:
        chartTypeName = 'مخطط';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.teal.withAlpha(30),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getIconForChartType(chartType), size: 12, color: Colors.teal),
          const SizedBox(width: 4),
          Text(
            chartTypeName,
            style: const TextStyle(fontSize: 10, color: Colors.teal),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار الفلترة
  void _showFilterDialog(BuildContext context) {
    // نسخ قيم الفلتر الحالية للتعديل
    DateTime? tempStartDate = startDate;
    DateTime? tempEndDate = endDate;
    TimeFilterType tempFilterType = filterType;

    // تسجيل معلومات الفلتر الحالي للتصحيح
    debugPrint('فتح مربع حوار التصفية للمخطط: $chartKey');
    debugPrint('الفلتر الحالي: $filterType');
    debugPrint('تاريخ البداية الحالي: $startDate');
    debugPrint('تاريخ النهاية الحالي: $endDate');

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Row(
                children: [
                  const Icon(Icons.filter_list, color: Colors.blue),
                  const SizedBox(width: 8),
                  Text('تصفية $title'),
                ],
              ),
              content: SizedBox(
                width: 300,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان فرعي
                      const Text(
                        'اختر نطاق زمني:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),

                      // خيارات التصفية
                      ...TimeFilterType.values.map((type) {
                        String label;
                        IconData icon;

                        switch (type) {
                          case TimeFilterType.day:
                            label = 'اليوم';
                            icon = Icons.today;
                            break;
                          case TimeFilterType.week:
                            label = 'الأسبوع';
                            icon = Icons.view_week;
                            break;
                          case TimeFilterType.month:
                            label = 'الشهر';
                            icon = Icons.calendar_month;
                            break;
                          case TimeFilterType.quarter:
                            label = 'الربع';
                            icon = Icons.calendar_view_month;
                            break;
                          case TimeFilterType.year:
                            label = 'السنة';
                            icon = Icons.calendar_today;
                            break;
                          case TimeFilterType.custom:
                            label = 'فترة مخصصة';
                            icon = Icons.date_range;
                            break;
                          case TimeFilterType.all:
                            label = 'الكل';
                            icon = Icons.all_inclusive;
                            break;
                        }

                        return RadioListTile<TimeFilterType>(
                          title: Row(
                            children: [
                              Icon(icon,
                                  size: 18,
                                  color: tempFilterType == type
                                      ? Colors.blue
                                      : Colors.grey),
                              const SizedBox(width: 8),
                              Text(label),
                            ],
                          ),
                          value: type,
                          groupValue: tempFilterType,
                          activeColor: Colors.blue,
                          onChanged: (value) {
                            setState(() {
                              tempFilterType = value!;
                              debugPrint(
                                  'تم اختيار نوع التصفية: $tempFilterType');

                              // تحديث التواريخ بناءً على نوع الفلتر
                              final now = DateTime.now();

                              switch (tempFilterType) {
                                case TimeFilterType.day:
                                  tempStartDate =
                                      DateTime(now.year, now.month, now.day);
                                  tempEndDate = DateTime(
                                      now.year, now.month, now.day, 23, 59, 59);
                                  break;
                                case TimeFilterType.week:
                                  // بداية الأسبوع (السبت)
                                  final weekStart = now.subtract(
                                      Duration(days: now.weekday % 7));
                                  tempStartDate = DateTime(weekStart.year,
                                      weekStart.month, weekStart.day);
                                  // نهاية الأسبوع (الجمعة)
                                  tempEndDate = tempStartDate!.add(
                                      const Duration(
                                          days: 6,
                                          hours: 23,
                                          minutes: 59,
                                          seconds: 59));
                                  break;
                                case TimeFilterType.month:
                                  tempStartDate =
                                      DateTime(now.year, now.month, 1);
                                  tempEndDate = DateTime(
                                      now.year, now.month + 1, 0, 23, 59, 59);
                                  break;
                                case TimeFilterType.quarter:
                                  final quarterStart =
                                      (now.month - 1) ~/ 3 * 3 + 1;
                                  tempStartDate =
                                      DateTime(now.year, quarterStart, 1);
                                  tempEndDate = DateTime(now.year,
                                      quarterStart + 3, 0, 23, 59, 59);
                                  break;
                                case TimeFilterType.year:
                                  tempStartDate = DateTime(now.year, 1, 1);
                                  tempEndDate =
                                      DateTime(now.year, 12, 31, 23, 59, 59);
                                  break;
                                case TimeFilterType.all:
                                  tempStartDate = null;
                                  tempEndDate = null;
                                  break;
                                case TimeFilterType.custom:
                                  // لا تغير التواريخ للتصفية المخصصة
                                  tempStartDate ??= DateTime.now()
                                      .subtract(const Duration(days: 30));
                                  tempEndDate ??= DateTime.now();
                                  break;
                              }

                              debugPrint(
                                  'تاريخ البداية الجديد: $tempStartDate');
                              debugPrint('تاريخ النهاية الجديد: $tempEndDate');
                            });
                          },
                        );
                      }),

                      // خيار التاريخ المخصص
                      if (tempFilterType == TimeFilterType.custom) ...[
                        const SizedBox(height: 16),
                        const Text(
                          'اختر فترة مخصصة:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'من',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  prefixIcon: Icon(Icons.calendar_today),
                                ),
                                readOnly: true,
                                controller: TextEditingController(
                                  text: tempStartDate != null
                                      ? DateFormat('yyyy/MM/dd')
                                          .format(tempStartDate!)
                                      : '',
                                ),
                                onTap: () async {
                                  final pickedDate = await showDatePicker(
                                    context: context,
                                    initialDate:
                                        tempStartDate ?? DateTime.now(),
                                    firstDate: DateTime(2020),
                                    lastDate: DateTime.now()
                                        .add(const Duration(days: 365)),
                                  );
                                  if (pickedDate != null) {
                                    setState(() {
                                      tempStartDate = pickedDate;
                                      debugPrint(
                                          'تم اختيار تاريخ البداية: $tempStartDate');
                                    });
                                  }
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'إلى',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  prefixIcon: Icon(Icons.calendar_today),
                                ),
                                readOnly: true,
                                controller: TextEditingController(
                                  text: tempEndDate != null
                                      ? DateFormat('yyyy/MM/dd')
                                          .format(tempEndDate!)
                                      : '',
                                ),
                                onTap: () async {
                                  final pickedDate = await showDatePicker(
                                    context: context,
                                    initialDate: tempEndDate ?? DateTime.now(),
                                    firstDate: DateTime(2020),
                                    lastDate: DateTime.now()
                                        .add(const Duration(days: 365)),
                                  );
                                  if (pickedDate != null) {
                                    setState(() {
                                      tempEndDate = DateTime(
                                        pickedDate.year,
                                        pickedDate.month,
                                        pickedDate.day,
                                        23,
                                        59,
                                        59,
                                      );
                                      debugPrint(
                                          'تم اختيار تاريخ النهاية: $tempEndDate');
                                    });
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('إلغاء'),
                ),
                if (startDate != null)
                  TextButton(
                    onPressed: () {
                      // إعادة تعيين الفلتر إلى الشهر الحالي
                      final now = DateTime.now();
                      final defaultStartDate = DateTime(now.year, now.month, 1);
                      final defaultEndDate =
                          DateTime(now.year, now.month + 1, 0, 23, 59, 59);

                      debugPrint('إعادة تعيين الفلتر إلى الشهر الحالي');
                      onFilterChanged(defaultStartDate, defaultEndDate,
                          TimeFilterType.month, chartKey);
                      Navigator.of(context).pop();
                    },
                    child: const Text('إعادة تعيين'),
                  ),
                ElevatedButton(
                  onPressed: () {
                    // تطبيق الفلتر
                    debugPrint('تطبيق الفلتر: $tempFilterType');
                    debugPrint(
                        'تاريخ البداية: $tempStartDate، تاريخ النهاية: $tempEndDate');

                    onFilterChanged(
                        tempStartDate, tempEndDate, tempFilterType, chartKey);
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // تم نقل محتوى هذه الدالة إلى دالة _showFilterDialog

  /// بناء مكون لعرض رسالة عندما لا توجد بيانات بسبب الفلتر
  ///
  /// يمكن استخدام هذا المكون في المخططات لعرض رسالة عندما لا توجد بيانات
  /// مع زر لإلغاء الفلتر
  static Widget buildNoDataMessage(
    BuildContext context, {
    required String message,
    required Function() onCancelFilter,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.filter_list_off,
            size: 48,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: onCancelFilter,
            icon: const Icon(Icons.cancel),
            label: const Text('إلغاء الفلتر'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار تغيير نوع المخطط
  void _showChartTypeDialog(BuildContext context) {
    if (onChartTypeChanged == null) return;

    // استخدام الدالة المحلية للحصول على أنواع المخططات المتاحة بناءً على العنوان
    final availableTypes = _getAvailableChartTypes(title);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تغيير نوع مخطط $title'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: availableTypes.map((type) {
              IconData icon = Icons.insert_chart;
              String label = 'مخطط';

              switch (type) {
                case ChartType.pie:
                  icon = Icons.pie_chart;
                  label = 'مخطط دائري';
                  break;
                case ChartType.bar:
                  icon = Icons.bar_chart;
                  label = 'مخطط شريطي';
                  break;
                case ChartType.line:
                  icon = Icons.show_chart;
                  label = 'مخطط خطي';
                  break;
                case ChartType.area:
                  icon = Icons.area_chart;
                  label = 'مخطط مساحي';
                  break;
                case ChartType.scatter:
                  icon = Icons.scatter_plot;
                  label = 'مخطط انتشاري';
                  break;
                case ChartType.bubble:
                  icon = Icons.bubble_chart;
                  label = 'مخطط فقاعي';
                  break;
                case ChartType.radar:
                  icon = Icons.radar;
                  label = 'مخطط راداري';
                  break;
                case ChartType.gauge:
                  icon = Icons.speed;
                  label = 'مخطط مقياس';
                  break;
                case ChartType.funnel:
                  icon = Icons.filter_alt;
                  label = 'مخطط قمعي';
                  break;
                case ChartType.treemap:
                  icon = Icons.dashboard;
                  label = 'خريطة شجرية';
                  break;
                case ChartType.heatmap:
                  icon = Icons.grid_on;
                  label = 'خريطة حرارية';
                  break;
                case ChartType.donut:
                  icon = Icons.donut_large;
                  label = 'مخطط حلقي';
                  break;
                case ChartType.gantt:
                  icon = Icons.stacked_bar_chart;
                  label = 'مخطط جانت';
                  break;
                case ChartType.table:
                  icon = Icons.table_chart;
                  label = 'جدول';
                  break;
                case ChartType.waterfall:
                  icon = Icons.waterfall_chart;
                  label = 'مخطط شلال';
                  break;
                case ChartType.candlestick:
                  icon = Icons.candlestick_chart;
                  label = 'مخطط شموع';
                  break;
                case ChartType.boxplot:
                  icon = Icons.analytics;
                  label = 'مخطط صندوقي';
                  break;
                case ChartType.network:
                  icon = Icons.share;
                  label = 'مخطط العلاقات';
                  break;
                case ChartType.sankey:
                  icon = Icons.account_tree;
                  label = 'مخطط سانكي';
                  break;
              }

              return ListTile(
                leading:
                    Icon(icon, color: chartType == type ? Colors.blue : null),
                title: Text(
                  label,
                  style: TextStyle(
                    fontWeight:
                        chartType == type ? FontWeight.bold : FontWeight.normal,
                    color: chartType == type ? Colors.blue : null,
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  onChartTypeChanged!(type, chartKey);
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  /// عرض مربع حوار التصدير
  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تصدير $title'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
                title: const Text('تصدير كـ PDF'),
                onTap: () {
                  Navigator.of(context).pop();
                  onExport('pdf', title);
                },
              ),
              ListTile(
                leading: const Icon(Icons.table_chart, color: Colors.green),
                title: const Text('تصدير كـ Excel'),
                onTap: () {
                  Navigator.of(context).pop();
                  onExport('excel', title);
                },
              ),
              ListTile(
                leading: const Icon(Icons.description, color: Colors.blue),
                title: const Text('تصدير كـ CSV'),
                onTap: () {
                  Navigator.of(context).pop();
                  onExport('csv', title);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  /// الحصول على أيقونة نوع المخطط
  IconData _getIconForChartType(ChartType type) {
    return ChartTypeUtils.getChartTypeIcon(type);
  }

  /// بناء أيقونات تغيير نوع المخطط
  Widget _buildChartTypeIcons(BuildContext context) {
    // استخدام الدالة المحلية للحصول على أنواع المخططات المتاحة بناءً على العنوان
    final availableTypes = _getAvailableChartTypes(title);

    // عرض جميع أنواع المخططات المتاحة كأيقونات (بحد أقصى 7 أيقونات)
    final displayTypes = availableTypes.take(7).toList();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (final type in displayTypes)
          Builder(builder: (context) {
            // استخدام chartType بدلاً من currentChartType
            final isSelected = chartType == type;
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 2.0),
              child: InkWell(
                onTap: () {
                  if (onChartTypeChanged != null) {
                    onChartTypeChanged!(type, chartKey);
                  }
                },
                borderRadius: BorderRadius.circular(4),
                child: Tooltip(
                  message: ChartTypeUtils.getChartTypeLabel(type),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Colors.blue.withAlpha(50)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(4),
                      border: isSelected
                          ? Border.all(color: Colors.blue, width: 1)
                          : null,
                    ),
                    child: Icon(
                      _getIconForChartType(type),
                      size: 18,
                      color: isSelected ? Colors.blue[700] : Colors.grey[600],
                    ),
                  ),
                ),
              ),
            );
          }),

        // زر لعرض المزيد من أنواع المخططات إذا كان هناك أكثر من 7 أنواع
        if (availableTypes.length > 7)
          IconButton(
            icon: const Icon(Icons.more_horiz, size: 18),
            tooltip: 'المزيد من أنواع المخططات',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            onPressed: () => _showChartTypeDialog(context),
          ),
      ],
    );
  }
}
