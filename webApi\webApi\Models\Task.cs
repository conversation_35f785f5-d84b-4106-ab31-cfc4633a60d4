﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace webApi.Models;

public partial class Task
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public int? TaskTypeId { get; set; }

    public int CreatorId { get; set; }

    public int? AssigneeId { get; set; }

    public int? DepartmentId { get; set; }

    public long CreatedAt { get; set; }

    public long? StartDate { get; set; }

    public long? DueDate { get; set; }

    public long? CompletedAt { get; set; }

    public int Status { get; set; }

    public int Priority { get; set; }

    public int CompletionPercentage { get; set; }

    public int? EstimatedTime { get; set; }

    public int? ActualTime { get; set; }

    public bool IsDeleted { get; set; }

    public virtual User? Assignee { get; set; }

    [JsonIgnore]
    public virtual ICollection<Attachment> Attachments { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<CalendarEvent> CalendarEvents { get; set; } = [];

    [JsonIgnore]
    public virtual User? Creator { get; set; }

    public virtual Department? Department { get; set; }

    [JsonIgnore]
    public virtual TaskPriority? PriorityNavigation { get; set; }

    [JsonIgnore]
    public virtual TaskStatus? StatusNavigation { get; set; }

    [JsonIgnore]
    public virtual ICollection<Subtask> Subtasks { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<TaskComment> TaskComments { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<TaskHistory> TaskHistories { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<TaskProgressTracker> TaskProgressTrackers { get; set; } = [];

    public virtual TaskType? TaskType { get; set; }

    [JsonIgnore]
    public virtual ICollection<TimeTrackingEntry> TimeTrackingEntries { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<ActivityLog> ActivityLogs { get; set; } = [];

    [JsonIgnore]
    public virtual ICollection<User> Users { get; set; } = [];
}
