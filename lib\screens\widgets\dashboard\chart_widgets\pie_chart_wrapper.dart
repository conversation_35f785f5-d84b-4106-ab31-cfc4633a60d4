import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../models/chart_enums.dart';
import '../../../../models/dashboard_models.dart';
import '../../../../models/task_status_enum.dart';
import '../../../../models/advanced_filter_options.dart';
import '../../../../controllers/task_controller.dart';
import '../../charts/enhanced_pie_chart.dart' as enhanced;

/// مغلف مخطط دائري
///
/// يعرض مخططًا دائريًا لحالة المهام
///
/// ملاحظة: يوصى باستخدام EnhancedPieChart من مجلد widgets/charts بدلاً من هذا المكون
/// لتوحيد واجهة المستخدم وتقليل التكرار في الكود.
/// راجع ملف DASHBOARD_REFACTORING.md للمزيد من المعلومات.
class PieChartWrapper extends StatefulWidget {
  /// عنصر لوحة المعلومات
  final DashboardWidget widget;

  /// إعدادات المخطط
  final Map<String, dynamic> settings;

  /// دالة يتم استدعاؤها عند تحديث الإعدادات
  final Function(DashboardWidget, Map<String, dynamic>)? onSettingsUpdated;

  /// ما إذا كان عرض تفاصيل
  final bool isDetailView;

  const PieChartWrapper({
    super.key,
    required this.widget,
    required this.settings,
    this.onSettingsUpdated,
    this.isDetailView = false,
  });

  @override
  State<PieChartWrapper> createState() => _PieChartWrapperState();
}

class _PieChartWrapperState extends State<PieChartWrapper> {
  // تحكم المهام
  final TaskController _taskController = Get.find<TaskController>();

  // تم إزالة _touchedIndex لأنه غير مستخدم

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final tasks = _taskController.allTasks;
      if (tasks.isEmpty) {
        return Center(
          child: Text(
            'لا توجد مهام',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
        );
      }

      // تجميع المهام حسب الحالة
      final Map<TaskStatus, int> tasksByStatus = {};
      for (final task in tasks) {
        final status = TaskStatus.fromId(task.status);
        tasksByStatus[status] = (tasksByStatus[status] ?? 0) + 1;
      }

      // تحويل البيانات إلى التنسيق المطلوب للمخطط المحسن
      final Map<String, double> chartData = {};
      final Map<String, Color> sectionColors = {};

      tasksByStatus.forEach((status, count) {
        final statusName = _getStatusName(status);
        chartData[statusName] = count.toDouble();
        sectionColors[statusName] = _getStatusColor(status);
      });

      // استخدام EnhancedPieChart بدلاً من التنفيذ المباشر مع تصميم Monday.com
      return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(25),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان المخطط بأسلوب Monday.com
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'توزيع المهام حسب الحالة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  Row(
                    children: [
                      // زر تصفية
                      IconButton(
                        icon: const Icon(Icons.filter_list, size: 18),
                        onPressed: () {
                          // عرض خيارات التصفية
                        },
                        tooltip: 'تصفية',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        splashRadius: 20,
                      ),
                      const SizedBox(width: 8),
                      // زر تصدير
                      IconButton(
                        icon: const Icon(Icons.more_vert, size: 18),
                        onPressed: () {
                          // عرض خيارات التصدير
                        },
                        tooltip: 'المزيد من الخيارات',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        splashRadius: 20,
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // المخطط نفسه
            Expanded(
              child: enhanced.EnhancedPieChart(
                data: chartData,
                sectionColors: sectionColors,
                title: '', // إزالة العنوان لأننا أضفناه بالفعل
                showValues: widget.settings['showValues'] == true,
                showPercentages: widget.settings['showPercentages'] == true,
                showLegend: widget.settings['showLegend'] == true,
                radius: 100,
                borderWidth: 1,
                borderColor: Colors.white,
                sectionsSpace: 2,
                showFilterOptions: false,
                showExportOptions: false,
                chartType: ChartType.pie,
                advancedFilterOptions: AdvancedFilterOptions(),
              ),
            ),

            // وسيلة الإيضاح المحسنة بأسلوب Monday.com
            if (widget.settings['showLegend'] == true) ...[
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: _buildMondayStyleLegend(tasksByStatus),
              ),
            ],
          ],
        ),
      );
    });
  }

  /// بناء وسيلة الإيضاح بأسلوب Monday.com
  Widget _buildMondayStyleLegend(Map<TaskStatus, int> tasksByStatus) {
    final List<Widget> legendItems = [];
    final int totalTasks =
        tasksByStatus.values.fold(0, (sum, count) => sum + count);

    tasksByStatus.forEach((status, count) {
      final double percentage = (count / totalTasks) * 100;

      legendItems.add(
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              // مربع اللون
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getStatusColor(status),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),

              // اسم الحالة
              Expanded(
                child: Text(
                  _getStatusName(status),
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              // العدد والنسبة المئوية
              Text(
                '$count (${percentage.toStringAsFixed(1)}%)',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // عنوان وسيلة الإيضاح
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Text(
            'توزيع المهام',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
        ),

        // عناصر وسيلة الإيضاح
        ...legendItems,
      ],
    );
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.waitingForInfo:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
      case TaskStatus.news:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على اسم الحالة
  String _getStatusName(TaskStatus status) {
    return status.displayNameAr;
  }
}
