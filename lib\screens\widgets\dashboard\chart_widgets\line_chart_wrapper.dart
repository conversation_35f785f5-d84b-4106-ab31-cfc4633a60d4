import 'package:flutter/material.dart';
import 'package:flutter_application_2/screens/widgets/charts/enhanced_line_chart.dart' as enhanced;
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../models/chart_enums.dart';
import '../../../../models/dashboard_models.dart';
import '../../../../models/advanced_filter_options.dart';
import '../../../../controllers/task_controller.dart';
import '../../../../models/task_status_enum.dart';



/// مغلف مخطط خطي
///
/// يعرض مخططًا خطيًا لتقدم المهام أو تتبع الوقت
///
/// ملاحظة: يوصى باستخدام EnhancedLineChart من مجلد widgets/charts بدلاً من هذا المكون
/// لتوحيد واجهة المستخدم وتقليل التكرار في الكود.
/// راجع ملف DASHBOARD_REFACTORING.md للمزيد من المعلومات.
class LineChartWrapper extends StatefulWidget {
  /// عنصر لوحة المعلومات
  final DashboardWidget widget;

  /// إعدادات المخطط
  final Map<String, dynamic> settings;

  /// دالة يتم استدعاؤها عند تحديث الإعدادات
  final Function(DashboardWidget, Map<String, dynamic>)? onSettingsUpdated;

  /// ما إذا كان عرض تفاصيل
  final bool isDetailView;

  const LineChartWrapper({
    super.key,
    required this.widget,
    required this.settings,
    this.onSettingsUpdated,
    this.isDetailView = false,
  });

  @override
  State<LineChartWrapper> createState() => _LineChartWrapperState();
}

class _LineChartWrapperState extends State<LineChartWrapper> {
  // تحكم المهام
  final TaskController _taskController = Get.find<TaskController>();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (widget.widget.type == 'taskProgressChart') {
        return _buildTaskProgressChart();
      } else {
        return _buildTimeTrackingChart();
      }
    });
  }

  /// بناء مخطط تقدم المهام
  Widget _buildTaskProgressChart() {
    final tasks = _taskController.allTasks;

    if (tasks.isEmpty) {
      return Center(
        child: Text(
          'لا توجد مهام',
          style: TextStyle(
            color: Colors.grey,
          ),
        ),
      );
    }

    // تحديد نطاق الوقت
    final timeRange = widget.settings['timeRange'] ?? 'month';
    final DateTime now = DateTime.now();
    DateTime startDate;

    switch (timeRange) {
      case 'week':
        startDate = now.subtract(const Duration(days: 7));
        break;
      case 'month':
        startDate = DateTime(now.year, now.month - 1, now.day);
        break;
      case 'quarter':
        startDate = DateTime(now.year, now.month - 3, now.day);
        break;
      case 'year':
        startDate = DateTime(now.year - 1, now.month, now.day);
        break;
      default:
        startDate = DateTime(now.year, now.month - 1, now.day);
    }

    // تجميع المهام حسب التاريخ
    final Map<DateTime, int> completedTasksByDate = {};
    final Map<DateTime, int> createdTasksByDate = {};

    // تهيئة القواميس بالتواريخ
    DateTime current = startDate;
    while (current.isBefore(now) || current.isAtSameMomentAs(now)) {
      final date = DateTime(current.year, current.month, current.day);
      completedTasksByDate[date] = 0;
      createdTasksByDate[date] = 0;
      current = current.add(const Duration(days: 1));
    }

    // تجميع المهام
    for (final task in tasks) {
      final createdAt = DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000);
      if (createdAt.isAfter(startDate)) {
        final date = DateTime(createdAt.year, createdAt.month, createdAt.day);
        createdTasksByDate[date] = (createdTasksByDate[date] ?? 0) + 1;
      }

      if (task.status == TaskStatus.completed.id) {
        final completedAt = task.completedAt != null
            ? DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000)
            : null;
        if (completedAt != null && completedAt.isAfter(startDate)) {
          final date =
              DateTime(completedAt.year, completedAt.month, completedAt.day);
          completedTasksByDate[date] = (completedTasksByDate[date] ?? 0) + 1;
        }
      }
    }

    // تحويل البيانات إلى التنسيق المطلوب للمخطط المحسن
    final Map<String, Map<String, double>> chartData = {};
    final Map<String, Color> lineColors = {};

    // تراكمي للمهام
    int cumulativeCompleted = 0;
    int cumulativeCreated = 0;

    // ترتيب التواريخ
    final sortedDates = completedTasksByDate.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    // إنشاء خرائط البيانات للمهام المكتملة والمنشأة
    final Map<String, double> completedData = {};
    final Map<String, double> createdData = {};

    for (int i = 0; i < sortedDates.length; i++) {
      final date = sortedDates[i];
      final dateKey = DateFormat('MM/dd').format(date);

      cumulativeCompleted += completedTasksByDate[date] ?? 0;
      completedData[dateKey] = cumulativeCompleted.toDouble();

      cumulativeCreated += createdTasksByDate[date] ?? 0;
      createdData[dateKey] = cumulativeCreated.toDouble();
    }

    // إضافة البيانات إلى القاموس
    chartData['المهام المكتملة'] = completedData;
    chartData['المهام المنشأة'] = createdData;

    // إضافة الألوان
    lineColors['المهام المكتملة'] = Colors.green;
    lineColors['المهام المنشأة'] = Colors.blue;

    // استخدام EnhancedLineChart بدلاً من التنفيذ المباشر مع تصميم Monday.com
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المخطط بأسلوب Monday.com
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'تقدم المهام',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                Row(
                  children: [
                    // زر تصفية
                    IconButton(
                      icon: const Icon(Icons.filter_list, size: 18),
                      onPressed: () {
                        // عرض خيارات التصفية
                      },
                      tooltip: 'تصفية',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 20,
                    ),
                    const SizedBox(width: 8),
                    // زر تصدير
                    IconButton(
                      icon: const Icon(Icons.more_vert, size: 18),
                      onPressed: () {
                        // عرض خيارات التصدير
                      },
                      tooltip: 'المزيد من الخيارات',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 20,
                    ),
                  ],
                ),
              ],
            ),
          ),

          // المخطط نفسه - تحسين مع SizedBox لضمان وجود ارتفاع
          Expanded(
            child: SizedBox(
              width: double.infinity,
              height: 300, // ارتفاع افتراضي
              child: enhanced.EnhancedLineChart(
                data: chartData,
                lineColors: lineColors,
                title: '', // إزالة العنوان لأننا أضفناه بالفعل
                xAxisTitle: 'التاريخ',
                yAxisTitle: 'عدد المهام',
                showGrid: widget.settings['showGrid'] == true,
                showDots: widget.settings['showDots'] == true,
                showBelowArea: widget.settings['showBelowArea'] == true,
                formatXAsDate: true,
                dateFormat: 'yyyy-MM-dd',
                showFilterOptions: false,
                showExportOptions: false,
                chartType: ChartType.line,
                advancedFilterOptions: const AdvancedFilterOptions(),
                // تفعيل نمط Monday.com
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مخطط تتبع الوقت
  Widget _buildTimeTrackingChart() {
    final tasks = _taskController.allTasks;

    if (tasks.isEmpty) {
      return Center(
        child: Text(
          'لا توجد مهام',
          style: TextStyle(
            color: Colors.grey,
          ),
        ),
      );
    }

    // تحديد نطاق الوقت
    final timeRange = widget.settings['timeRange'] ?? 'week';
    final DateTime now = DateTime.now();
    DateTime startDate;

    switch (timeRange) {
      case 'week':
        startDate = now.subtract(const Duration(days: 7));
        break;
      case 'month':
        startDate = DateTime(now.year, now.month - 1, now.day);
        break;
      case 'quarter':
        startDate = DateTime(now.year, now.month - 3, now.day);
        break;
      case 'year':
        startDate = DateTime(now.year - 1, now.month, now.day);
        break;
      default:
        startDate = DateTime(now.year, now.month - 1, now.day);
    }

    // تجميع ساعات العمل حسب التاريخ
    final Map<DateTime, double> hoursWorkedByDate = {};

    // تهيئة القاموس بالتواريخ
    DateTime current = startDate;
    while (current.isBefore(now) || current.isAtSameMomentAs(now)) {
      final date = DateTime(current.year, current.month, current.day);
      hoursWorkedByDate[date] = 0;
      current = current.add(const Duration(days: 1));
    }

    // تجميع ساعات العمل (بيانات تجريبية)
    // في التطبيق الفعلي، ستأتي هذه البيانات من سجلات تتبع الوقت
    for (int i = 0; i < 30; i++) {
      final date = startDate.add(Duration(days: i));
      if (date.isBefore(now) || date.isAtSameMomentAs(now)) {
        final normalizedDate = DateTime(date.year, date.month, date.day);
        // إنشاء بيانات عشوائية للعرض التجريبي
        if (normalizedDate.weekday <= 5) {
          // أيام العمل فقط (من الاثنين إلى الجمعة)
          hoursWorkedByDate[normalizedDate] =
              (3 + (normalizedDate.day % 5) + (normalizedDate.weekday % 3))
                  .toDouble();
        }
      }
    }

    // تحويل البيانات إلى التنسيق المطلوب للمخطط المحسن (Syncfusion)
    final Map<String, Map<String, double>> chartData = {};
    final Map<String, Color> lineColors = {};

    // ترتيب التواريخ
    final sortedDates = hoursWorkedByDate.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    // إنشاء خريطة بيانات ساعات العمل
    final Map<String, double> hoursData = {};
    for (int i = 0; i < sortedDates.length; i++) {
      final date = sortedDates[i];
      final dateKey = DateFormat('MM/dd').format(date);
      final hours = hoursWorkedByDate[date] ?? 0;
      hoursData[dateKey] = hours.toDouble();
    }

    // إضافة البيانات إلى القاموس
    chartData['ساعات العمل'] = hoursData;

    // إضافة الألوان
    lineColors['ساعات العمل'] = Colors.purple;

    // استخدام EnhancedLineChart بدلاً من التنفيذ المباشر مع تصميم Monday.com
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المخطط بأسلوب Monday.com
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'تتبع ساعات العمل',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                Row(
                  children: [
                    // زر تصفية
                    IconButton(
                      icon: const Icon(Icons.filter_list, size: 18),
                      onPressed: () {
                        // عرض خيارات التصفية
                      },
                      tooltip: 'تصفية',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 20,
                    ),
                    const SizedBox(width: 8),
                    // زر تصدير
                    IconButton(
                      icon: const Icon(Icons.more_vert, size: 18),
                      onPressed: () {
                        // عرض خيارات التصدير
                      },
                      tooltip: 'المزيد من الخيارات',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 20,
                    ),
                  ],
                ),
              ],
            ),
          ),

          // المخطط نفسه - تحسين مع SizedBox لضمان وجود ارتفاع
          Expanded(
            child: SizedBox(
              width: double.infinity,
              height: 300, // ارتفاع افتراضي
              child: enhanced.EnhancedLineChart(
                data: chartData,
                lineColors: lineColors,
                title: '', // إزالة العنوان لأننا أضفناه بالفعل
                xAxisTitle: 'التاريخ',
                yAxisTitle: 'الساعات',
                showGrid: widget.settings['showGrid'] == true,
                showDots: widget.settings['showDots'] == true,
                showBelowArea: widget.settings['showBelowArea'] == true,
                formatXAsDate: true,
                dateFormat: 'yyyy-MM-dd',
                showFilterOptions: false,
                showExportOptions: false,
                chartType: ChartType.line,
                advancedFilterOptions: const AdvancedFilterOptions(),
                // تفعيل نمط Monday.com
              ),
            ),
          ),
        ],
      ),
    );
  }
}
