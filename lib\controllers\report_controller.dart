import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../models/enhanced_report_model.dart';
import '../models/exported_report_info.dart';
import '../models/report_models.dart';
import '../models/reporting/report_result_model.dart';
import '../services/api/reports_api_service.dart';

/// وحدة تحكم التقارير متوافقة مع ASP.NET Core API
class ReportController extends GetxController {
  final ReportsApiService _apiService = ReportsApiService();
  
  // حالة التحميل والأخطاء
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  
  // التقارير
  final RxList<EnhancedReport> reports = <EnhancedReport>[].obs;
  final RxList<EnhancedReport> sharedReports = <EnhancedReport>[].obs;
  final RxList<EnhancedReport> favoriteReports = <EnhancedReport>[].obs;
  final RxList<EnhancedReport> publicReports = <EnhancedReport>[].obs;
  
  // التقرير الحالي ونتيجته
  final Rx<EnhancedReport?> currentReport = Rx<EnhancedReport?>(null);
  final Rx<ReportResult?> currentResult = Rx<ReportResult?>(null);
  
  // إحصائيات
  final RxMap<String, dynamic> tasksStatistics = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> usersStatistics = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> userPerformance = <String, dynamic>{}.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }

  /// تهيئة وحدة التحكم
  Future<void> _initializeController() async {
    try {
      await _apiService.initialize();
      debugPrint('تم تهيئة ReportController بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة ReportController: $e');
      error.value = 'خطأ في تهيئة النظام: ${e.toString()}';
    }
  }

  /// تحميل جميع التقارير
  Future<void> loadReports() async {
    try {
      isLoading.value = true;
      error.value = '';

      // تحميل التقارير من API
      final apiReports = await _apiService.getReports();
      
      // تحويل إلى EnhancedReport
      final enhancedReports = apiReports.map((report) => 
        EnhancedReport.fromReport(report)).toList();
      
      reports.value = enhancedReports;
      
      // فلترة التقارير حسب النوع
      _filterReports();
      
    } catch (e) {
      error.value = 'خطأ في تحميل التقارير: ${e.toString()}';
      debugPrint('خطأ في تحميل التقارير: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل التقارير العامة
  Future<void> loadPublicReports() async {
    try {
      isLoading.value = true;
      error.value = '';

      final apiReports = await _apiService.getPublicReports();
      final enhancedReports = apiReports.map((report) => 
        EnhancedReport.fromReport(report)).toList();
      
      publicReports.value = enhancedReports;
      
    } catch (e) {
      error.value = 'خطأ في تحميل التقارير العامة: ${e.toString()}';
      debugPrint('خطأ في تحميل التقارير العامة: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل تقرير محدد
  Future<void> loadReport(String reportId) async {
    try {
      isLoading.value = true;
      error.value = '';

      final report = await _apiService.getReport(int.parse(reportId));
      currentReport.value = EnhancedReport.fromReport(report);
      
    } catch (e) {
      error.value = 'خطأ في تحميل التقرير: ${e.toString()}';
      debugPrint('خطأ في تحميل التقرير: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تنفيذ التقرير الحالي
  Future<void> executeCurrentReport() async {
    if (currentReport.value == null) {
      error.value = 'لا يوجد تقرير محدد للتنفيذ';
      return;
    }

    try {
      isLoading.value = true;
      error.value = '';

      // تنفيذ التقرير حسب نوعه
      switch (currentReport.value!.type) {
        case ReportType.taskStatus:
        case ReportType.taskSummary:
        case ReportType.taskProgress:
        case ReportType.taskDetails:
        case ReportType.taskCompletion:
          await _executeTasksStatisticsReport();
          break;
        case ReportType.userPerformance:
        case ReportType.userActivity:
          await _executeUserPerformanceReport();
          break;
        case ReportType.departmentPerformance:
        case ReportType.departmentWorkload:
          await _executeUsersStatisticsReport();
          break;
        case ReportType.timeTracking:
        case ReportType.projectProgress:
        case ReportType.projectStatus:
        case ReportType.systemUsage:
        case ReportType.custom:
        default:
          await _executeGenericReport();
      }
      
    } catch (e) {
      error.value = 'خطأ في تنفيذ التقرير: ${e.toString()}';
      debugPrint('خطأ في تنفيذ التقرير: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تنفيذ تقرير إحصائيات المهام
  Future<void> _executeTasksStatisticsReport() async {
    final stats = await _apiService.getTasksStatistics();
    tasksStatistics.value = stats;
    
    // تحويل إلى ReportResult
    currentResult.value = ReportResult.success(
      generatedAt: DateTime.now(),
      summary: {
        'total_tasks': stats['totalTasks'],
        'completed_tasks': stats['completedTasks'],
        'pending_tasks': stats['pendingTasks'],
        'completion_rate': stats['completionRate'],
      },
      data: [
        ...stats['tasksByStatus'].map((item) => {
          'category': 'الحالة',
          'name': item['status'],
          'count': item['count'],
        }),
        ...stats['tasksByPriority'].map((item) => {
          'category': 'الأولوية',
          'name': item['priority'],
          'count': item['count'],
        }),
        ...stats['tasksByDepartment'].map((item) => {
          'category': 'القسم',
          'name': item['department'],
          'count': item['count'],
        }),
      ],
      visualizationData: {
        'tasksByStatus': stats['tasksByStatus'],
        'tasksByPriority': stats['tasksByPriority'],
        'tasksByDepartment': stats['tasksByDepartment'],
      },
    );
  }

  /// تنفيذ تقرير أداء المستخدمين
  Future<void> _executeUserPerformanceReport() async {
    final performance = await _apiService.getUserPerformanceReport();
    userPerformance.value = performance;
    
    currentResult.value = ReportResult.success(
      generatedAt: DateTime.now(),
      summary: {
        'total_users': performance['totalUsers'],
        'active_users': performance['activeUsers'],
        'avg_completion_rate': performance['averageCompletionRate'],
      },
      data: performance['userPerformance'],
      visualizationData: {
        'userPerformance': performance['userPerformance'],
      },
    );
  }

  /// تنفيذ تقرير إحصائيات المستخدمين
  Future<void> _executeUsersStatisticsReport() async {
    final stats = await _apiService.getUsersStatistics();
    usersStatistics.value = stats;
    
    currentResult.value = ReportResult.success(
      generatedAt: DateTime.now(),
      summary: {
        'total_users': stats['totalUsers'],
        'active_users': stats['activeUsers'],
        'online_users': stats['onlineUsers'],
      },
      data: [
        ...stats['usersByDepartment'].map((item) => {
          'category': 'القسم',
          'name': item['department'],
          'count': item['count'],
        }),
        ...stats['usersByRole'].map((item) => {
          'category': 'الدور',
          'name': item['role'],
          'count': item['count'],
        }),
      ],
      visualizationData: {
        'usersByDepartment': stats['usersByDepartment'],
        'usersByRole': stats['usersByRole'],
      },
    );
  }

  /// تنفيذ تقرير عام
  Future<void> _executeGenericReport() async {
    // تنفيذ تقرير عام بناءً على الاستعلام
    currentResult.value = ReportResult.success(
      generatedAt: DateTime.now(),
      summary: {'message': 'تم تنفيذ التقرير بنجاح'},
      data: [
        {'info': 'هذا تقرير عام', 'status': 'تم التنفيذ'}
      ],
    );
  }

  /// إنشاء تقرير جديد
  Future<EnhancedReport?> createReport(EnhancedReport report) async {
    try {
      isLoading.value = true;
      error.value = '';

      final apiReport = report.toReport();
      final createdReport = await _apiService.createReport(apiReport);
      final enhancedReport = EnhancedReport.fromReport(createdReport);
      
      reports.add(enhancedReport);
      _filterReports();
      
      return enhancedReport;
      
    } catch (e) {
      error.value = 'خطأ في إنشاء التقرير: ${e.toString()}';
      debugPrint('خطأ في إنشاء التقرير: $e');
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث تقرير
  Future<EnhancedReport?> updateReport(EnhancedReport report) async {
    try {
      isLoading.value = true;
      error.value = '';

      final apiReport = report.toReport();
      final updatedReport = await _apiService.updateReport(
        int.parse(report.id), apiReport);
      final enhancedReport = EnhancedReport.fromReport(updatedReport);
      
      // تحديث القائمة
      final index = reports.indexWhere((r) => r.id == report.id);
      if (index != -1) {
        reports[index] = enhancedReport;
        _filterReports();
      }
      
      return enhancedReport;
      
    } catch (e) {
      error.value = 'خطأ في تحديث التقرير: ${e.toString()}';
      debugPrint('خطأ في تحديث التقرير: $e');
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(String reportId) async {
    try {
      isLoading.value = true;
      error.value = '';

      await _apiService.deleteReport(int.parse(reportId));
      
      // إزالة من القوائم
      reports.removeWhere((r) => r.id == reportId);
      _filterReports();
      
      return true;
      
    } catch (e) {
      error.value = 'خطأ في حذف التقرير: ${e.toString()}';
      debugPrint('خطأ في حذف التقرير: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// فلترة التقارير حسب النوع
  void _filterReports() {
    // التقارير المشتركة (عامة)
    sharedReports.value = reports.where((r) => r.isShared || r.isPublic).toList();
    
    // التقارير المفضلة
    favoriteReports.value = reports.where((r) => r.isFavorite).toList();
  }

  /// تصدير التقرير الحالي إلى PDF
  Future<void> exportCurrentReportToPdf() async {
    if (currentResult.value == null) {
      error.value = 'لا توجد نتائج للتصدير';
      return;
    }
    
    // محاكاة التصدير
    Get.snackbar(
      'تصدير PDF',
      'تم تصدير التقرير إلى PDF بنجاح',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.withAlpha(179),
      colorText: Colors.white,
    );
  }

  /// تصدير التقرير الحالي إلى Excel
  Future<void> exportCurrentReportToExcel() async {
    if (currentResult.value == null) {
      error.value = 'لا توجد نتائج للتصدير';
      return;
    }
    
    // محاكاة التصدير
    Get.snackbar(
      'تصدير Excel',
      'تم تصدير التقرير إلى Excel بنجاح',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.withAlpha(179),
      colorText: Colors.white,
    );
  }

  /// تصدير التقرير الحالي إلى CSV
  Future<void> exportCurrentReportToCsv() async {
    if (currentResult.value == null) {
      error.value = 'لا توجد نتائج للتصدير';
      return;
    }

    // محاكاة التصدير
    Get.snackbar(
      'تصدير CSV',
      'تم تصدير التقرير إلى CSV بنجاح',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.withAlpha(179),
      colorText: Colors.white,
    );
  }

  /// تصدير التقرير الحالي إلى JSON مع إعدادات مخصصة
  Future<String?> exportCurrentReportToJsonWithSettings({
    bool includeSummary = true,
    bool includeVisualizationData = true,
    bool includeReportInfo = true,
    bool prettyPrint = true,
    bool includeMetadata = true,
  }) async {
    if (currentResult.value == null || currentReport.value == null) {
      error.value = 'لا توجد نتائج للتصدير';
      return null;
    }

    try {
      // إنشاء بنية البيانات للتصدير
      final Map<String, dynamic> exportData = {};

      // تضمين معلومات التقرير
      if (includeReportInfo) {
        exportData['reportInfo'] = {
          'id': currentReport.value!.id,
          'title': currentReport.value!.title,
          'description': currentReport.value!.description,
          'type': currentReport.value!.type.toString(),
          'createdAt': currentReport.value!.createdAt is int
              ? DateTime.fromMillisecondsSinceEpoch(currentReport.value!.createdAt as int).toIso8601String()
              : currentReport.value!.createdAt.toString(),
          'createdBy': currentReport.value!.createdBy,
        };
      }

      // تضمين ملخص التقرير
      if (includeSummary && currentResult.value!.summary != null) {
        exportData['summary'] = currentResult.value!.summary;
      }

      // تضمين بيانات التقرير
      exportData['data'] = currentResult.value!.data;

      // تضمين بيانات التصور المرئي
      if (includeVisualizationData && currentResult.value!.visualizationData != null) {
        exportData['visualizationData'] = currentResult.value!.visualizationData;
      }

      // تضمين البيانات الوصفية
      if (includeMetadata) {
        exportData['metadata'] = {
          'generatedAt': currentResult.value!.generatedAt.toIso8601String(),
          'exportedAt': DateTime.now().toIso8601String(),
          'version': '1.0',
          'format': 'JSON',
          'settings': {
            'includeSummary': includeSummary,
            'includeVisualizationData': includeVisualizationData,
            'includeReportInfo': includeReportInfo,
            'prettyPrint': prettyPrint,
            'includeMetadata': includeMetadata,
          },
        };
      }

      // تحويل إلى JSON
      String jsonString;
      if (prettyPrint) {
        const encoder = JsonEncoder.withIndent('  ');
        jsonString = encoder.convert(exportData);
      } else {
        jsonString = json.encode(exportData);
      }

      // محاكاة حفظ الملف
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'report_${currentReport.value!.id}_$timestamp.json';
      final filePath = '/storage/emulated/0/Download/$fileName';

      // في التطبيق الحقيقي، سيتم حفظ الملف فعلياً
      debugPrint('تم تصدير التقرير إلى JSON: $filePath');
      debugPrint('حجم البيانات: ${jsonString.length} حرف');

      return filePath;
    } catch (e) {
      error.value = 'خطأ في تصدير التقرير إلى JSON: ${e.toString()}';
      debugPrint('خطأ في تصدير التقرير إلى JSON: $e');
      return null;
    }
  }

  /// تصدير تقرير بتنسيق محدد
  Future<String> exportReport({
    required String reportId,
    required ReportFormat format,
  }) async {
    try {
      String? filePath;

      switch (format) {
        case ReportFormat.pdf:
          filePath = await _apiService.exportReportToPdf(int.parse(reportId));
          break;
        case ReportFormat.excel:
          filePath = await _apiService.exportReportToExcel(int.parse(reportId));
          break;
        case ReportFormat.csv:
          filePath = await _apiService.exportReportToCsv(int.parse(reportId));
          break;
        default:
          throw Exception('تنسيق غير مدعوم: $format');
      }

      return filePath ?? '';
    } catch (e) {
      error.value = 'خطأ في تصدير التقرير: ${e.toString()}';
      debugPrint('خطأ في تصدير التقرير: $e');
      rethrow;
    }
  }

  /// فتح ملف مصدر
  void openExportedFile(String filePath) {
    // محاكاة فتح الملف
    Get.snackbar(
      'فتح الملف',
      'تم فتح الملف: $filePath',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.withAlpha(179),
      colorText: Colors.white,
    );
  }

  /// تبديل حالة المفضلة
  Future<void> toggleFavorite(String reportId) async {
    try {
      final report = reports.firstWhere((r) => r.id == reportId);

      if (report.isFavorite) {
        await _apiService.removeFromFavorites(int.parse(reportId));
      } else {
        await _apiService.addToFavorites(int.parse(reportId));
      }

      // تحديث القائمة المحلية
      final index = reports.indexWhere((r) => r.id == reportId);
      if (index != -1) {
        reports[index] = reports[index].copyWith(
          isFavorite: !reports[index].isFavorite,
        );
        _filterReports();
      }

    } catch (e) {
      error.value = 'خطأ في تبديل المفضلة: ${e.toString()}';
      debugPrint('خطأ في تبديل المفضلة: $e');
    }
  }

  /// تحديث فلاتر التقرير
  Future<void> updateReportFilters(String reportId, List<ReportFilter> filters) async {
    try {
      final report = reports.firstWhere((r) => r.id == reportId);
      final updatedReport = report.copyWith(filters: filters);

      await updateReport(updatedReport);

    } catch (e) {
      error.value = 'خطأ في تحديث الفلاتر: ${e.toString()}';
      debugPrint('خطأ في تحديث الفلاتر: $e');
    }
  }

  /// تحديث فترة التقرير
  Future<void> updateReportPeriod(
    String reportId,
    ReportPeriod period,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    try {
      final report = reports.firstWhere((r) => r.id == reportId);
      final updatedReport = report.copyWith(
        period: period,
        customStartDate: startDate,
        customEndDate: endDate,
      );

      await updateReport(updatedReport);

    } catch (e) {
      error.value = 'خطأ في تحديث الفترة: ${e.toString()}';
      debugPrint('خطأ في تحديث الفترة: $e');
    }
  }

  /// تنفيذ تقرير بمعرف محدد
  Future<void> executeReport(
    String reportId, {
    DateTimeRange? dateRange,
  }) async {
    try {
      isLoading.value = true;
      error.value = '';

      // تحميل التقرير أولاً
      await loadReport(reportId);

      // تنفيذ التقرير
      await executeCurrentReport();

    } catch (e) {
      error.value = 'خطأ في تنفيذ التقرير: ${e.toString()}';
      debugPrint('خطأ في تنفيذ التقرير: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// الحصول على تقرير بالمعرف
  Future<EnhancedReport?> getReportById(String reportId) async {
    try {
      final report = await _apiService.getReport(int.parse(reportId));
      return EnhancedReport.fromReport(report);
    } catch (e) {
      error.value = 'خطأ في الحصول على التقرير: ${e.toString()}';
      debugPrint('خطأ في الحصول على التقرير: $e');
      return null;
    }
  }

  /// إنشاء تصور مرئي
  ReportVisualization createVisualization({
    required String title,
    required VisualizationType type,
    required List<String> dataFields,
    String? xAxisField,
    String? yAxisField,
    String? description,
  }) {
    return ReportVisualization(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      type: type,
      xAxisField: xAxisField,
      yAxisField: yAxisField,
      dataFields: dataFields,
    );
  }

  /// الحصول على التقارير المصدرة
  Future<List<ExportedReportInfo>> getExportedReports() async {
    try {
      // محاكاة البيانات - في التطبيق الحقيقي ستأتي من API أو قاعدة البيانات
      final mockExportedReports = <ExportedReportInfo>[
        ExportedReportInfo(
          id: '1',
          title: 'تقرير المهام الشهري',
          filePath: '/storage/emulated/0/Download/tasks_report_202412.pdf',
          format: ReportFormat.pdf,
          fileSize: 1024 * 1024, // 1 MB
          exportedAt: DateTime.now().subtract(const Duration(hours: 2)),
          description: 'تقرير شامل عن حالة المهام خلال شهر ديسمبر',
          reportId: '101',
        ),
        ExportedReportInfo(
          id: '2',
          title: 'إحصائيات الأداء',
          filePath: '/storage/emulated/0/Download/performance_stats.xlsx',
          format: ReportFormat.excel,
          fileSize: 512 * 1024, // 512 KB
          exportedAt: DateTime.now().subtract(const Duration(days: 1)),
          description: 'إحصائيات أداء الموظفين والأقسام',
          reportId: '102',
        ),
        ExportedReportInfo(
          id: '3',
          title: 'بيانات المستخدمين',
          filePath: '/storage/emulated/0/Download/users_data.csv',
          format: ReportFormat.csv,
          fileSize: 256 * 1024, // 256 KB
          exportedAt: DateTime.now().subtract(const Duration(days: 3)),
          description: 'قائمة بجميع المستخدمين وبياناتهم',
          reportId: '103',
        ),
        ExportedReportInfo(
          id: '4',
          title: 'تقرير النشاطات',
          filePath: '/storage/emulated/0/Download/activities_report.json',
          format: ReportFormat.json,
          fileSize: 128 * 1024, // 128 KB
          exportedAt: DateTime.now().subtract(const Duration(days: 5)),
          description: 'سجل النشاطات والعمليات في النظام',
          reportId: '104',
        ),
      ];

      // في التطبيق الحقيقي، يمكن استدعاء API هنا
      // final response = await _apiService.getExportedReports();
      // return response.map((json) => ExportedReportInfo.fromJson(json)).toList();

      return mockExportedReports;
    } catch (e) {
      error.value = 'خطأ في تحميل التقارير المصدرة: ${e.toString()}';
      debugPrint('خطأ في تحميل التقارير المصدرة: $e');
      rethrow;
    }
  }
}
