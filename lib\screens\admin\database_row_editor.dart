import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/database_management_controller.dart';
import 'package:flutter_application_2/models/database_table_model.dart';
import 'package:get/get.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';


/// محرر سجلات قاعدة البيانات
///
/// يوفر واجهة لإنشاء وتعديل سجلات قاعدة البيانات
class DatabaseRowEditor extends StatefulWidget {
  final DatabaseTable table;
  final Map<String, dynamic>? initialValues;
  final Function(Map<String, dynamic>) onSave;

  const DatabaseRowEditor({
    super.key,
    required this.table,
    this.initialValues,
    required this.onSave,
  });

  @override
  State<DatabaseRowEditor> createState() => _DatabaseRowEditorState();
}

class _DatabaseRowEditorState extends State<DatabaseRowEditor> {
  final _formKey = GlobalKey<FormState>();
  final Map<String, dynamic> _formValues = {};
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();

    // تهيئة قيم النموذج
    for (final column in widget.table.columns) {
      // إذا كان هذا إضافة سجل جديد وكان العمود يزيد تلقائياً، تجاهله
      if (widget.initialValues == null && column.isAutoIncrement) {
        continue;
      }

      if (widget.initialValues != null && widget.initialValues!.containsKey(column.name)) {
        _formValues[column.name] = widget.initialValues![column.name];
      } else if (column.defaultValue != null) {
        _formValues[column.name] = column.defaultValue;
      }

      // إنشاء متحكمات النص للحقول النصية
      if (column.type == DatabaseColumnType.text ||
          column.type == DatabaseColumnType.email ||
          column.type == DatabaseColumnType.url ||
          column.type == DatabaseColumnType.phone ||
          column.type == DatabaseColumnType.password ||
          column.type == DatabaseColumnType.markdown ||
          column.type == DatabaseColumnType.richText ||
          column.type == DatabaseColumnType.code) {
        _controllers[column.name] = TextEditingController(
          text: _formValues[column.name]?.toString() ?? '',
        );
      }
    }
  }

  @override
  void dispose() {
    // التخلص من متحكمات النص
    for (final controller in _controllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تحديد الأعمدة القابلة للتعديل
    final editableColumns = widget.table.columns.where((col) {
      // إذا كان هذا تعديل لسجل موجود، لا يمكن تعديل المفتاح الأساسي
      if (widget.initialValues != null && col.isPrimaryKey) {
        debugPrint('تم استبعاد العمود "${col.name}" لأنه مفتاح أساسي في وضع التعديل');
        return false;
      }

      // إذا كان العمود غير قابل للتعديل، لا يمكن تعديله
      if (!col.isEditable) {
        debugPrint('تم استبعاد العمود "${col.name}" لأنه غير قابل للتعديل');
        return false;
      }

      // إذا كان هذا إضافة سجل جديد وكان العمود يزيد تلقائياً، لا نعرضه للمستخدم
      if (widget.initialValues == null && col.isAutoIncrement) {
        debugPrint('تم استبعاد العمود "${col.name}" لأنه يزيد تلقائياً في وضع الإضافة');
        return false;
      }

      // تم تضمين العمود في النموذج
      return true;
    }).toList();

    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عرض الأعمدة القابلة للتعديل
            ...editableColumns.map((column) => _buildFormField(column)),

            // عرض الأعمدة غير القابلة للتعديل
            if (widget.initialValues != null) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Text(
                'معلومات إضافية',
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 16),
              ...widget.table.columns
                  .where((col) => !col.isEditable && col.isVisibleInList)
                  .map((column) => _buildReadOnlyField(column)),
            ],

            const SizedBox(height: 16),

            // زر الحفظ
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveForm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  widget.initialValues != null ? 'حفظ التغييرات' : 'إضافة سجل',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormField(DatabaseColumn column) {
    switch (column.type) {
      case DatabaseColumnType.text:
      case DatabaseColumnType.email:
      case DatabaseColumnType.url:
      case DatabaseColumnType.phone:
        return _buildTextField(column);

      case DatabaseColumnType.password:
        return _buildPasswordField(column);

      case DatabaseColumnType.integer:
      case DatabaseColumnType.real:
        return _buildNumberField(column);

      case DatabaseColumnType.boolean:
        return _buildBooleanField(column);

      case DatabaseColumnType.date:
        return _buildDateField(column);

      case DatabaseColumnType.datetime:
        return _buildDateTimeField(column);

      case DatabaseColumnType.enumType: // تم تغيير الاسم من enum
        return _buildEnumField(column);

      case DatabaseColumnType.foreignKey:
        return _buildForeignKeyField(column);

      case DatabaseColumnType.color:
        return _buildColorField(column);

      case DatabaseColumnType.markdown:
      case DatabaseColumnType.richText:
      case DatabaseColumnType.code:
        return _buildMultilineTextField(column);

      default:
        return _buildTextField(column);
    }
  }

  Widget _buildTextField(DatabaseColumn column) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: _controllers[column.name],
        decoration: InputDecoration(
          labelText: column.effectiveDisplayName,
          hintText: column.description,
          border: const OutlineInputBorder(),
        ),
        validator: column.isRequired
            ? (value) {
                if (value == null || value.isEmpty) {
                  return 'هذا الحقل مطلوب';
                }
                return null;
              }
            : null,
        onChanged: (value) {
          _formValues[column.name] = value;
        },
      ),
    );
  }

  Widget _buildPasswordField(DatabaseColumn column) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: _controllers[column.name],
        decoration: InputDecoration(
          labelText: column.effectiveDisplayName,
          hintText: column.description,
          border: const OutlineInputBorder(),
        ),
        obscureText: true,
        validator: column.isRequired
            ? (value) {
                if (value == null || value.isEmpty) {
                  return 'هذا الحقل مطلوب';
                }
                return null;
              }
            : null,
        onChanged: (value) {
          _formValues[column.name] = value;
        },
      ),
    );
  }

  Widget _buildNumberField(DatabaseColumn column) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        initialValue: _formValues[column.name]?.toString() ?? '',
        decoration: InputDecoration(
          labelText: column.effectiveDisplayName,
          hintText: column.description,
          border: const OutlineInputBorder(),
        ),
        keyboardType: column.type == DatabaseColumnType.integer
            ? TextInputType.number
            : const TextInputType.numberWithOptions(decimal: true),
        validator: column.isRequired
            ? (value) {
                if (value == null || value.isEmpty) {
                  return 'هذا الحقل مطلوب';
                }

                if (column.type == DatabaseColumnType.integer) {
                  if (int.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                } else {
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال رقم';
                  }
                }

                return null;
              }
            : null,
        onChanged: (value) {
          if (column.type == DatabaseColumnType.integer) {
            _formValues[column.name] = int.tryParse(value);
          } else {
            _formValues[column.name] = double.tryParse(value);
          }
        },
      ),
    );
  }

  Widget _buildBooleanField(DatabaseColumn column) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Checkbox(
            value: _formValues[column.name] == 1 || _formValues[column.name] == true,
            onChanged: (value) {
              setState(() {
                _formValues[column.name] = value == true ? 1 : 0;
              });
            },
          ),
          const SizedBox(width: 8),
          Text(column.effectiveDisplayName),
          if (column.description != null) ...[
            const SizedBox(width: 8),
            Tooltip(
              message: column.description!,
              child: const Icon(Icons.info_outline, size: 16),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDateField(DatabaseColumn column) {
    final initialDate = _formValues[column.name] != null
        ? _formValues[column.name] is DateTime
            ? _formValues[column.name]
            : DateTime.fromMillisecondsSinceEpoch(_formValues[column.name])
        : null;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () async {
          final date = await showDatePicker(
            context: context,
            initialDate: initialDate ?? DateTime.now(),
            firstDate: DateTime(1900),
            lastDate: DateTime(2100),
          );

          if (date != null && mounted) {
            setState(() {
              _formValues[column.name] = date.millisecondsSinceEpoch;
            });
          }
        },
        child: InputDecorator(
          decoration: InputDecoration(
            labelText: column.effectiveDisplayName,
            hintText: column.description,
            border: const OutlineInputBorder(),
            suffixIcon: const Icon(Icons.calendar_today),
          ),
          child: Text(
            initialDate != null
                ? '${initialDate.year}-${initialDate.month.toString().padLeft(2, '0')}-${initialDate.day.toString().padLeft(2, '0')}'
                : 'اختر تاريخ',
          ),
        ),
      ),
    );
  }

  Widget _buildDateTimeField(DatabaseColumn column) {
    final initialDateTime = _formValues[column.name] != null
        ? _formValues[column.name] is DateTime
            ? _formValues[column.name]
            : DateTime.fromMillisecondsSinceEpoch(_formValues[column.name])
        : null;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () async {
          final date = await showDatePicker(
            context: context,
            initialDate: initialDateTime ?? DateTime.now(),
            firstDate: DateTime(1900),
            lastDate: DateTime(2100),
          );

          if (date != null && mounted) {
            final time = await showTimePicker(
              context: context,
              initialTime: TimeOfDay.fromDateTime(initialDateTime ?? DateTime.now()),
            );

            if (time != null && mounted) {
              setState(() {
                final dateTime = DateTime(
                  date.year,
                  date.month,
                  date.day,
                  time.hour,
                  time.minute,
                );
                _formValues[column.name] = dateTime.millisecondsSinceEpoch;
              });
            }
          }
        },
        child: InputDecorator(
          decoration: InputDecoration(
            labelText: column.effectiveDisplayName,
            hintText: column.description,
            border: const OutlineInputBorder(),
            suffixIcon: const Icon(Icons.calendar_today),
          ),
          child: Text(
            initialDateTime != null
                ? '${initialDateTime.year}-${initialDateTime.month.toString().padLeft(2, '0')}-${initialDateTime.day.toString().padLeft(2, '0')} ${initialDateTime.hour.toString().padLeft(2, '0')}:${initialDateTime.minute.toString().padLeft(2, '0')}'
                : 'اختر تاريخ ووقت',
          ),
        ),
      ),
    );
  }

  Widget _buildEnumField(DatabaseColumn column) {
    if (column.allowedValues == null || column.allowedValueNames == null) {
      return _buildTextField(column);
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: DropdownButtonFormField<dynamic>(
        value: _formValues[column.name],
        decoration: InputDecoration(
          labelText: column.effectiveDisplayName,
          hintText: column.description,
          border: const OutlineInputBorder(),
        ),
        items: List.generate(column.allowedValues!.length, (index) {
          return DropdownMenuItem<dynamic>(
            value: column.allowedValues![index],
            child: Text(column.allowedValueNames![index]),
          );
        }),
        validator: column.isRequired
            ? (value) {
                if (value == null) {
                  return 'هذا الحقل مطلوب';
                }
                return null;
              }
            : null,
        onChanged: (value) {
          setState(() {
            _formValues[column.name] = value;
          });
        },
      ),
    );
  }

  Widget _buildForeignKeyField(DatabaseColumn column) {
    if (column.foreignKeyTable == null ||
        column.foreignKeyColumn == null ||
        column.foreignKeyDisplayColumn == null) {
      return _buildTextField(column);
    }

    final controller = Get.find<DatabaseManagementController>();

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: FutureBuilder<List<Map<String, dynamic>>>(
        future: controller.getForeignKeyOptions(
          column.foreignKeyTable!,
          column.foreignKeyColumn!,
          column.foreignKeyDisplayColumn!,
        ),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return InputDecorator(
              decoration: InputDecoration(
                labelText: column.effectiveDisplayName,
                hintText: 'جاري تحميل البيانات...',
                border: const OutlineInputBorder(),
              ),
              child: const Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            );
          }

          if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
            return TextFormField(
              initialValue: _formValues[column.name]?.toString() ?? '',
              decoration: InputDecoration(
                labelText: column.effectiveDisplayName,
                hintText: snapshot.hasError
                    ? 'خطأ في تحميل البيانات'
                    : 'لا توجد بيانات',
                border: const OutlineInputBorder(),
                errorText: snapshot.hasError ? 'خطأ في تحميل البيانات' : null,
              ),
              validator: column.isRequired
                  ? (value) {
                      if (value == null || value.isEmpty) {
                        return 'هذا الحقل مطلوب';
                      }
                      return null;
                    }
                  : null,
              onChanged: (value) {
                _formValues[column.name] = value;
              },
            );
          }

          // تحويل البيانات إلى قائمة منسدلة
          final options = snapshot.data!;

          // البحث عن القيمة الحالية في القائمة
          dynamic currentValue = _formValues[column.name];

          // التحقق من وجود القيمة الحالية في القائمة
          bool valueExists = false;
          if (currentValue != null) {
            valueExists = options.any((option) =>
                option[column.foreignKeyColumn!]?.toString() == currentValue.toString());
          }

          return DropdownButtonFormField<String>(
            value: valueExists ? currentValue?.toString() : null,
            decoration: InputDecoration(
              labelText: column.effectiveDisplayName,
              hintText: column.description,
              border: const OutlineInputBorder(),
            ),
            items: options.map((option) {
              final value = option[column.foreignKeyColumn!]?.toString() ?? '';
              final display = option[column.foreignKeyDisplayColumn!]?.toString() ?? '';
              return DropdownMenuItem<String>(
                value: value,
                child: Text(display),
              );
            }).toList(),
            validator: column.isRequired
                ? (value) {
                    if (value == null || value.isEmpty) {
                      return 'هذا الحقل مطلوب';
                    }
                    return null;
                  }
                : null,
            onChanged: (value) {
              setState(() {
                _formValues[column.name] = value;
              });
            },
          );
        },
      ),
    );
  }

  Widget _buildColorField(DatabaseColumn column) {
    final initialColor = _formValues[column.name]?.toString() ?? '';
    final currentColor = _parseColor(initialColor);

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              initialValue: initialColor,
              decoration: InputDecoration(
                labelText: column.effectiveDisplayName,
                hintText: column.description,
                border: const OutlineInputBorder(),
                prefixIcon: Container(
                  margin: const EdgeInsets.all(8),
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: currentColor,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey),
                  ),
                ),
              ),
              validator: column.isRequired
                  ? (value) {
                      if (value == null || value.isEmpty) {
                        return 'هذا الحقل مطلوب';
                      }
                      return null;
                    }
                  : null,
              onChanged: (value) {
                setState(() {
                  _formValues[column.name] = value;
                });
              },
            ),
          ),
          IconButton(
            icon: const Icon(Icons.color_lens),
            onPressed: () {
              // عرض منتقي الألوان
              _showColorPicker(context, currentColor, (Color color) {
                // Convert color to hex format
                final String hexColor = _colorToHex(color);
                setState(() {
                  _formValues[column.name] = hexColor;
                });
              });
            },
          ),
        ],
      ),
    );
  }

  // عرض مربع حوار منتقي الألوان
  void _showColorPicker(BuildContext context, Color currentColor, Function(Color) onColorChanged) {
    Color pickerColor = currentColor;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('اختر لونًا'),
          content: SingleChildScrollView(
            child: ColorPicker(
              pickerColor: pickerColor,
              onColorChanged: (Color color) {
                pickerColor = color;
              },
              portraitOnly: true,
              enableAlpha: true,
              hexInputBar: true,
              displayThumbColor: true,
              colorPickerWidth: 300,
              pickerAreaHeightPercent: 0.7,
              labelTypes: const [],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('إلغاء'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('تأكيد'),
              onPressed: () {
                onColorChanged(pickerColor);
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildMultilineTextField(DatabaseColumn column) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: _controllers[column.name],
        decoration: InputDecoration(
          labelText: column.effectiveDisplayName,
          hintText: column.description,
          border: const OutlineInputBorder(),
        ),
        maxLines: 5,
        validator: column.isRequired
            ? (value) {
                if (value == null || value.isEmpty) {
                  return 'هذا الحقل مطلوب';
                }
                return null;
              }
            : null,
        onChanged: (value) {
          _formValues[column.name] = value;
        },
      ),
    );
  }

  Widget _buildReadOnlyField(DatabaseColumn column) {
    final value = widget.initialValues?[column.name];

    if (value == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            column.effectiveDisplayName,
            style: AppStyles.labelMedium.copyWith(
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 4),
          column.type == DatabaseColumnType.foreignKey &&
                column.foreignKeyTable != null &&
                column.foreignKeyColumn != null &&
                column.foreignKeyDisplayColumn != null
              ? _buildReadOnlyForeignKeyValue(column, value)
              : Text(
                  _formatValue(column, value),
                  style: AppStyles.bodyMedium,
                ),
        ],
      ),
    );
  }

  Widget _buildReadOnlyForeignKeyValue(DatabaseColumn column, dynamic value) {
    final controller = Get.find<DatabaseManagementController>();

    return FutureBuilder<String>(
      future: controller.getForeignKeyDisplayValue(
        column.foreignKeyTable!,
        column.foreignKeyColumn!,
        column.foreignKeyDisplayColumn!,
        value,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          );
        }

        return Text(
          snapshot.data ?? value.toString(),
          style: AppStyles.bodyMedium,
        );
      },
    );
  }

  String _formatValue(DatabaseColumn column, dynamic value) {
    if (value == null) {
      return '-';
    }

    switch (column.type) {
      case DatabaseColumnType.boolean:
        return value == 1 || value == true ? 'نعم' : 'لا';

      case DatabaseColumnType.date:
      case DatabaseColumnType.datetime:
        if (value is int) {
          final date = DateTime.fromMillisecondsSinceEpoch(value);
          return column.type == DatabaseColumnType.date
              ? '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}'
              : '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
        }
        return value.toString();

      case DatabaseColumnType.enumType: // تم تغيير الاسم من enum
        if (column.allowedValues != null && column.allowedValueNames != null) {
          final index = column.allowedValues!.indexOf(value);
          if (index >= 0 && index < column.allowedValueNames!.length) {
            return column.allowedValueNames![index];
          }
        }
        return value.toString();

      case DatabaseColumnType.password:
        return '••••••••';

      default:
        return value.toString();
    }
  }

  void _saveForm() {
    if (_formKey.currentState!.validate()) {
      final finalValues = <String, dynamic>{};

      // إضافة القيم المحررة
      for (final entry in _formValues.entries) {
        if (entry.value != null) {
          // البحث عن العمود المقابل
          final column = widget.table.columns.firstWhere(
            (col) => col.name == entry.key,
            orElse: () => DatabaseColumn(name: entry.key, type: DatabaseColumnType.text),
          );

          // إذا كان هذا إضافة سجل جديد وكان العمود يزيد تلقائياً، لا نرسله
          if (widget.initialValues == null && column.isAutoIncrement) {
            continue;
          }

          finalValues[entry.key] = entry.value;
        }
      }

      // إضافة القيم غير القابلة للتعديل من البيانات الأصلية
      if (widget.initialValues != null) {
        for (final column in widget.table.columns) {
          if (!column.isEditable && widget.initialValues!.containsKey(column.name)) {
            finalValues[column.name] = widget.initialValues![column.name];
          }
        }
      }

      // تنظيف البيانات قبل الإرسال
      final cleanedValues = _cleanFormData(finalValues);

      widget.onSave(cleanedValues);
    }
  }

  /// تنظيف بيانات النموذج قبل الإرسال
  Map<String, dynamic> _cleanFormData(Map<String, dynamic> data) {
    final cleaned = <String, dynamic>{};

    for (final entry in data.entries) {
      final column = widget.table.columns.firstWhere(
        (col) => col.name == entry.key,
        orElse: () => DatabaseColumn(name: entry.key, type: DatabaseColumnType.text),
      );

      dynamic value = entry.value;

      // تحويل القيم حسب نوع العمود
      switch (column.type) {
        case DatabaseColumnType.integer:
          if (value is String && value.isNotEmpty) {
            value = int.tryParse(value) ?? 0;
          }
          break;
        case DatabaseColumnType.decimal:
        case DatabaseColumnType.real:
          if (value is String && value.isNotEmpty) {
            value = double.tryParse(value) ?? 0.0;
          }
          break;
        case DatabaseColumnType.boolean:
          if (value is String) {
            value = value.toLowerCase() == 'true' || value == '1';
          }
          break;
        case DatabaseColumnType.datetime:
        case DatabaseColumnType.date:
          if (value is DateTime) {
            value = value.millisecondsSinceEpoch;
          }
          break;
        default:
          // للنصوص والأنواع الأخرى، احتفظ بالقيمة كما هي
          break;
      }

      cleaned[entry.key] = value;
    }

    return cleaned;
  }

  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        String hexColor = colorString.replaceAll('#', '');
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
        return Color(int.parse('0x$hexColor'));
      }
      return Colors.blue;
    } catch (e) {
      return Colors.grey;
    }
  }

  /// تحويل اللون إلى صيغة هيكس
  String _colorToHex(Color color) {
    try {
      // استخدام طريقة آمنة لتحويل اللون إلى صيغة هيكس
      final String hexString = color.toARGB32().toRadixString(16).padLeft(8, '0');
      // استخراج الجزء الخاص بالألوان RGB فقط (بدون قناة الشفافية)
      final String rgbHex = hexString.substring(2);
      return '#$rgbHex';
    } catch (e) {
      return '#000000';
    }
  }
}
