@echo off
echo =================================
echo    Archive Models Migration
echo =================================
echo.

echo Running archive migration script...
sqlcmd -S ".\sqlexpress" -d "databasetasks" -i "webApi\webApi\SQL_Scripts\UpdateArchiveModels.sql" -E

if %ERRORLEVEL% EQU 0 (
    echo.
    echo SUCCESS: Archive migration completed!
    echo.
    echo Changes applied:
    echo - Added color column to archive_categories
    echo - Added icon column to archive_categories  
    echo - Added is_active column to archive_categories
    echo - Added content column to archive_documents
    echo - Added created_by column to archive_documents
    echo - Added created_at column to archive_documents
    echo - Added description column to archive_tags
    echo - Added is_active column to archive_tags
    echo - Created archive_document_tags table
    echo.
    echo Your API should now work without column errors!
) else (
    echo.
    echo ERROR: Migration failed!
    echo.
    echo Manual alternative:
    echo 1. Open SQL Server Management Studio
    echo 2. Connect to .\sqlexpress
    echo 3. Select database: databasetasks
    echo 4. Open file: webApi\webApi\SQL_Scripts\UpdateArchiveModels.sql
    echo 5. Execute the script
)

echo.
pause
