import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

/// مكون عرض ملفات PDF
///
/// يوفر واجهة متكاملة لعرض ملفات PDF مع دعم:
/// - التكبير والتصغير والتنقل بين الصفحات
/// - البحث في محتوى PDF
/// - تنزيل الملف
class PdfViewerWidget extends StatefulWidget {
  /// مسار ملف PDF
  final String filePath;

  /// عنوان الملف (اختياري)
  final String? title;

  /// إظهار شريط الأدوات
  final bool showToolbar;

  /// إنشاء مكون عرض ملفات PDF
  const PdfViewerWidget({
    super.key,
    required this.filePath,
    this.title,
    this.showToolbar = true,
  });

  @override
  State<PdfViewerWidget> createState() => _PdfViewerWidgetState();
}

class _PdfViewerWidgetState extends State<PdfViewerWidget> {
  /// مرجع لعارض PDF
  final PdfViewerController _pdfViewerController = PdfViewerController();

  /// مرجع للبحث في PDF
  final PdfTextSearchResult _searchResult = PdfTextSearchResult();

  /// حالة تحميل الملف
  bool _isLoading = true;

  /// رسالة الخطأ (إن وجدت)
  String? _errorMessage;

  /// حالة البحث
  bool _isSearching = false;

  /// نص البحث
  final TextEditingController _searchController = TextEditingController();

  /// حالة تنزيل الملف
  bool _isDownloading = false;

  /// نسبة تقدم التنزيل
  double _downloadProgress = 0.0;

  /// حالة عرض المصغرات
  bool _showThumbnails = false;

  /// وضع العرض (عمودي/أفقي)
  bool _isHorizontalScrollDirection = false;

  /// حالة عرض الإشارات المرجعية
  bool _showBookmarks = false;

  /// عدد صفحات المستند
  int _pageCount = 0;

  @override
  void initState() {
    super.initState();
    // التحقق من وجود الملف
    _checkFileExists();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _pdfViewerController.clearSelection();
    super.dispose();
  }

  /// التحقق من وجود الملف
  Future<void> _checkFileExists() async {
    try {
      debugPrint('التحقق من وجود ملف PDF: ${widget.filePath}');

      final file = File(widget.filePath);
      final exists = await file.exists();

      if (!exists) {
        debugPrint('ملف PDF غير موجود: ${widget.filePath}');
        setState(() {
          _isLoading = false;
          _errorMessage = 'الملف غير موجود'.tr;
        });
      } else {
        // التحقق من حجم الملف
        final fileSize = await file.length();
        debugPrint('حجم ملف PDF: $fileSize بايت');

        if (fileSize <= 0) {
          debugPrint('ملف PDF فارغ: ${widget.filePath}');
          setState(() {
            _isLoading = false;
            _errorMessage = 'ملف PDF فارغ'.tr;
          });
        } else {
          // التحقق من امتداد الملف
          if (!widget.filePath.toLowerCase().endsWith('.pdf')) {
            debugPrint('امتداد الملف غير صحيح: ${widget.filePath}');
            setState(() {
              _isLoading = false;
              _errorMessage = 'امتداد الملف غير صحيح، يجب أن يكون PDF'.tr;
            });
          } else {
            debugPrint('ملف PDF موجود وصالح: ${widget.filePath}');
            setState(() {
              _isLoading = false;
            });
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ أثناء التحقق من ملف PDF: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء التحقق من الملف: ${e.toString()}'.tr;
      });
    }
  }

  /// تنزيل الملف
  Future<void> _downloadFile() async {
    try {
      setState(() {
        _isDownloading = true;
        _downloadProgress = 0.0;
      });

      // استخدام حزمة background_downloader للتنزيل في الخلفية
      final file = File(widget.filePath);
      if (!await file.exists()) {
        _showErrorSnackbar('الملف غير موجود'.tr);
        setState(() {
          _isDownloading = false;
        });
        return;
      }

      // تحديد مجلد التنزيلات
      final downloadsDir = await getDownloadsDirectory() ??
                          await getApplicationDocumentsDirectory();
      final fileName = path.basename(widget.filePath);

      // نسخ الملف مباشرة
      try {
        // قراءة محتوى الملف
        final bytes = await file.readAsBytes();

        // إنشاء ملف جديد في مجلد التنزيلات
        final targetFile = File(path.join(downloadsDir.path, fileName));
        await targetFile.writeAsBytes(bytes);

        // تحديث التقدم وإظهار رسالة نجاح
        setState(() {
          _isDownloading = false;
          _downloadProgress = 1.0;
        });

        _showSuccessSnackbar('تم تنزيل الملف بنجاح إلى: ${targetFile.path}'.tr);
      } catch (e) {
        setState(() {
          _isDownloading = false;
        });
        _showErrorSnackbar('فشل نسخ الملف: ${e.toString()}'.tr);
      }
    } catch (e) {
      setState(() {
        _isDownloading = false;
      });
      _showErrorSnackbar('حدث خطأ أثناء تنزيل الملف: ${e.toString()}'.tr);
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackbar(String message) {
    Get.snackbar(
      'نجاح'.tr,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      duration: const Duration(seconds: 3),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackbar(String message) {
    Get.snackbar(
      'خطأ'.tr,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      duration: const Duration(seconds: 3),
    );
  }

  /// البحث في محتوى PDF
  void _searchPDF(String text) {
    _searchResult.clear();
    if (text.isEmpty) {
      setState(() {
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final result = _pdfViewerController.searchText(text);
      result.addListener(() {
        if (mounted) {
          setState(() {});
        }
      });
    });
  }

  /// الانتقال إلى النتيجة التالية
  void _findNextOccurrence() {
    _pdfViewerController.searchText(_searchController.text).nextInstance();
  }

  /// الانتقال إلى النتيجة السابقة
  void _findPreviousOccurrence() {
    _pdfViewerController.searchText(_searchController.text).previousInstance();
  }

  /// إغلاق البحث
  void _closeSearch() {
    setState(() {
      _isSearching = false;
      _searchController.clear();
    });
    _pdfViewerController.clearSelection();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'بحث في المستند'.tr,
                  border: InputBorder.none,
                  hintStyle: const TextStyle(color: Colors.white70),
                ),
                style: const TextStyle(color: Colors.white),
                autofocus: true,
                onChanged: _searchPDF,
              )
            : Text(widget.title ?? 'عرض PDF'.tr),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_isSearching) ...[
            // Search result count will be shown here
            IconButton(
              icon: const Icon(Icons.navigate_before),
              onPressed: _findPreviousOccurrence,
              tooltip: 'السابق'.tr,
            ),
            IconButton(
              icon: const Icon(Icons.navigate_next),
              onPressed: _findNextOccurrence,
              tooltip: 'التالي'.tr,
            ),
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: _closeSearch,
              tooltip: 'إغلاق البحث'.tr,
            ),
          ] else ...[
            // زر البحث
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                setState(() {
                  _isSearching = true;
                });
              },
              tooltip: 'بحث'.tr,
            ),

            // زر المصغرات
            IconButton(
              icon: Icon(
                Icons.grid_view,
                color: _showThumbnails ? Colors.amber : Colors.white,
              ),
              onPressed: () {
                setState(() {
                  _showThumbnails = !_showThumbnails;
                  // إغلاق الإشارات المرجعية عند فتح المصغرات
                  if (_showThumbnails) {
                    _showBookmarks = false;
                  }
                });
              },
              tooltip: 'عرض المصغرات'.tr,
            ),

            // زر الإشارات المرجعية
            IconButton(
              icon: Icon(
                Icons.bookmark,
                color: _showBookmarks ? Colors.amber : Colors.white,
              ),
              onPressed: () {
                setState(() {
                  _showBookmarks = !_showBookmarks;
                  // إغلاق المصغرات عند فتح الإشارات المرجعية
                  if (_showBookmarks) {
                    _showThumbnails = false;
                  }
                });
              },
              tooltip: 'الإشارات المرجعية'.tr,
            ),

            // زر تغيير اتجاه العرض
            IconButton(
              icon: Icon(
                _isHorizontalScrollDirection ? Icons.swap_horiz : Icons.swap_vert,
              ),
              onPressed: () {
                setState(() {
                  _isHorizontalScrollDirection = !_isHorizontalScrollDirection;
                });
              },
              tooltip: 'تغيير اتجاه العرض'.tr,
            ),

            // زر التنزيل
            IconButton(
              icon: _isDownloading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Icon(Icons.download),
              onPressed: _isDownloading ? null : _downloadFile,
              tooltip: 'تنزيل'.tr,
            ),
          ],
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'جاري تحميل المستند...',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: AppStyles.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _errorMessage = null;
                });
                _checkFileExists();
              },
              icon: const Icon(Icons.refresh),
              label: Text('إعادة المحاولة'.tr),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ],
        ),
      );
    }

    // تم التحقق من حالة التحميل والخطأ في الأعلى

    return SfPdfViewer.file(
      File(widget.filePath),
      controller: _pdfViewerController,
      enableDocumentLinkAnnotation: true,
      enableTextSelection: true,
      enableDoubleTapZooming: true,
      canShowScrollHead: true,
      canShowScrollStatus: true,
      canShowPaginationDialog: true,
      pageSpacing: 4,
      onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
        setState(() {
          _errorMessage = 'فشل تحميل ملف PDF: ${details.error}'.tr;
        });
      },
      onDocumentLoaded: (PdfDocumentLoadedDetails details) {
        setState(() {
          _pageCount = details.document.pages.count;
        });
      },
    );
  }

  /// بناء شريط التنقل السفلي
  Widget? _buildBottomNavigationBar() {
    if (_isLoading || _errorMessage != null) {
      return null;
    }

    return BottomAppBar(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // زر التكبير
          IconButton(
            icon: const Icon(Icons.zoom_in),
            tooltip: 'تكبير'.tr,
            onPressed: () {
              _pdfViewerController.zoomLevel = (_pdfViewerController.zoomLevel + 0.25).clamp(0.25, 3.0);
            },
          ),

          // زر التصغير
          IconButton(
            icon: const Icon(Icons.zoom_out),
            tooltip: 'تصغير'.tr,
            onPressed: () {
              _pdfViewerController.zoomLevel = (_pdfViewerController.zoomLevel - 0.25).clamp(0.25, 3.0);
            },
          ),

          // زر الانتقال إلى صفحة محددة
          IconButton(
            icon: const Icon(Icons.find_in_page),
            tooltip: 'انتقال إلى صفحة'.tr,
            onPressed: _showPageNavigationDialog,
          ),

          // زر تبديل وضع العرض
          IconButton(
            icon: Icon(_isHorizontalScrollDirection ? Icons.swap_vert : Icons.swap_horiz),
            tooltip: 'تبديل وضع العرض'.tr,
            onPressed: () {
              setState(() {
                _isHorizontalScrollDirection = !_isHorizontalScrollDirection;
              });
            },
          ),

          // زر التنزيل
          IconButton(
            icon: const Icon(Icons.download),
            tooltip: 'تنزيل'.tr,
            onPressed: _downloadFile,
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار للانتقال إلى صفحة محددة
  void _showPageNavigationDialog() {
    if (_pageCount <= 0) return;

    final TextEditingController pageController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: Text('انتقال إلى صفحة'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('أدخل رقم الصفحة (1-$_pageCount)'.tr),
            const SizedBox(height: 8),
            TextField(
              controller: pageController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: 'رقم الصفحة'.tr,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'.tr),
          ),
          TextButton(
            onPressed: () {
              final pageNumber = int.tryParse(pageController.text);
              if (pageNumber != null && pageNumber > 0 && pageNumber <= _pageCount) {
                _pdfViewerController.jumpToPage(pageNumber);
                Get.back();
              } else {
                Get.snackbar(
                  'خطأ'.tr,
                  'رقم الصفحة غير صالح'.tr,
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
              }
            },
            child: Text('انتقال'.tr),
          ),
        ],
      ),
    );
  }
}
