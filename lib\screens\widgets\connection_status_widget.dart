import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../helpers/database_helper.dart';

/// مكون لعرض حالة الاتصال بالإنترنت وقاعدة البيانات
///
/// يعرض هذا المكون حالة الاتصال الحالية ويوفر خيارات للتحكم في إعدادات الاتصال
class ConnectionStatusWidget extends StatelessWidget {
  /// حجم المكون
  final ConnectionStatusSize size;

  /// ما إذا كان يجب عرض التفاصيل
  final bool showDetails;

  /// ما إذا كان يجب عرض الإعدادات
  final bool showSettings;

  /// دالة يتم استدعاؤها عند النقر على المكون
  final VoidCallback? onTap;

  const ConnectionStatusWidget({
    super.key,
    this.size = ConnectionStatusSize.normal,
    this.showDetails = true,
    this.showSettings = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // الحصول على DatabaseHelper للتحقق من حالة الاتصال الفعلية
    final databaseHelper = Get.find<DatabaseHelper>();

    return Obx(() {
      final isConnected = databaseHelper.isConnected;
      final connectionType = 'api'; // نوع الاتصال API

      return _buildStatusWidget(context, isConnected, connectionType);
    });
  }

  /// بناء مكون حالة الاتصال
  Widget _buildStatusWidget(BuildContext context, bool isConnected, String connectionType) {
    // تحديد الألوان والأيقونات بناءً على حالة الاتصال
    final Color backgroundColor = isConnected
        ? Colors.green.withValues(alpha: 204) // 0.8 * 255 = 204
        : Colors.red.withValues(alpha: 204); // 0.8 * 255 = 204

    final Color textColor = Colors.white;

    final IconData iconData = isConnected
        ? _getConnectionTypeIcon(connectionType)
        : Icons.signal_wifi_off;

    final String statusText = isConnected
        ? 'متصل (${_getConnectionTypeText(connectionType)})'
        : 'غير متصل';

    // بناء المكون حسب الحجم المطلوب
    switch (size) {
      case ConnectionStatusSize.small:
        return _buildSmallIndicator(backgroundColor, iconData);
      case ConnectionStatusSize.normal:
        return _buildNormalIndicator(context, backgroundColor, textColor, iconData, statusText);
      case ConnectionStatusSize.large:
        return _buildLargeIndicator(context, backgroundColor, textColor, iconData, statusText);
    }
  }

  /// بناء مؤشر صغير (أيقونة فقط)
  Widget _buildSmallIndicator(Color backgroundColor, IconData iconData) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: Icon(
          iconData,
          color: Colors.white,
          size: 16,
        ),
      ),
    );
  }

  /// بناء مؤشر عادي (أيقونة ونص)
  Widget _buildNormalIndicator(
    BuildContext context,
    Color backgroundColor,
    Color textColor,
    IconData iconData,
    String statusText,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              iconData,
              color: textColor,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              statusText,
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر كبير (أيقونة ونص وتفاصيل)
  Widget _buildLargeIndicator(
    BuildContext context,
    Color backgroundColor,
    Color textColor,
    IconData iconData,
    String statusText,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  iconData,
                  color: textColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  statusText,
                  style: TextStyle(
                    color: textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            if (showDetails) ...[
              const SizedBox(height: 8),
              _buildConnectionDetails(textColor),
            ],
            if (showSettings) ...[
              const SizedBox(height: 8),
              _buildSettingsSection(context),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء قسم تفاصيل الاتصال
  Widget _buildConnectionDetails(Color textColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'آخر تغيير: ${_formatDateTime(DateTime.now())}',
          style: TextStyle(color: textColor, fontSize: 12),
        ),
        Text(
          'عدد مرات الانقطاع: 0',
          style: TextStyle(color: textColor, fontSize: 12),
        ),
        Text(
          'إجمالي وقت عدم الاتصال: ${_formatDuration(0)}',
          style: TextStyle(color: textColor, fontSize: 12),
        ),
      ],
    );
  }

  /// بناء قسم الإعدادات
  Widget _buildSettingsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إعادة الاتصال التلقائي',
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
            Switch(
              value: true,
              onChanged: (value) {
                // TODO: تنفيذ حفظ الإعدادات
              },
              activeColor: Colors.white,
            ),
          ],
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'عرض الإشعارات',
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
            Switch(
              value: true,
              onChanged: (value) {
                // TODO: تنفيذ حفظ الإعدادات
              },
              activeColor: Colors.white,
            ),
          ],
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'فترة إعادة الاتصال: 30 ثانية',
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
            IconButton(
              icon: Icon(Icons.refresh, color: Colors.white, size: 16),
              onPressed: () {
                // TODO: تنفيذ إعادة تعيين الإحصائيات
              },
            ),
          ],
        ),
      ],
    );
  }

  /// الحصول على أيقونة نوع الاتصال
  IconData _getConnectionTypeIcon(String connectionType) {
    switch (connectionType) {
      case 'wifi':
        return Icons.wifi;
      case 'mobile':
        return Icons.signal_cellular_4_bar;
      case 'ethernet':
        return Icons.lan;
      case 'bluetooth':
        return Icons.bluetooth;
      case 'vpn':
        return Icons.vpn_key;
      case 'api':
        return Icons.cloud;
      default:
        return Icons.signal_wifi_4_bar;
    }
  }

  /// الحصول على نص نوع الاتصال
  String _getConnectionTypeText(String connectionType) {
    switch (connectionType) {
      case 'wifi':
        return 'واي فاي';
      case 'mobile':
        return 'بيانات الجوال';
      case 'ethernet':
        return 'إيثرنت';
      case 'bluetooth':
        return 'بلوتوث';
      case 'vpn':
        return 'VPN';
      case 'api':
        return 'API';
      default:
        return connectionType;
    }
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// تنسيق المدة الزمنية
  String _formatDuration(int seconds) {
    if (seconds < 60) {
      return '$seconds ثانية';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return '$minutes دقيقة ${remainingSeconds > 0 ? '$remainingSeconds ثانية' : ''}';
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return '$hours ساعة ${minutes > 0 ? '$minutes دقيقة' : ''}';
    }
  }
}

/// أحجام مكون حالة الاتصال
enum ConnectionStatusSize {
  /// صغير (أيقونة فقط)
  small,

  /// عادي (أيقونة ونص)
  normal,

  /// كبير (أيقونة ونص وتفاصيل)
  large,
}
