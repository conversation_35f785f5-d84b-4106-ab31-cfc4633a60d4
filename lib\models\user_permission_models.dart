import 'user_model.dart';
import 'permission_models.dart';

/// نموذج صلاحيات المستخدم - متطابق مع ASP.NET Core API
class UserPermission {
  final int id;
  final int userId;
  final int permissionId;
  final int grantedBy;
  final int grantedAt;
  final bool isActive;
  final int? expiresAt;
  final bool isDeleted;

  // Navigation properties
  final User? user;
  final Permission? permission;
  final User? grantedByNavigation;

  const UserPermission({
    required this.id,
    required this.userId,
    required this.permissionId,
    required this.grantedBy,
    required this.grantedAt,
    this.isActive = true,
    this.expiresAt,
    this.isDeleted = false,
    this.user,
    this.permission,
    this.grantedByNavigation,
  });

  /// إنشاء UserPermission من JSON (من API)
  factory UserPermission.fromJson(Map<String, dynamic> json) {
    return UserPermission(
      id: json['id'] as int,
      userId: json['userId'] as int,
      permissionId: json['permissionId'] as int,
      grantedBy: json['grantedBy'] as int,
      grantedAt: json['grantedAt'] as int,
      isActive: json['isActive'] as bool? ?? true,
      expiresAt: json['expiresAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      permission: json['permission'] != null
          ? Permission.fromJson(json['permission'] as Map<String, dynamic>)
          : null,
      grantedByNavigation: json['grantedByNavigation'] != null
          ? User.fromJson(json['grantedByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  /// تحويل UserPermission إلى JSON (للإرسال إلى API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'permissionId': permissionId,
      'grantedBy': grantedBy,
      'grantedAt': grantedAt,
      'isActive': isActive,
      'expiresAt': expiresAt,
      'isDeleted': isDeleted,
    };
  }

  /// إنشاء نسخة معدلة من UserPermission
  UserPermission copyWith({
    int? id,
    int? userId,
    int? permissionId,
    int? grantedBy,
    int? grantedAt,
    bool? isActive,
    int? expiresAt,
    bool? isDeleted,
    User? user,
    Permission? permission,
    User? grantedByNavigation,
  }) {
    return UserPermission(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      permissionId: permissionId ?? this.permissionId,
      grantedBy: grantedBy ?? this.grantedBy,
      grantedAt: grantedAt ?? this.grantedAt,
      isActive: isActive ?? this.isActive,
      expiresAt: expiresAt ?? this.expiresAt,
      isDeleted: isDeleted ?? this.isDeleted,
      user: user ?? this.user,
      permission: permission ?? this.permission,
      grantedByNavigation: grantedByNavigation ?? this.grantedByNavigation,
    );
  }

  /// الحصول على تاريخ المنح كـ DateTime
  DateTime get grantedAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(grantedAt * 1000);

  /// الحصول على تاريخ انتهاء الصلاحية كـ DateTime
  DateTime? get expiresAtDateTime => expiresAt != null
      ? DateTime.fromMillisecondsSinceEpoch(expiresAt! * 1000)
      : null;

  /// التحقق من انتهاء صلاحية الإذن
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().millisecondsSinceEpoch > (expiresAt! * 1000);
  }

  /// التحقق من صحة الإذن (نشط وغير منتهي الصلاحية وغير محذوف)
  bool get isValid => isActive && !isExpired && !isDeleted;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserPermission && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserPermission(id: $id, userId: $userId, permissionId: $permissionId, isActive: $isActive)';
  }
}

/// نموذج طلب منح صلاحية
class GrantPermissionRequest {
  final int userId;
  final int permissionId;
  final String? notes;

  const GrantPermissionRequest({
    required this.userId,
    required this.permissionId,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'permissionId': permissionId,
      'notes': notes,
    };
  }
}

/// نموذج طلب إلغاء صلاحية
class RevokePermissionRequest {
  final int userId;
  final int permissionId;
  final String? notes;

  const RevokePermissionRequest({
    required this.userId,
    required this.permissionId,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'permissionId': permissionId,
      'notes': notes,
    };
  }
}

/// نموذج طلب تحديث صلاحية مستخدم
class UpdateUserPermissionRequest {
  final bool? isGranted;
  final String? notes;

  const UpdateUserPermissionRequest({
    this.isGranted,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (isGranted != null) data['isGranted'] = isGranted;
    if (notes != null) data['notes'] = notes;
    return data;
  }
}

/// نموذج مجموعة صلاحيات المستخدم
class UserPermissionGroup {
  final int userId;
  final String userName;
  final List<UserPermission> permissions;
  final List<Permission> grantedPermissions;
  final List<Permission> availablePermissions;

  const UserPermissionGroup({
    required this.userId,
    required this.userName,
    required this.permissions,
    required this.grantedPermissions,
    required this.availablePermissions,
  });

  factory UserPermissionGroup.fromJson(Map<String, dynamic> json) {
    return UserPermissionGroup(
      userId: json['userId'] as int,
      userName: json['userName'] as String,
      permissions: (json['permissions'] as List<dynamic>)
          .map((p) => UserPermission.fromJson(p as Map<String, dynamic>))
          .toList(),
      grantedPermissions: (json['grantedPermissions'] as List<dynamic>)
          .map((p) => Permission.fromJson(p as Map<String, dynamic>))
          .toList(),
      availablePermissions: (json['availablePermissions'] as List<dynamic>)
          .map((p) => Permission.fromJson(p as Map<String, dynamic>))
          .toList(),
    );
  }

  /// التحقق من وجود صلاحية معينة
  bool hasPermission(String permissionName) {
    return grantedPermissions.any((p) => p.name == permissionName);
  }

  /// التحقق من وجود صلاحية معينة بالمعرف
  bool hasPermissionById(int permissionId) {
    return grantedPermissions.any((p) => p.id == permissionId);
  }

  /// الحصول على عدد الصلاحيات الممنوحة
  int get grantedPermissionsCount => grantedPermissions.length;

  /// الحصول على عدد الصلاحيات المتاحة
  int get availablePermissionsCount => availablePermissions.length;
}

/// إحصائيات صلاحيات المستخدمين
class UserPermissionStats {
  final int totalUsers;
  final int usersWithPermissions;
  final int totalPermissions;
  final int grantedPermissions;
  final Map<String, int> permissionsByCategory;
  final Map<int, int> permissionsByUser;

  const UserPermissionStats({
    required this.totalUsers,
    required this.usersWithPermissions,
    required this.totalPermissions,
    required this.grantedPermissions,
    required this.permissionsByCategory,
    required this.permissionsByUser,
  });

  factory UserPermissionStats.fromJson(Map<String, dynamic> json) {
    return UserPermissionStats(
      totalUsers: json['totalUsers'] as int,
      usersWithPermissions: json['usersWithPermissions'] as int,
      totalPermissions: json['totalPermissions'] as int,
      grantedPermissions: json['grantedPermissions'] as int,
      permissionsByCategory: Map<String, int>.from(json['permissionsByCategory'] as Map),
      permissionsByUser: Map<int, int>.from(json['permissionsByUser'] as Map),
    );
  }
}
