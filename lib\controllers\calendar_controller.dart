import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/calendar_models.dart';
import '../services/api/calendar_events_api_service.dart';

/// متحكم التقويم الرئيسي
class CalendarController extends GetxController {
  final CalendarEventsApiService _apiService = CalendarEventsApiService();

  // قوائم الأحداث
  final RxList<CalendarEvent> _events = <CalendarEvent>[].obs;
  final RxList<CalendarEvent> _filteredEvents = <CalendarEvent>[].obs;

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // التاريخ المحدد
  final Rx<DateTime> _selectedDate = DateTime.now().obs;

  // Getters
  List<CalendarEvent> get events => _events;
  List<CalendarEvent> get filteredEvents => _filteredEvents;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  DateTime get selectedDate => _selectedDate.value;

  @override
  void onInit() {
    super.onInit();
    loadEvents();
  }

  /// تحميل جميع الأحداث
  Future<void> loadEvents() async {
    try {
      _isLoading.value = true;
      _error.value = '';

      final events = await _apiService.getAllEvents();
      _events.assignAll(events);
      _filteredEvents.assignAll(events);
      
      debugPrint('تم تحميل ${events.length} حدث');
    } catch (e) {
      _error.value = 'خطأ في تحميل الأحداث: $e';
      debugPrint('خطأ في تحميل الأحداث: $e');
      
      // في حالة الخطأ، استخدام بيانات وهمية للاختبار
      _loadMockEvents();
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل بيانات وهمية للاختبار
  void _loadMockEvents() {
    final now = DateTime.now();
    final mockEvents = [
      CalendarEvent(
        id: 1,
        title: 'اجتماع فريق العمل',
        description: 'مراجعة التقدم الأسبوعي',
        startTime: now.millisecondsSinceEpoch ~/ 1000,
        endTime: now.add(const Duration(hours: 1)).millisecondsSinceEpoch ~/ 1000,
        userId: 1,
        createdAt: now.millisecondsSinceEpoch ~/ 1000,
        eventType: CalendarEventType.meeting,
        color: '#FF9800',
      ),
      CalendarEvent(
        id: 2,
        title: 'مهمة مهمة',
        description: 'إنجاز المشروع',
        startTime: now.add(const Duration(days: 1)).millisecondsSinceEpoch ~/ 1000,
        endTime: now.add(const Duration(days: 1, hours: 2)).millisecondsSinceEpoch ~/ 1000,
        userId: 1,
        createdAt: now.millisecondsSinceEpoch ~/ 1000,
        eventType: CalendarEventType.task,
        color: '#2196F3',
      ),
      CalendarEvent(
        id: 3,
        title: 'تذكير',
        description: 'موعد مع الطبيب',
        startTime: now.add(const Duration(days: 2)).millisecondsSinceEpoch ~/ 1000,
        endTime: now.add(const Duration(days: 2, hours: 1)).millisecondsSinceEpoch ~/ 1000,
        userId: 1,
        createdAt: now.millisecondsSinceEpoch ~/ 1000,
        eventType: CalendarEventType.reminder,
        color: '#9C27B0',
      ),
    ];

    _events.assignAll(mockEvents);
    _filteredEvents.assignAll(mockEvents);
  }

  /// الحصول على أحداث يوم معين
  List<CalendarEvent> getEventsForDay(DateTime day) {
    return _events.where((event) {
      final eventDate = event.startDateTime;
      return eventDate.year == day.year &&
             eventDate.month == day.month &&
             eventDate.day == day.day;
    }).toList();
  }

  /// تحديد التاريخ المحدد
  void setSelectedDate(DateTime date) {
    _selectedDate.value = date;
  }

  /// إضافة حدث جديد
  Future<void> addEvent(CalendarEvent event) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // محاولة إضافة الحدث عبر API
      // final newEvent = await _apiService.createEvent(event);
      
      // في الوقت الحالي، إضافة الحدث محلياً
      _events.add(event);
      _filteredEvents.add(event);
      
      update();
    } catch (e) {
      _error.value = 'خطأ في إضافة الحدث: $e';
      debugPrint('خطأ في إضافة الحدث: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث حدث
  Future<void> updateEvent(CalendarEvent event) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // محاولة تحديث الحدث عبر API
      // final updatedEvent = await _apiService.updateEvent(event.id, event);
      
      // في الوقت الحالي، تحديث الحدث محلياً
      final index = _events.indexWhere((e) => e.id == event.id);
      if (index != -1) {
        _events[index] = event;
        _filteredEvents[index] = event;
      }
      
      update();
    } catch (e) {
      _error.value = 'خطأ في تحديث الحدث: $e';
      debugPrint('خطأ في تحديث الحدث: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف حدث
  Future<void> deleteEvent(int eventId) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // محاولة حذف الحدث عبر API
      // await _apiService.deleteEvent(eventId);
      
      // في الوقت الحالي، حذف الحدث محلياً
      _events.removeWhere((e) => e.id == eventId);
      _filteredEvents.removeWhere((e) => e.id == eventId);
      
      update();
    } catch (e) {
      _error.value = 'خطأ في حذف الحدث: $e';
      debugPrint('خطأ في حذف الحدث: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// البحث في الأحداث
  void searchEvents(String query) {
    if (query.isEmpty) {
      _filteredEvents.assignAll(_events);
    } else {
      _filteredEvents.assignAll(
        _events.where((event) =>
          event.title.toLowerCase().contains(query.toLowerCase()) ||
          (event.description?.toLowerCase().contains(query.toLowerCase()) ?? false)
        ).toList()
      );
    }
  }

  /// تصفية الأحداث حسب النوع
  void filterEventsByType(CalendarEventType? type) {
    if (type == null) {
      _filteredEvents.assignAll(_events);
    } else {
      _filteredEvents.assignAll(
        _events.where((event) => event.eventType == type).toList()
      );
    }
  }

  /// الحصول على أحداث اليوم
  List<CalendarEvent> getTodayEvents() {
    return getEventsForDay(DateTime.now());
  }

  /// الحصول على الأحداث القادمة
  List<CalendarEvent> getUpcomingEvents({int days = 7}) {
    final now = DateTime.now();
    final endDate = now.add(Duration(days: days));
    
    return _events.where((event) {
      final eventDate = event.startDateTime;
      return eventDate.isAfter(now) && eventDate.isBefore(endDate);
    }).toList()..sort((a, b) => a.startDateTime.compareTo(b.startDateTime));
  }

  /// تحديث البيانات
  @override
  void refresh() {
    loadEvents();
  }
}
