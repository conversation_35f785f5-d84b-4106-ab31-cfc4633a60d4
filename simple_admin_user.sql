-- Simple admin user creation script
USE [databasetasks]
GO

-- Delete existing admin user
DELETE FROM users WHERE email = '<EMAIL>'

-- Disable foreign key checks temporarily
ALTER TABLE users NOCHECK CONSTRAINT ALL

-- Create admin user with a fresh password hash
INSERT INTO users (
    name, 
    first_name,
    last_name,
    email, 
    username, 
    password, 
    role, 
    is_active, 
    is_online, 
    is_deleted, 
    created_at
)
VALUES (
    'Admin User',
    'Admin',
    'User',
    '<EMAIL>',
    'admin',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtGtrKxQ4i', -- admin123 (fresh hash)
    5, -- SuperAdmin
    1, -- Active
    0, -- Not online
    0, -- Not deleted
    DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
)

-- Re-enable foreign key checks
ALTER TABLE users CHECK CONSTRAINT ALL

-- Verify creation
SELECT 
    id,
    name,
    email,
    username,
    role,
    is_active
FROM users
WHERE email = '<EMAIL>'

PRINT 'Admin user created successfully!'
PRINT 'Email: <EMAIL>'
PRINT 'Password: admin123'
PRINT 'Role: SuperAdmin (5)'

GO
