import 'package:flutter/material.dart';

/// مكون شريط البحث القابل لإعادة الاستخدام
class SearchBarWidget extends StatefulWidget {
  /// وحدة تحكم النص
  final TextEditingController? controller;
  
  /// نص التلميح
  final String hintText;
  
  /// دالة يتم استدعاؤها عند تغيير النص
  final Function(String)? onChanged;
  
  /// دالة يتم استدعاؤها عند إرسال النص
  final Function(String)? onSubmitted;
  
  /// مؤشر التركيز
  final FocusNode? focusNode;
  
  /// تمكين زر المسح
  final bool enableClear;
  
  /// ارتفاع الشريط
  final double height;
  
  /// لون الخلفية
  final Color? backgroundColor;
  
  /// نصف قطر الحدود
  final double borderRadius;
  
  /// لون الحدود
  final Color? borderColor;
  
  /// سماكة الحدود
  final double borderWidth;
  
  /// التباعد الداخلي
  final EdgeInsetsGeometry? padding;
  
  /// أيقونة البادئة
  final Widget? prefixIcon;
  
  /// أيقونة اللاحقة
  final Widget? suffixIcon;
  
  /// تمكين الحقل
  final bool enabled;
  
  /// نمط النص
  final TextStyle? textStyle;
  
  /// نمط نص التلميح
  final TextStyle? hintStyle;

  const SearchBarWidget({
    super.key,
    this.controller,
    this.hintText = 'بحث...',
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
    this.enableClear = true,
    this.height = 50,
    this.backgroundColor,
    this.borderRadius = 8.0,
    this.borderColor,
    this.borderWidth = 1.0,
    this.padding,
    this.prefixIcon,
    this.suffixIcon,
    this.enabled = true,
    this.textStyle,
    this.hintStyle,
  });

  @override
  State<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget> {
  late TextEditingController _controller;
  bool _showClearButton = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _showClearButton = _controller.text.isNotEmpty;
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (_showClearButton != hasText) {
      setState(() {
        _showClearButton = hasText;
      });
    }
  }

  void _clearText() {
    _controller.clear();
    widget.onChanged?.call('');
    setState(() {
      _showClearButton = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.grey.shade100,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null
            ? Border.all(
                color: widget.borderColor!,
                width: widget.borderWidth,
              )
            : null,
      ),
      child: TextField(
        controller: _controller,
        focusNode: widget.focusNode,
        enabled: widget.enabled,
        style: widget.textStyle,
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: widget.hintStyle,
          prefixIcon: widget.prefixIcon ?? const Icon(Icons.search),
          suffixIcon: _buildSuffixIcon(),
          border: InputBorder.none,
          contentPadding: widget.padding ?? 
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: widget.onChanged,
        onSubmitted: widget.onSubmitted,
      ),
    );
  }

  Widget? _buildSuffixIcon() {
    if (widget.suffixIcon != null) {
      return widget.suffixIcon;
    }
    
    if (widget.enableClear && _showClearButton) {
      return IconButton(
        icon: const Icon(Icons.clear),
        onPressed: _clearText,
        tooltip: 'مسح',
      );
    }
    
    return null;
  }
}
