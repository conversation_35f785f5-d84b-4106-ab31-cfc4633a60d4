using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using webApi.Services;

namespace webApi.Controllers;

/// <summary>
/// متحكم إدارة قاعدة البيانات
/// </summary>
[ApiController]
[Route("api/[controller]")]
[AllowAnonymous] // مؤقتاً للسماح بإنشاء البيانات الافتراضية
public class DatabaseController : ControllerBase
{
    private readonly DatabaseSeeder _seeder;
    private readonly ILogger<DatabaseController> _logger;

    public DatabaseController(DatabaseSeeder seeder, ILogger<DatabaseController> logger)
    {
        _seeder = seeder;
        _logger = logger;
    }

    /// <summary>
    /// إنشاء البيانات الافتراضية
    /// </summary>
    /// <returns>نتيجة العملية</returns>
    /// <response code="200">تم إنشاء البيانات بنجاح</response>
    /// <response code="500">خطأ في إنشاء البيانات</response>
    [HttpPost("seed")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> SeedDatabase()
    {
        try
        {
            await _seeder.SeedAsync();
            
            var response = new
            {
                success = true,
                message = "تم إنشاء البيانات الافتراضية بنجاح",
                users = new[]
                {
                    new { email = "<EMAIL>", password = "admin123", role = "SuperAdmin", description = "مدير النظام" },
                    new { email = "<EMAIL>", password = "manager123", role = "Manager", description = "مدير تجريبي" },
                    new { email = "<EMAIL>", password = "user123", role = "User", description = "مستخدم تجريبي" }
                }
            };

            _logger.LogInformation("تم إنشاء البيانات الافتراضية بنجاح");
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في إنشاء البيانات الافتراضية");
            return StatusCode(500, new
            {
                success = false,
                message = "حدث خطأ أثناء إنشاء البيانات الافتراضية",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// التحقق من وجود البيانات الافتراضية
    /// </summary>
    /// <returns>حالة البيانات</returns>
    /// <response code="200">معلومات حالة البيانات</response>
    [HttpGet("status")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetDatabaseStatus()
    {
        try
        {
            // يمكن إضافة فحص أكثر تفصيلاً هنا
            var response = new
            {
                success = true,
                message = "تم فحص حالة قاعدة البيانات",
                timestamp = DateTime.UtcNow,
                status = "ready"
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في فحص حالة قاعدة البيانات");
            return StatusCode(500, new
            {
                success = false,
                message = "حدث خطأ أثناء فحص حالة قاعدة البيانات",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// الحصول على عينة من البيانات للاختبار (بدون مصادقة)
    /// </summary>
    /// <returns>عينة من البيانات</returns>
    /// <response code="200">عينة البيانات</response>
    [HttpGet("sample-data")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetSampleData()
    {
        try
        {
            var response = new
            {
                success = true,
                message = "عينة من البيانات للاختبار",
                data = new
                {
                    users = new[]
                    {
                        new { id = 1, name = "مدير النظام", email = "<EMAIL>", role = "SuperAdmin" },
                        new { id = 2, name = "مدير تجريبي", email = "<EMAIL>", role = "Manager" },
                        new { id = 3, name = "مستخدم تجريبي", email = "<EMAIL>", role = "User" }
                    },
                    departments = new[]
                    {
                        new { id = 1, name = "قسم تقنية المعلومات", description = "قسم تطوير وصيانة الأنظمة" },
                        new { id = 2, name = "قسم الموارد البشرية", description = "قسم إدارة الموظفين" }
                    },
                    tasks = new[]
                    {
                        new { id = 1, title = "مهمة تجريبية", description = "وصف المهمة", status = "جديد", priority = "عالي" },
                        new { id = 2, title = "مهمة أخرى", description = "وصف آخر", status = "قيد التنفيذ", priority = "متوسط" }
                    }
                }
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في الحصول على عينة البيانات");
            return StatusCode(500, new
            {
                success = false,
                message = "حدث خطأ أثناء الحصول على عينة البيانات",
                error = ex.Message
            });
        }
    }
}
