# Simple PowerShell script to create admin user

Write-Host "Creating admin user..." -ForegroundColor Green

try {
    # Run SQL script
    sqlcmd -S ".\sqlexpress" -d "databasetasks" -i "create_admin_user.sql" -E
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success! Admin user created." -ForegroundColor Green
        Write-Host "Email: <EMAIL>" -ForegroundColor White
        Write-Host "Password: admin123" -ForegroundColor White
        Write-Host "Role: SuperAdmin" -ForegroundColor White
    } else {
        Write-Host "Failed to create user. Error code: $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
