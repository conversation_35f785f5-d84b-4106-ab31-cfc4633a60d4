import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/user_permission_models.dart';
import 'package:get/get.dart';

import '../services/api/user_permissions_api_service.dart';

/// متحكم صلاحيات المستخدمين
class UserPermissionsController extends GetxController {
  final UserPermissionsApiService _apiService = UserPermissionsApiService();

  // قوائم الصلاحيات
  final RxList<UserPermission> _allPermissions = <UserPermission>[].obs;
  final RxList<UserPermission> _filteredPermissions = <UserPermission>[].obs;
  final RxList<UserPermission> _userPermissions = <UserPermission>[].obs;

  // الصلاحية الحالية
  final Rx<UserPermission?> _currentPermission = Rx<UserPermission?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _userFilter = Rx<int?>(null);
  final Rx<String?> _permissionFilter = Rx<String?>(null);
  final Rx<String?> _moduleFilter = Rx<String?>(null);
  final RxBool _showActiveOnly = true.obs;

  // إحصائيات
  final RxInt _totalPermissions = 0.obs;
  final RxInt _activePermissions = 0.obs;
  final RxInt _revokedPermissions = 0.obs;

  // Getters
  List<UserPermission> get allPermissions => _allPermissions;
  List<UserPermission> get filteredPermissions => _filteredPermissions;
  List<UserPermission> get userPermissions => _userPermissions;
  UserPermission? get currentPermission => _currentPermission.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get userFilter => _userFilter.value;
  String? get permissionFilter => _permissionFilter.value;
  String? get moduleFilter => _moduleFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;
  int get totalPermissions => _totalPermissions.value;
  int get activePermissions => _activePermissions.value;
  int get revokedPermissions => _revokedPermissions.value;

  @override
  void onInit() {
    super.onInit();
    loadAllPermissions();
    loadStatistics();
  }

  /// تحميل جميع صلاحيات المستخدمين
  Future<void> loadAllPermissions() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permissions = await _apiService.getAllUserPermissions();
      _allPermissions.assignAll(permissions);
      _applyFilters();
      debugPrint('تم تحميل ${permissions.length} صلاحية مستخدم');
    } catch (e) {
      _error.value = 'خطأ في تحميل صلاحيات المستخدمين: $e';
      debugPrint('خطأ في تحميل صلاحيات المستخدمين: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الإحصائيات
  Future<void> loadStatistics() async {
    try {
      final stats = await _apiService.getUserPermissionsStatistics();
      _totalPermissions.value = stats['total'] ?? 0;
      _activePermissions.value = stats['active'] ?? 0;
      _revokedPermissions.value = stats['revoked'] ?? 0;
      debugPrint('تم تحميل إحصائيات صلاحيات المستخدمين');
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات صلاحيات المستخدمين: $e');
    }
  }

  /// الحصول على صلاحية مستخدم بالمعرف
  Future<void> getPermissionById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permission = await _apiService.getUserPermissionById(id);
      _currentPermission.value = permission;
      debugPrint('تم تحميل صلاحية المستخدم');
    } catch (e) {
      _error.value = 'خطأ في تحميل صلاحية المستخدم: $e';
      debugPrint('خطأ في تحميل صلاحية المستخدم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// منح صلاحية لمستخدم
  Future<bool> grantPermission(int userId, int permissionId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permission = await _apiService.grantPermission(userId, permissionId);
      _allPermissions.add(permission);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم منح صلاحية للمستخدم: $permissionId');
      return true;
    } catch (e) {
      _error.value = 'خطأ في منح الصلاحية للمستخدم: $e';
      debugPrint('خطأ في منح الصلاحية للمستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إلغاء صلاحية من مستخدم
  Future<bool> revokePermission(int userId, int permissionId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _apiService.revokePermission(userId, permissionId);
      if (success) {
        final index = _allPermissions.indexWhere((p) => p.userId == userId && p.permissionId == permissionId);
        if (index != -1) {
          _allPermissions[index] = _allPermissions[index].copyWith(isActive: false);
          _applyFilters();
        }
        await loadStatistics();
        debugPrint('تم إلغاء الصلاحية من المستخدم');
      }
      return success;
    } catch (e) {
      _error.value = 'خطأ في إلغاء الصلاحية من المستخدم: $e';
      debugPrint('خطأ في إلغاء الصلاحية من المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على صلاحيات مستخدم محدد
  Future<void> getUserPermissions(int userId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permissions = await _apiService.getUserPermissions(userId);
      _userPermissions.assignAll(permissions);
      debugPrint('تم تحميل ${permissions.length} صلاحية للمستخدم $userId');
    } catch (e) {
      _error.value = 'خطأ في تحميل صلاحيات المستخدم: $e';
      debugPrint('خطأ في تحميل صلاحيات المستخدم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// التحقق من صلاحية مستخدم
  Future<bool> checkUserPermission(int userId, String permissionName) async {
    try {
      final hasPermission = await _apiService.checkUserPermission(userId, permissionName);
      debugPrint('التحقق من صلاحية المستخدم $userId للصلاحية $permissionName: $hasPermission');
      return hasPermission;
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية المستخدم: $e');
      return false;
    }
  }

  /// منح صلاحيات متعددة لمستخدم
  Future<bool> grantMultiplePermissions(int userId, List<int> permissionIds) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final grantedPermissions = await _apiService.grantMultiplePermissions(userId, permissionIds);
      _allPermissions.addAll(grantedPermissions);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم منح ${grantedPermissions.length} صلاحية للمستخدم');
      return true;
    } catch (e) {
      _error.value = 'خطأ في منح الصلاحيات المتعددة: $e';
      debugPrint('خطأ في منح الصلاحيات المتعددة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إلغاء جميع صلاحيات مستخدم
  Future<bool> revokeAllUserPermissions(int userId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // الحصول على صلاحيات المستخدم أولاً
      final userPermissions = _allPermissions.where((p) => p.userId == userId && p.isActive).toList();
      final permissionIds = userPermissions.map((p) => p.permissionId).toList();

      if (permissionIds.isNotEmpty) {
        final success = await _apiService.revokeMultiplePermissions(userId, permissionIds);
        if (success) {
          for (int i = 0; i < _allPermissions.length; i++) {
            if (_allPermissions[i].userId == userId) {
              _allPermissions[i] = _allPermissions[i].copyWith(isActive: false);
            }
          }
          _applyFilters();
          await loadStatistics();
          debugPrint('تم إلغاء جميع صلاحيات المستخدم');
        }
        return success;
      }
      return true; // لا توجد صلاحيات للإلغاء
    } catch (e) {
      _error.value = 'خطأ في إلغاء جميع صلاحيات المستخدم: $e';
      debugPrint('خطأ في إلغاء جميع صلاحيات المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// نسخ صلاحيات من مستخدم إلى آخر
  Future<bool> copyPermissions(int fromUserId, int toUserId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // الحصول على صلاحيات المستخدم المصدر
      final sourcePermissions = await _apiService.getUserPermissions(fromUserId);
      final activePermissions = sourcePermissions.where((p) => p.isActive).toList();
      final permissionIds = activePermissions.map((p) => p.permissionId).toList();

      if (permissionIds.isNotEmpty) {
        final copiedPermissions = await _apiService.grantMultiplePermissions(toUserId, permissionIds);
        _allPermissions.addAll(copiedPermissions);
        _applyFilters();
        await loadStatistics();
        debugPrint('تم نسخ ${copiedPermissions.length} صلاحية من المستخدم $fromUserId إلى $toUserId');
      }
      return true;
    } catch (e) {
      _error.value = 'خطأ في نسخ الصلاحيات: $e';
      debugPrint('خطأ في نسخ الصلاحيات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على المستخدمين حسب الصلاحية
  Future<List<UserPermission>?> getUsersByPermission(int permissionId) async {
    try {
      final userPermissions = await _apiService.getUsersWithPermission(permissionId);
      debugPrint('تم تحميل ${userPermissions.length} مستخدم للصلاحية $permissionId');
      return userPermissions;
    } catch (e) {
      debugPrint('خطأ في تحميل المستخدمين حسب الصلاحية: $e');
      return null;
    }
  }

  /// تصدير تقرير الصلاحيات
  Future<String?> exportPermissionsReport(String format) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // تنفيذ تصدير محلي بدلاً من API غير متوفر
      final permissions = _allPermissions;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'permissions_report_$timestamp.$format';

      debugPrint('تم إنشاء تقرير الصلاحيات محلياً: $fileName');
      debugPrint('عدد الصلاحيات: ${permissions.length}');

      return fileName;
    } catch (e) {
      _error.value = 'خطأ في تصدير تقرير الصلاحيات: $e';
      debugPrint('خطأ في تصدير تقرير الصلاحيات: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allPermissions.where((userPermission) {
      // مرشح البحث - البحث في معرف المستخدم ومعرف الصلاحية
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        final userIdStr = userPermission.userId.toString();
        final permissionIdStr = userPermission.permissionId.toString();
        final userName = userPermission.user?.name.toLowerCase() ?? '';
        final permissionName = userPermission.permission?.name.toLowerCase() ?? '';

        if (!userIdStr.contains(query) &&
            !permissionIdStr.contains(query) &&
            !userName.contains(query) &&
            !permissionName.contains(query)) {
          return false;
        }
      }

      // مرشح المستخدم
      if (_userFilter.value != null && userPermission.userId != _userFilter.value) {
        return false;
      }

      // مرشح الصلاحية - استخدام معرف الصلاحية
      if (_permissionFilter.value != null) {
        final permissionName = userPermission.permission?.name;
        if (permissionName != _permissionFilter.value) {
          return false;
        }
      }

      // مرشح الوحدة - استخدام مجموعة الصلاحية
      if (_moduleFilter.value != null) {
        final permissionGroup = userPermission.permission?.permissionGroup;
        if (permissionGroup != _moduleFilter.value) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !userPermission.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredPermissions.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المستخدم
  void setUserFilter(int? userId) {
    _userFilter.value = userId;
    _applyFilters();
  }

  /// تعيين مرشح الصلاحية
  void setPermissionFilter(String? permission) {
    _permissionFilter.value = permission;
    _applyFilters();
  }

  /// تعيين مرشح الوحدة
  void setModuleFilter(String? module) {
    _moduleFilter.value = module;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _userFilter.value = null;
    _permissionFilter.value = null;
    _moduleFilter.value = null;
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllPermissions(),
      loadStatistics(),
    ]);
  }
}
