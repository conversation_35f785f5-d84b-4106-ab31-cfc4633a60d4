import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/test_users_controller.dart';
import '../../models/user_model.dart';
import '../../constants/app_colors.dart';

/// شاشة اختبار جلب بيانات المستخدمين بدون تسجيل دخول
class TestUsersScreen extends StatelessWidget {
  const TestUsersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(TestUsersController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار بيانات المستخدمين'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refreshData(),
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: () => controller.clearTestResults(),
            tooltip: 'مسح النتائج',
          ),
        ],
      ),
      body: Obx(() {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رسائل الحالة
              _buildStatusMessages(controller),
              
              const SizedBox(height: 16),
              
              // أزرار الاختبار
              _buildTestButtons(controller),
              
              const SizedBox(height: 24),
              
              // معلومات الخادم
              _buildServerInfo(controller),
              
              const SizedBox(height: 24),
              
              // نتائج الاختبار الشامل
              _buildSystemTestResults(controller),
              
              const SizedBox(height: 24),
              
              // قائمة المستخدمين
              _buildUsersList(controller),
              
              const SizedBox(height: 24),
              
              // نتائج الاختبارات
              _buildTestResults(controller),
            ],
          ),
        );
      }),
    );
  }

  /// بناء رسائل الحالة
  Widget _buildStatusMessages(TestUsersController controller) {
    return Column(
      children: [
        if (controller.successMessage.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green),
            ),
            child: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.green),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    controller.successMessage,
                    style: const TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
        
        if (controller.error.isNotEmpty)
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red),
            ),
            child: Row(
              children: [
                const Icon(Icons.error, color: Colors.red),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    controller.error,
                    style: const TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// بناء أزرار الاختبار
  Widget _buildTestButtons(TestUsersController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختبارات النظام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: controller.isTestingConnection 
                      ? null 
                      : () => controller.testConnection(),
                  icon: controller.isTestingConnection
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.wifi),
                  label: const Text('اختبار الاتصال'),
                ),
                
                ElevatedButton.icon(
                  onPressed: controller.isSeedingData 
                      ? null 
                      : () => controller.seedTestData(),
                  icon: controller.isSeedingData
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.add_circle),
                  label: const Text('إضافة بيانات تجريبية'),
                ),
                
                ElevatedButton.icon(
                  onPressed: controller.isLoading 
                      ? null 
                      : () => controller.tryFetchUsers(),
                  icon: controller.isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.people),
                  label: const Text('جلب المستخدمين'),
                ),
                
                ElevatedButton.icon(
                  onPressed: controller.isRunningSystemTest 
                      ? null 
                      : () => controller.runSystemTest(),
                  icon: controller.isRunningSystemTest
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.assessment),
                  label: const Text('اختبار شامل'),
                ),
                
                ElevatedButton.icon(
                  onPressed: controller.isLoading 
                      ? null 
                      : () => controller.clearAllData(),
                  icon: const Icon(Icons.delete_forever),
                  label: const Text('مسح البيانات'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade100,
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الخادم
  Widget _buildServerInfo(TestUsersController controller) {
    if (controller.serverInfo == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الخادم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            ...controller.serverInfo!.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Text(
                      '${entry.key}: ',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Expanded(
                      child: Text(entry.value.toString()),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// بناء نتائج الاختبار الشامل
  Widget _buildSystemTestResults(TestUsersController controller) {
    if (controller.systemTestResults == null) return const SizedBox.shrink();

    final results = controller.systemTestResults!;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نتائج الاختبار الشامل',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            ...results.entries.where((entry) => entry.key != 'errors').map((entry) {
              final value = entry.value;
              final isSuccess = value is bool ? value : true;
              
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(
                      isSuccess ? Icons.check_circle : Icons.error,
                      color: isSuccess ? Colors.green : Colors.red,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${entry.key}: ',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Expanded(
                      child: Text(value.toString()),
                    ),
                  ],
                ),
              );
            }).toList(),
            
            if (results['errors'] != null && (results['errors'] as List).isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  const Text(
                    'الأخطاء:',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
                  ),
                  ...(results['errors'] as List).map((error) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text(
                        '• $error',
                        style: const TextStyle(color: Colors.red),
                      ),
                    );
                  }).toList(),
                ],
              ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة المستخدمين
  Widget _buildUsersList(TestUsersController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المستخدمون (${controller.users.length})',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                if (controller.users.isNotEmpty)
                  TextButton.icon(
                    onPressed: () => _showUserTestDialog(controller),
                    icon: const Icon(Icons.person_search),
                    label: const Text('اختبار مستخدم واحد'),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            
            if (controller.users.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Column(
                    children: [
                      Icon(Icons.people_outline, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد بيانات مستخدمين',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'جرب إضافة البيانات التجريبية أولاً',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: controller.users.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final user = controller.users[index];
                  return _buildUserTile(user);
                },
              ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر المستخدم
  Widget _buildUserTile(User user) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.primary,
        child: Text(
          user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      title: Text(
        user.name,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('البريد الإلكتروني: ${user.email}'),
          Text('الدور: ${user.role.displayName}'),
          if (user.departmentId != null)
            Text('القسم: ${user.departmentId}'),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            user.isActive ? Icons.check_circle : Icons.cancel,
            color: user.isActive ? Colors.green : Colors.red,
          ),
          Text(
            user.isActive ? 'نشط' : 'غير نشط',
            style: TextStyle(
              fontSize: 12,
              color: user.isActive ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
      isThreeLine: true,
    );
  }

  /// بناء نتائج الاختبارات
  Widget _buildTestResults(TestUsersController controller) {
    if (controller.testResults.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'سجل الاختبارات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.testResults.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final result = controller.testResults[index];
                final success = result['success'] as bool;
                
                return ListTile(
                  dense: true,
                  leading: Icon(
                    success ? Icons.check_circle : Icons.error,
                    color: success ? Colors.green : Colors.red,
                    size: 20,
                  ),
                  title: Text(
                    result['test_name'] as String,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Text(result['message'] as String),
                  trailing: Text(
                    DateTime.parse(result['timestamp'] as String)
                        .toLocal()
                        .toString()
                        .substring(11, 19),
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// إظهار حوار اختبار مستخدم واحد
  void _showUserTestDialog(TestUsersController controller) {
    final textController = TextEditingController();
    
    Get.dialog(
      AlertDialog(
        title: const Text('اختبار جلب مستخدم واحد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل معرف المستخدم:'),
            const SizedBox(height: 16),
            TextField(
              controller: textController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'معرف المستخدم',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final userId = int.tryParse(textController.text);
              if (userId != null) {
                Get.back();
                controller.tryGetSingleUser(userId);
              }
            },
            child: const Text('اختبار'),
          ),
        ],
      ),
    );
  }
}
