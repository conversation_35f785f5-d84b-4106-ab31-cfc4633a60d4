-- Check current structure of archive tables
USE [databasetasks]
GO

-- Check archive_categories table structure
PRINT 'Archive Categories Table Structure:'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'archive_categories'
ORDER BY ORDINAL_POSITION;

PRINT ''
PRINT 'Archive Documents Table Structure:'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'archive_documents'
ORDER BY ORDINAL_POSITION;

PRINT ''
PRINT 'Archive Tags Table Structure:'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'archive_tags'
ORDER BY ORDINAL_POSITION;

PRINT ''
PRINT 'Checking if archive_document_tags table exists:'
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[archive_document_tags]') AND type in (N'U'))
    PRINT 'archive_document_tags table EXISTS'
ELSE
    PRINT 'archive_document_tags table DOES NOT EXIST'
