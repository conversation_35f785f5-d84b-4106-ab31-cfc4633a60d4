import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/power_bi_models.dart';
import '../services/api/power_bi_api_service.dart';
import 'auth_controller.dart';

/// وحدة تحكم Power BI
class PowerBIController extends GetxController {
  final PowerBIApiService _apiService = PowerBIApiService();

  // قوائم التقارير
  final RxList<PowerBIReport> _myReports = <PowerBIReport>[].obs;
  final RxList<PowerBIReport> _sharedReports = <PowerBIReport>[].obs;

  // التقرير الحالي وبياناته
  final Rx<PowerBIReport?> _currentReport = Rx<PowerBIReport?>(null);
  final RxMap<String, dynamic> _chartData = <String, dynamic>{}.obs;

  // الجداول والأعمدة المتاحة
  final RxList<String> _availableTables = <String>[].obs;
  final RxMap<String, List<Map<String, dynamic>>> _tableColumns = 
      <String, List<Map<String, dynamic>>>{}.obs;

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;

  // Getters
  List<PowerBIReport> get myReports => _myReports;
  List<PowerBIReport> get sharedReports => _sharedReports;
  Rx<PowerBIReport?> get currentReport => _currentReport;
  RxMap<String, dynamic> get chartData => _chartData;
  List<String> get availableTables => _availableTables;
  Map<String, List<Map<String, dynamic>>> get tableColumns => _tableColumns;
  RxBool get isLoading => _isLoading;
  RxString get errorMessage => _errorMessage;

  @override
  void onInit() {
    super.onInit();
    // لا نحمل البيانات تلقائياً - سيتم تحميلها عند الحاجة
    debugPrint('تم تهيئة PowerBIController');
  }

  /// تحميل البيانات الأولية (يتم استدعاؤها عند الحاجة)
  Future<void> loadInitialData() async {
    await loadAvailableTables();
  }

  /// تحميل تقاريري
  Future<void> loadMyReports() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';
      
      final reports = await _apiService.getMyReports();
      _myReports.assignAll(reports);
      
      debugPrint('تم تحميل ${reports.length} من تقاريري');
    } catch (e) {
      _errorMessage.value = 'خطأ في تحميل التقارير: $e';
      debugPrint('خطأ في تحميل تقاريري: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل التقارير المشتركة
  Future<void> loadSharedReports() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';
      
      final reports = await _apiService.getSharedReports();
      _sharedReports.assignAll(reports);
      
      debugPrint('تم تحميل ${reports.length} من التقارير المشتركة');
    } catch (e) {
      _errorMessage.value = 'خطأ في تحميل التقارير المشتركة: $e';
      debugPrint('خطأ في تحميل التقارير المشتركة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل تقرير محدد
  Future<void> loadReport(String reportId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';
      
      final report = await _apiService.getReport(reportId);
      if (report != null) {
        _currentReport.value = report;
        // تحميل بيانات الرسم البياني
        await getChartData(report);
        debugPrint('تم تحميل التقرير: ${report.title}');
      } else {
        _errorMessage.value = 'لم يتم العثور على التقرير';
      }
    } catch (e) {
      _errorMessage.value = 'خطأ في تحميل التقرير: $e';
      debugPrint('خطأ في تحميل التقرير: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء تقرير جديد
  Future<PowerBIReport?> createReport({
    required String title,
    String? description,
    required PowerBIChartType chartType,
    required String tableName,
    required List<String> columnNames,
    required String xAxisColumn,
    required String yAxisColumn,
    AggregateFunction? yAxisAggregateFunction,
    String? sizeColumn,
    String? colorColumn,
    String? filterCriteria,
    List<String>? relatedTables,
    List<String>? joinConditions,
    List<String>? joinTypes,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final report = PowerBIReport(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        description: description,
        chartType: chartType,
        createdById: '1', // TODO: الحصول من AuthController
        createdAt: DateTime.now(),
        tableName: tableName,
        columnNames: columnNames,
        xAxisColumn: xAxisColumn,
        yAxisColumn: yAxisColumn,
        yAxisAggregateFunction: yAxisAggregateFunction,
        sizeColumn: sizeColumn,
        colorColumn: colorColumn,
        filterCriteria: filterCriteria,
        relatedTables: relatedTables,
        joinConditions: joinConditions,
        joinTypes: joinTypes,
      );

      final createdReport = await _apiService.createReport(report);
      if (createdReport != null) {
        _myReports.add(createdReport);
        debugPrint('تم إنشاء التقرير: ${createdReport.title}');
        return createdReport;
      } else {
        _errorMessage.value = 'فشل في إنشاء التقرير';
      }
    } catch (e) {
      _errorMessage.value = 'خطأ في إنشاء التقرير: $e';
      debugPrint('خطأ في إنشاء التقرير: $e');
    } finally {
      _isLoading.value = false;
    }
    return null;
  }

  /// تحديث تقرير
  Future<PowerBIReport?> updateReport(PowerBIReport report) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final updatedReport = await _apiService.updateReport(report);
      if (updatedReport != null) {
        // تحديث التقرير في القائمة
        final index = _myReports.indexWhere((r) => r.id == report.id);
        if (index != -1) {
          _myReports[index] = updatedReport;
        }
        
        // تحديث التقرير الحالي إذا كان هو نفسه
        if (_currentReport.value?.id == report.id) {
          _currentReport.value = updatedReport;
        }
        
        debugPrint('تم تحديث التقرير: ${updatedReport.title}');
        return updatedReport;
      } else {
        _errorMessage.value = 'فشل في تحديث التقرير';
      }
    } catch (e) {
      _errorMessage.value = 'خطأ في تحديث التقرير: $e';
      debugPrint('خطأ في تحديث التقرير: $e');
    } finally {
      _isLoading.value = false;
    }
    return null;
  }

  /// حذف تقرير
  Future<bool> deleteReport(String reportId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final success = await _apiService.deleteReport(reportId);
      if (success) {
        _myReports.removeWhere((r) => r.id == reportId);
        
        // إذا كان التقرير المحذوف هو التقرير الحالي، قم بمسحه
        if (_currentReport.value?.id == reportId) {
          _currentReport.value = null;
          _chartData.clear();
        }
        
        debugPrint('تم حذف التقرير');
        return true;
      } else {
        _errorMessage.value = 'فشل في حذف التقرير';
      }
    } catch (e) {
      _errorMessage.value = 'خطأ في حذف التقرير: $e';
      debugPrint('خطأ في حذف التقرير: $e');
    } finally {
      _isLoading.value = false;
    }
    return false;
  }

  /// تحميل الجداول المتاحة
  Future<void> loadAvailableTables() async {
    try {
      // التحقق من حالة المصادقة قبل تحميل الجداول
      if (!Get.isRegistered<AuthController>()) {
        debugPrint('AuthController غير مسجل - تخطي تحميل الجداول');
        return;
      }

      final authController = Get.find<AuthController>();
      if (authController.currentUser.value == null) {
        debugPrint('المستخدم غير مسجل دخول - تخطي تحميل الجداول');
        return;
      }

      final tables = await _apiService.getAvailableTables();
      _availableTables.assignAll(tables);
      debugPrint('تم تحميل ${tables.length} جدول');
    } catch (e) {
      debugPrint('خطأ في تحميل الجداول: $e');
    }
  }

  /// تحميل أعمدة جدول محدد
  Future<void> loadTableColumns(String tableName) async {
    try {
      final columns = await _apiService.getTableColumns(tableName);
      _tableColumns[tableName] = columns;
      debugPrint('تم تحميل ${columns.length} عمود للجدول $tableName');
    } catch (e) {
      debugPrint('خطأ في تحميل أعمدة الجدول: $e');
    }
  }

  /// الحصول على بيانات الرسم البياني
  Future<Map<String, dynamic>> getChartData(PowerBIReport report) async {
    try {
      _isLoading.value = true;
      final data = await _apiService.getChartData(report);
      _chartData.assignAll(data);
      debugPrint('تم تحميل بيانات الرسم البياني');
      return data;
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الرسم البياني: $e');
      return {};
    } finally {
      _isLoading.value = false;
    }
  }

  /// اقتراح علاقات بين الجداول
  Future<List<Map<String, String>>> suggestTableRelations(String table1, String table2) async {
    try {
      return await _apiService.suggestTableRelations(table1, table2);
    } catch (e) {
      debugPrint('خطأ في اقتراح العلاقات: $e');
      return [];
    }
  }

  /// مشاركة تقرير
  Future<bool> shareReport(String reportId, String userEmail) async {
    try {
      return await _apiService.shareReport(reportId, userEmail);
    } catch (e) {
      debugPrint('خطأ في مشاركة التقرير: $e');
      return false;
    }
  }

  /// تصدير تقرير
  Future<String?> exportReport(String reportId, String format) async {
    try {
      if (format.toLowerCase() == 'pdf') {
        return await _apiService.exportToPdf(reportId);
      } else if (format.toLowerCase() == 'excel') {
        return await _apiService.exportToExcel(reportId);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير: $e');
      return null;
    }
  }

  /// حفظ تغييرات التقرير
  Future<bool> saveReportChanges() async {
    try {
      final report = _currentReport.value;
      if (report == null) return false;

      final updatedReport = await updateReport(report);
      return updatedReport != null;
    } catch (e) {
      debugPrint('خطأ في حفظ تغييرات التقرير: $e');
      return false;
    }
  }

  /// تحديث نوع الرسم البياني
  Future<void> updateChartType(PowerBIChartType newType) async {
    try {
      final report = _currentReport.value;
      if (report == null) return;

      final updatedReport = report.copyWith(
        chartType: newType,
        updatedAt: DateTime.now(),
      );

      _currentReport.value = updatedReport;

      // تحديث بيانات الرسم البياني
      await getChartData(updatedReport);
    } catch (e) {
      debugPrint('خطأ في تحديث نوع الرسم البياني: $e');
    }
  }

  /// تحديث أعمدة الرسم البياني
  Future<void> updateChartColumns({
    String? newXAxisColumn,
    String? newYAxisColumn,
    String? newColorColumn,
    String? newSizeColumn,
  }) async {
    try {
      final report = _currentReport.value;
      if (report == null) return;

      final updatedReport = report.copyWith(
        xAxisColumn: newXAxisColumn ?? report.xAxisColumn,
        yAxisColumn: newYAxisColumn ?? report.yAxisColumn,
        colorColumn: newColorColumn ?? report.colorColumn,
        sizeColumn: newSizeColumn ?? report.sizeColumn,
        updatedAt: DateTime.now(),
      );

      _currentReport.value = updatedReport;

      // تحديث بيانات الرسم البياني
      await getChartData(updatedReport);
    } catch (e) {
      debugPrint('خطأ في تحديث أعمدة الرسم البياني: $e');
    }
  }
}
