import 'package:flutter/material.dart';
import 'package:get/get.dart';

// تم إزالة sync_service - سيتم استخدام API

/// أحجام مكون حالة التزامن
enum SyncStatusSize {
  /// صغير (أيقونة فقط)
  small,

  /// عادي (أيقونة ونص)
  normal,

  /// كبير (أيقونة ونص وتفاصيل)
  large,
}

/// مكون لعرض حالة التزامن
///
/// يعرض هذا المكون حالة التزامن الحالية ويوفر خيارات للتحكم في إعدادات التزامن
class SyncStatusWidget extends StatelessWidget {
  /// حجم المكون
  final SyncStatusSize size;

  /// ما إذا كان يجب عرض التفاصيل
  final bool showDetails;

  /// ما إذا كان يجب عرض الإعدادات
  final bool showSettings;

  /// دالة يتم استدعاؤها عند النقر على المكون
  final VoidCallback? onTap;

  /// تم إزالة SyncService - سيتم استخدام API

  /// حالة الاتصال
  final RxBool _isConnected = true.obs;

  SyncStatusWidget({
    super.key,
    this.size = SyncStatusSize.normal,
    this.showDetails = true,
    this.showSettings = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // تم إزالة SyncService - استخدام قيم ثابتة مؤقتة
    return Obx(() {
      final isSyncing = false; // سيتم تحديثه عبر API
      final syncStatus = 'متصل'; // سيتم تحديثه عبر API
      final isConnected = _isConnected.value;

      return _buildStatusWidget(context, isSyncing, syncStatus, isConnected);
    });
  }

  /// بناء مكون حالة التزامن
  Widget _buildStatusWidget(
    BuildContext context,
    bool isSyncing,
    String syncStatus,
    bool isConnected,
  ) {
    // تحديد الألوان والأيقونات بناءً على حالة التزامن
    Color backgroundColor;
    IconData iconData;
    String statusText;

    if (!isConnected) {
      // غير متصل بالإنترنت
      backgroundColor = Colors.grey.withValues(alpha: 204); // 0.8 * 255 = 204
      iconData = Icons.sync_disabled;
      statusText = 'التزامن متوقف (غير متصل)';
    } else if (isSyncing) {
      // جاري التزامن
      backgroundColor = Colors.blue.withValues(alpha: 204); // 0.8 * 255 = 204
      iconData = Icons.sync;
      statusText = 'جاري التزامن...';
    } else if (syncStatus.contains('خطأ')) {
      // خطأ في التزامن
      backgroundColor = Colors.orange.withValues(alpha: 204); // 0.8 * 255 = 204
      iconData = Icons.sync_problem;
      statusText = 'خطأ في التزامن';
    } else {
      // تم التزامن بنجاح
      backgroundColor = Colors.green.withValues(alpha: 204); // 0.8 * 255 = 204
      iconData = Icons.sync_alt;
      statusText = 'تم التزامن';
    }

    // بناء المكون حسب الحجم المطلوب
    switch (size) {
      case SyncStatusSize.small:
        return _buildSmallIndicator(backgroundColor, iconData, isSyncing);
      case SyncStatusSize.normal:
        return _buildNormalIndicator(
            context, backgroundColor, iconData, statusText, isSyncing);
      case SyncStatusSize.large:
        return _buildLargeIndicator(context, backgroundColor, iconData,
            statusText, syncStatus, isSyncing);
    }
  }

  /// بناء مؤشر صغير (أيقونة فقط)
  Widget _buildSmallIndicator(
      Color backgroundColor, IconData iconData, bool isSyncing) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: isSyncing
            ? _buildRotatingIcon(iconData)
            : Icon(
                iconData,
                color: Colors.white,
                size: 16,
              ),
      ),
    );
  }

  /// بناء مؤشر عادي (أيقونة ونص)
  Widget _buildNormalIndicator(
    BuildContext context,
    Color backgroundColor,
    IconData iconData,
    String statusText,
    bool isSyncing,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            isSyncing
                ? _buildRotatingIcon(iconData)
                : Icon(
                    iconData,
                    color: Colors.white,
                    size: 16,
                  ),
            const SizedBox(width: 8),
            Text(
              statusText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر كبير (أيقونة ونص وتفاصيل)
  Widget _buildLargeIndicator(
    BuildContext context,
    Color backgroundColor,
    IconData iconData,
    String statusText,
    String syncStatus,
    bool isSyncing,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                isSyncing
                    ? _buildRotatingIcon(iconData, size: 24)
                    : Icon(
                        iconData,
                        color: Colors.white,
                        size: 24,
                      ),
                const SizedBox(width: 12),
                Text(
                  statusText,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            if (showDetails) ...[
              const SizedBox(height: 8),
              Text(
                syncStatus,
                style: const TextStyle(color: Colors.white, fontSize: 12),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              _buildSyncDetails(),
            ],
            if (showSettings) ...[
              const SizedBox(height: 8),
              // تم إزالة إعدادات التزامن - سيتم عبر API
              const Text(
                'إعدادات التزامن ستتم عبر API',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء أيقونة دوارة
  Widget _buildRotatingIcon(IconData iconData, {double size = 16}) {
    return RotationTransition(
      turns:
          AlwaysStoppedAnimation(DateTime.now().millisecondsSinceEpoch / 1000),
      child: Icon(
        iconData,
        color: Colors.white,
        size: size,
      ),
    );
  }

  /// بناء قسم تفاصيل التزامن
  Widget _buildSyncDetails() {
    // تم إزالة SyncService - استخدام قيم ثابتة مؤقتة
    final syncIntervalMs = 30000; // 30 ثانية
    final autoRetry = true;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'فترة التزامن: ${syncIntervalMs ~/ 1000} ثانية',
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
        Text(
          'إعادة المحاولة التلقائية: ${autoRetry ? 'مفعلة' : 'معطلة'}',
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
      ],
    );
  }
}
