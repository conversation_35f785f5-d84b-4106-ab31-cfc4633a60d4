import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:flutter_application_2/controllers/database_management_controller.dart';
import 'package:flutter_application_2/services/database_management_service.dart';
import 'package:flutter_application_2/helpers/database_helper.dart';
import 'package:flutter_application_2/models/database_table_model.dart';

/// اختبارات تبويب إدارة قاعدة البيانات
void main() {
  group('Database Management Tests', () {
    late DatabaseManagementController controller;
    late DatabaseManagementService service;
    late DatabaseHelper helper;

    setUp(() {
      // تهيئة المساعدات والخدمات
      helper = DatabaseHelper();
      service = DatabaseManagementService(helper);
      controller = DatabaseManagementController(service);
      
      // تسجيل المتحكم في GetX
      Get.put(controller);
    });

    tearDown(() {
      // تنظيف بعد كل اختبار
      Get.delete<DatabaseManagementController>();
    });

    test('Controller initialization', () {
      expect(controller, isNotNull);
      expect(controller.tables, isEmpty);
      expect(controller.selectedTable, isNull);
      expect(controller.tableData, isEmpty);
    });

    test('Load tables', () async {
      // اختبار تحميل الجداول
      await controller.loadTables();
      
      // التحقق من أن الجداول تم تحميلها
      expect(controller.isLoadingTables, isFalse);
      // ملاحظة: قد تكون القائمة فارغة إذا لم يكن هناك اتصال بقاعدة البيانات
    });

    test('Select table', () {
      // إنشاء جدول وهمي للاختبار
      final testTable = DatabaseTable(
        name: 'TestTable',
        displayName: 'جدول اختبار',
        columns: [
          DatabaseColumn(
            name: 'Id',
            type: DatabaseColumnType.integer,
            isPrimaryKey: true,
            isRequired: true,
          ),
          DatabaseColumn(
            name: 'Name',
            type: DatabaseColumnType.text,
            isRequired: true,
          ),
        ],
      );

      // اختيار الجدول
      controller.selectTable(testTable);

      // التحقق من أن الجدول تم اختياره
      expect(controller.selectedTable, equals(testTable));
      expect(controller.currentPage, equals(1));
    });

    test('Search functionality', () {
      // اختبار وظيفة البحث
      const searchQuery = 'test search';
      controller.search(searchQuery);

      expect(controller.searchQuery, equals(searchQuery));
      expect(controller.currentPage, equals(1));
    });

    test('Pagination', () {
      // اختبار التنقل بين الصفحات
      controller.nextPage();
      expect(controller.currentPage, equals(2));

      controller.previousPage();
      expect(controller.currentPage, equals(1));

      controller.goToPage(5);
      expect(controller.currentPage, equals(5));
    });

    test('Page size change', () {
      // اختبار تغيير حجم الصفحة
      const newPageSize = 100;
      controller.changePageSize(newPageSize);

      expect(controller.pageSize, equals(newPageSize));
      expect(controller.currentPage, equals(1)); // يجب إعادة تعيين الصفحة للأولى
    });

    test('Clear filters', () {
      // إضافة بعض الفلاتر والبحث
      controller.search('test');
      controller.applyFilters({'status': 'active'});

      // مسح الفلاتر
      controller.clearFilters();

      expect(controller.searchQuery, isEmpty);
      expect(controller.filters, isEmpty);
      expect(controller.currentPage, equals(1));
    });

    test('Error handling', () {
      // اختبار معالجة الأخطاء
      controller.clearMessages();
      expect(controller.error, isEmpty);
      expect(controller.successMessage, isEmpty);
    });
  });

  group('Database Table Model Tests', () {
    test('DatabaseTable creation', () {
      final table = DatabaseTable(
        name: 'Users',
        displayName: 'المستخدمون',
        columns: [
          DatabaseColumn(
            name: 'Id',
            type: DatabaseColumnType.integer,
            isPrimaryKey: true,
          ),
          DatabaseColumn(
            name: 'Name',
            type: DatabaseColumnType.text,
            isRequired: true,
          ),
        ],
        recordCount: 10,
      );

      expect(table.name, equals('Users'));
      expect(table.displayName, equals('المستخدمون'));
      expect(table.columns.length, equals(2));
      expect(table.recordCount, equals(10));
      expect(table.primaryKeyColumn?.name, equals('Id'));
    });

    test('DatabaseColumn creation', () {
      final column = DatabaseColumn(
        name: 'Email',
        displayName: 'البريد الإلكتروني',
        type: DatabaseColumnType.email,
        isRequired: true,
        isSearchable: true,
      );

      expect(column.name, equals('Email'));
      expect(column.displayName, equals('البريد الإلكتروني'));
      expect(column.type, equals(DatabaseColumnType.email));
      expect(column.isRequired, isTrue);
      expect(column.isSearchable, isTrue);
      expect(column.effectiveDisplayName, equals('البريد الإلكتروني'));
    });

    test('DatabaseColumnType extension', () {
      expect(DatabaseColumnType.text.displayName, equals('نص'));
      expect(DatabaseColumnType.integer.displayName, equals('رقم صحيح'));
      expect(DatabaseColumnType.email.displayName, equals('بريد إلكتروني'));
      expect(DatabaseColumnType.boolean.displayName, equals('منطقي'));
    });
  });

  group('Database Service Tests', () {
    late DatabaseManagementService service;
    late DatabaseHelper helper;

    setUp(() {
      helper = DatabaseHelper();
      service = DatabaseManagementService(helper);
    });

    test('Service initialization', () {
      expect(service, isNotNull);
    });

    test('Get available tables', () async {
      // اختبار الحصول على الجداول المتاحة
      final tables = await service.getAvailableTables();
      
      // التحقق من أن النتيجة قائمة (قد تكون فارغة)
      expect(tables, isA<List<DatabaseTable>>());
    });

    test('Test connection', () async {
      // اختبار الاتصال بقاعدة البيانات
      final isConnected = await service.testConnection();
      
      // التحقق من أن النتيجة boolean
      expect(isConnected, isA<bool>());
    });
  });

  group('Integration Tests', () {
    test('Full workflow test', () async {
      // اختبار تدفق العمل الكامل
      final helper = DatabaseHelper();
      final service = DatabaseManagementService(helper);
      final controller = DatabaseManagementController(service);

      // تحميل الجداول
      await controller.loadTables();

      // إذا كانت هناك جداول، اختر الأول
      if (controller.tables.isNotEmpty) {
        controller.selectTable(controller.tables.first);
        
        // تحميل بيانات الجدول
        await controller.loadTableData();
        
        // اختبار البحث
        controller.search('test');
        
        // اختبار التنقل
        controller.nextPage();
        controller.previousPage();
      }

      // التحقق من عدم وجود أخطاء
      expect(controller.error, isEmpty);
    });
  });
}
