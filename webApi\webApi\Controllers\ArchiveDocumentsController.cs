using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// متحكم مستندات الأرشيف
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ArchiveDocumentsController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public ArchiveDocumentsController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع مستندات الأرشيف
        /// </summary>
        /// <returns>قائمة بجميع مستندات الأرشيف</returns>
        /// <response code="200">إرجاع قائمة مستندات الأرشيف</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveDocument>>> GetArchiveDocuments()
        {
            return await _context.ArchiveDocuments
                .Include(ad => ad.Category)
                .Include(ad => ad.CreatedByNavigation)
                .Include(ad => ad.UploadedByNavigation)
                .Include(ad => ad.Tags)
                .Where(ad => !ad.IsDeleted)
                .OrderByDescending(ad => ad.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على مستند أرشيف محدد
        /// </summary>
        /// <param name="id">معرف مستند الأرشيف</param>
        /// <returns>مستند الأرشيف المطلوب</returns>
        /// <response code="200">إرجاع مستند الأرشيف</response>
        /// <response code="404">مستند الأرشيف غير موجود</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ArchiveDocument>> GetArchiveDocument(int id)
        {
            var archiveDocument = await _context.ArchiveDocuments
                .Include(ad => ad.Category)
                .Include(ad => ad.CreatedByNavigation)
                .Include(ad => ad.UploadedByNavigation)
                .Include(ad => ad.Tags)
                .FirstOrDefaultAsync(ad => ad.Id == id && !ad.IsDeleted);

            if (archiveDocument == null)
            {
                return NotFound();
            }

            return archiveDocument;
        }

        /// <summary>
        /// الحصول على مستندات الأرشيف حسب الفئة
        /// </summary>
        /// <param name="categoryId">معرف الفئة</param>
        /// <returns>قائمة مستندات الفئة</returns>
        /// <response code="200">إرجاع قائمة مستندات الفئة</response>
        [HttpGet("category/{categoryId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveDocument>>> GetDocumentsByCategory(int categoryId)
        {
            return await _context.ArchiveDocuments
                .Include(ad => ad.Category)
                .Include(ad => ad.CreatedByNavigation)
                .Include(ad => ad.UploadedByNavigation)
                .Include(ad => ad.Tags)
                .Where(ad => ad.CategoryId == categoryId && !ad.IsDeleted)
                .OrderByDescending(ad => ad.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على علامات مستند
        /// </summary>
        /// <param name="id">معرف المستند</param>
        /// <returns>قائمة علامات المستند</returns>
        /// <response code="200">إرجاع قائمة علامات المستند</response>
        /// <response code="404">المستند غير موجود</response>
        [HttpGet("{id}/tags")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<ArchiveTag>>> GetDocumentTags(int id)
        {
            var document = await _context.ArchiveDocuments
                .Include(ad => ad.Tags)
                .FirstOrDefaultAsync(ad => ad.Id == id && !ad.IsDeleted);

            if (document == null)
            {
                return NotFound();
            }

            return Ok(document.Tags);
        }

        /// <summary>
        /// إنشاء مستند أرشيف جديد
        /// </summary>
        /// <param name="archiveDocument">بيانات مستند الأرشيف</param>
        /// <returns>مستند الأرشيف المُنشأ</returns>
        /// <response code="201">تم إنشاء مستند الأرشيف بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ArchiveDocument>> PostArchiveDocument(ArchiveDocument archiveDocument)
        {
            // تعيين القيم التلقائية
            archiveDocument.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            archiveDocument.UploadedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            archiveDocument.IsDeleted = false;

            _context.ArchiveDocuments.Add(archiveDocument);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetArchiveDocument", new { id = archiveDocument.Id }, archiveDocument);
        }

        /// <summary>
        /// تحديث مستند أرشيف
        /// </summary>
        /// <param name="id">معرف مستند الأرشيف</param>
        /// <param name="archiveDocument">بيانات مستند الأرشيف المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث مستند الأرشيف بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="404">مستند الأرشيف غير موجود</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutArchiveDocument(int id, ArchiveDocument archiveDocument)
        {
            if (id != archiveDocument.Id)
            {
                return BadRequest();
            }

            var existingDocument = await _context.ArchiveDocuments.FindAsync(id);
            if (existingDocument == null || existingDocument.IsDeleted)
            {
                return NotFound();
            }

            // تحديث الحقول القابلة للتعديل
            existingDocument.Title = archiveDocument.Title;
            existingDocument.Description = archiveDocument.Description;
            existingDocument.CategoryId = archiveDocument.CategoryId;
            existingDocument.Metadata = archiveDocument.Metadata;
            existingDocument.Content = archiveDocument.Content;
            existingDocument.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ArchiveDocumentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// حذف مستند أرشيف (حذف منطقي)
        /// </summary>
        /// <param name="id">معرف مستند الأرشيف</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف مستند الأرشيف بنجاح</response>
        /// <response code="404">مستند الأرشيف غير موجود</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteArchiveDocument(int id)
        {
            var archiveDocument = await _context.ArchiveDocuments.FindAsync(id);
            if (archiveDocument == null || archiveDocument.IsDeleted)
            {
                return NotFound();
            }

            // حذف منطقي
            archiveDocument.IsDeleted = true;
            archiveDocument.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// البحث في مستندات الأرشيف
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة مستندات الأرشيف المطابقة</returns>
        /// <response code="200">إرجاع قائمة مستندات الأرشيف المطابقة</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveDocument>>> SearchDocuments([FromQuery] string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetArchiveDocuments();
            }

            return await _context.ArchiveDocuments
                .Include(ad => ad.Category)
                .Include(ad => ad.CreatedByNavigation)
                .Include(ad => ad.UploadedByNavigation)
                .Include(ad => ad.Tags)
                .Where(ad => !ad.IsDeleted && 
                    (ad.Title.Contains(searchTerm) || 
                     ad.Description.Contains(searchTerm) ||
                     ad.Content.Contains(searchTerm) ||
                     ad.FileName.Contains(searchTerm)))
                .OrderByDescending(ad => ad.CreatedAt)
                .ToListAsync();
        }

        private bool ArchiveDocumentExists(int id)
        {
            return _context.ArchiveDocuments.Any(e => e.Id == id && !e.IsDeleted);
        }
    }
}
