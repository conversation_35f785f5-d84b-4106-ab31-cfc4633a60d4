import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/task_status_enum.dart';
import 'package:flutter_application_2/models/chart_enums.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';

import '../../../utils/chart_color_schemes.dart';
import '../../../utils/chart_type_utils.dart';
import '../../../constants/app_colors.dart';
import '../../../models/dashboard_widget_model.dart';
import '../../../controllers/task_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../controllers/department_controller.dart';
import '../../../services/export_services/chart_export_service.dart';

import '../../../routes/app_routes.dart';

/// مكون المخطط القابل للسحب
///
/// يمثل مخططًا قابلًا للسحب والإفلات في لوحة المعلومات
class DraggableChartWidget extends StatefulWidget {
  /// عنصر لوحة المعلومات
  final DashboardWidget widget;

  /// دالة يتم استدعاؤها عند بدء السحب
  final Function(DashboardWidget widget)? onDragStarted;

  /// دالة يتم استدعاؤها عند إفلات العنصر
  final Function(DashboardWidget widget, Offset position)? onDragEnd;

  /// دالة يتم استدعاؤها عند النقر المزدوج
  final Function(DashboardWidget widget)? onDoubleTap;

  /// دالة يتم استدعاؤها عند تحديث الإعدادات
  final Function(DashboardWidget widget, Map<String, dynamic> settings)?
      onSettingsUpdated;

  const DraggableChartWidget({
    super.key,
    required this.widget,
    this.onDragStarted,
    this.onDragEnd,
    this.onDoubleTap,
    this.onSettingsUpdated,
  });

  @override
  State<DraggableChartWidget> createState() => _DraggableChartWidgetState();
}

class _DraggableChartWidgetState extends State<DraggableChartWidget> {
  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();
  final DepartmentController _departmentController =
      Get.find<DepartmentController>();
  final ChartExportService _chartExportService = Get.find<ChartExportService>();

  late Map<String, dynamic> _settings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void didUpdateWidget(DraggableChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // For now, we'll use a simple comparison since the widget structure is different
    _loadSettings();
  }

  /// تحميل إعدادات المخطط
  void _loadSettings() {
    setState(() {
      _isLoading = true;
    });

    try {
      // Use default settings for now since the widget structure is different
      _settings = {
        'chartType': 'pie',
        'viewMode': 'chart',
        'colorScheme': 'default',
        'chartSize': 200,
        'showValues': true,
        'showLabels': true,
        'showPercentages': true,
      };
    } catch (e) {
      _settings = {};
      debugPrint('خطأ في تحميل إعدادات المخطط: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض مربع حوار تفاصيل المخطط
  void _showChartDetailDialog() {
    if (widget.onDoubleTap != null) {
      widget.onDoubleTap!(widget.widget);
      return;
    }

    // TODO: Fix ChartDetailDialog to work with the correct DashboardWidget type
    Get.snackbar(
      'تفاصيل المخطط',
      'سيتم إضافة تفاصيل المخطط قريباً',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  /// عرض المخطط بملء الشاشة
  void _showFullscreenChartView() {
    // استخدام المسار المسمى بدلاً من الانتقال المباشر
    Get.toNamed(
      AppRoutes.fullscreenChartView,
      arguments: {
        'widget': widget.widget,
        'settings': _settings,
        'onSettingsUpdated': (Map<String, dynamic> updatedSettings) {
          setState(() {
            _settings = updatedSettings;
          });

          if (widget.onSettingsUpdated != null) {
            widget.onSettingsUpdated!(widget.widget, updatedSettings);
          }
        },
      },
    );
  }

  /// تصدير المخطط
  void _exportChart(String format) async {
    try {
      // إظهار مؤشر التحميل
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // الحصول على بيانات المخطط
      final data = await _getChartData();

      // تصدير المخطط
      String? filePath;
      switch (format) {
        case 'pdf':
          filePath = await _chartExportService.exportToPdf(
            'chart_${widget.widget.id}',
            title: widget.widget.title,
            data: data,
            chartType: _settings['chartType'] ?? 'pie',
            startDate: DateTime.now().subtract(const Duration(days: 30)),
            endDate: DateTime.now(),
          );
          break;
        case 'excel':
          filePath = await _chartExportService.exportToExcel(
            title: widget.widget.title,
            data: data,
            chartType: _settings['chartType'] ?? 'pie',
          );
          break;
        case 'csv':
          filePath = await _chartExportService.exportToCsv(
            title: widget.widget.title,
            data: data,
            chartType: _settings['chartType'] ?? 'pie',
          );
          break;
      }

      // إغلاق مؤشر التحميل
      Get.back();

      // إظهار رسالة نجاح
      if (filePath != null) {
        Get.snackbar(
          'تم التصدير بنجاح',
          'تم حفظ الملف في: $filePath',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 5),
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ في التصدير',
          'حدث خطأ أثناء تصدير المخطط',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ في التصدير',
        'حدث خطأ أثناء تصدير المخطط: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// الحصول على بيانات المخطط
  Future<Map<String, dynamic>> _getChartData() async {
    switch (widget.widget.type) {
      case DashboardWidgetType.taskStatusChart:
        return await _getTaskStatusChartData();
      case DashboardWidgetType.taskProgressChart:
        return await _getTaskProgressChartData();
      case DashboardWidgetType.userPerformanceChart:
        return await _getUserPerformanceChartData();
      case DashboardWidgetType.departmentPerformanceChart:
        return await _getDepartmentPerformanceChartData();
      default:
        return {};
    }
  }

  /// الحصول على بيانات مخطط حالة المهام
  Future<Map<String, dynamic>> _getTaskStatusChartData() async {
    try {
      // تحميل المهام
      await _taskController.loadAllTasks();

      // تجميع المهام حسب الحالة
      final tasks = _taskController.allTasks;
      final Map<String, int> statusCounts = {};

      // إذا لم تكن هناك مهام، إضافة بيانات افتراضية
      if (tasks.isEmpty) {
        return {
          'قيد الانتظار': 0.0,
          'قيد التنفيذ': 0.0,
          'بانتظار معلومات': 0.0,
          'مكتملة': 0.0,
        };
      }

      for (final task in tasks) {
        final status = TaskStatus.fromId(task.status).displayNameAr;
        statusCounts[status] = (statusCounts[status] ?? 0) + 1;
      }

      // تحويل البيانات إلى الصيغة المطلوبة
      final Map<String, double> data = {};
      statusCounts.forEach((key, value) {
        data[key] = value.toDouble();
      });

      return data;
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات مخطط حالة المهام: $e');
      // إرجاع بيانات افتراضية في حالة حدوث خطأ
      return {
        'قيد الانتظار': 0.0,
        'قيد التنفيذ': 0.0,
        'بانتظار معلومات': 0.0,
        'مكتملة': 0.0,
      };
    }
  }

  /// الحصول على بيانات مخطط تقدم المهام
  Future<Map<String, dynamic>> _getTaskProgressChartData() async {
    try {
      // تحميل المهام
      await _taskController.loadAllTasks();

      // تجميع المهام حسب التاريخ
      final tasks = _taskController.allTasks;
      final Map<DateTime, Map<String, int>> progressByDate = {};

      // إذا لم تكن هناك مهام، إضافة بيانات افتراضية
      if (tasks.isEmpty) {
        // إنشاء بيانات افتراضية لآخر 7 أيام
        final now = DateTime.now();
        for (int i = 6; i >= 0; i--) {
          final date = DateTime(now.year, now.month, now.day - i);
          progressByDate[date] = {
            'total': 0,
            'completed': 0,
          };
        }
      } else {
        for (final task in tasks) {
          final createdDate = DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000);
          final date = DateTime(
            createdDate.year,
            createdDate.month,
            createdDate.day,
          );

          if (!progressByDate.containsKey(date)) {
            progressByDate[date] = {
              'total': 0,
              'completed': 0,
            };
          }

          progressByDate[date]!['total'] =
              (progressByDate[date]!['total'] ?? 0) + 1;

          if (task.status == TaskStatus.completed.id) {
            progressByDate[date]!['completed'] =
                (progressByDate[date]!['completed'] ?? 0) + 1;
          }
        }
      }

      // تحويل البيانات إلى الصيغة المطلوبة
      final Map<String, List<Map<String, dynamic>>> data = {
        'series': [
          {
            'name': 'إجمالي المهام',
            'data': progressByDate.entries.map((entry) {
              return {
                'x': entry.key.millisecondsSinceEpoch,
                'y': entry.value['total'],
              };
            }).toList(),
          },
          {
            'name': 'المهام المكتملة',
            'data': progressByDate.entries.map((entry) {
              return {
                'x': entry.key.millisecondsSinceEpoch,
                'y': entry.value['completed'],
              };
            }).toList(),
          },
        ],
      };

      return data;
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات مخطط تقدم المهام: $e');
      // إرجاع بيانات افتراضية في حالة حدوث خطأ
      final now = DateTime.now();
      final Map<String, List<Map<String, dynamic>>> defaultData = {
        'series': [
          {
            'name': 'إجمالي المهام',
            'data': List.generate(7, (index) {
              final date = DateTime(now.year, now.month, now.day - (6 - index));
              return {
                'x': date.millisecondsSinceEpoch,
                'y': 0,
              };
            }),
          },
          {
            'name': 'المهام المكتملة',
            'data': List.generate(7, (index) {
              final date = DateTime(now.year, now.month, now.day - (6 - index));
              return {
                'x': date.millisecondsSinceEpoch,
                'y': 0,
              };
            }),
          },
        ],
      };
      return defaultData;
    }
  }

  /// الحصول على بيانات مخطط أداء المستخدمين
  Future<Map<String, dynamic>> _getUserPerformanceChartData() async {
    try {
      // تحميل المهام والمستخدمين
      await _taskController.loadAllTasks();
      await _userController.loadAllUsers();

      // تجميع المهام حسب المستخدم
      final tasks = _taskController.allTasks;
      final users = _userController.users;
      final Map<String, Map<String, int>> tasksByUser = {};

      // إذا لم تكن هناك مستخدمين، إرجاع بيانات افتراضية
      if (users.isEmpty) {
        return {
          'لا توجد بيانات': 0.0,
        };
      }

      for (final user in users) {
        tasksByUser[user.name] = {
          'total': 0,
          'completed': 0,
        };
      }

      for (final task in tasks) {
        final assigneeId = task.assigneeId;
        if (assigneeId != null) {
          final user = users.firstWhereOrNull((u) => u.id == assigneeId);
          if (user != null) {
            tasksByUser[user.name]!['total'] =
                (tasksByUser[user.name]!['total'] ?? 0) + 1;

            if (task.status == TaskStatus.completed.id) {
              tasksByUser[user.name]!['completed'] =
                  (tasksByUser[user.name]!['completed'] ?? 0) + 1;
            }
          }
        }
      }

      // تحويل البيانات إلى الصيغة المطلوبة
      final Map<String, double> completionRates = {};
      tasksByUser.forEach((userName, counts) {
        if (counts['total']! > 0) {
          completionRates[userName] =
              (counts['completed']! / counts['total']!) * 100;
        } else {
          completionRates[userName] = 0;
        }
      });

      // إذا لم تكن هناك بيانات، إضافة بيانات افتراضية
      if (completionRates.isEmpty) {
        return {
          'لا توجد بيانات': 0.0,
        };
      }

      return completionRates;
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات مخطط أداء المستخدمين: $e');
      // إرجاع بيانات افتراضية في حالة حدوث خطأ
      return {
        'مستخدم 1': 0.0,
        'مستخدم 2': 0.0,
        'مستخدم 3': 0.0,
      };
    }
  }

  /// الحصول على بيانات مخطط أداء الأقسام
  Future<Map<String, dynamic>> _getDepartmentPerformanceChartData() async {
    try {
      // تحميل المهام والأقسام
      await _taskController.loadAllTasks();
      await _departmentController.loadAllDepartments();

      // تجميع المهام حسب القسم
      final tasks = _taskController.allTasks;
      final departments = _departmentController.allDepartments;
      final Map<String, Map<String, int>> tasksByDepartment = {};

      // إذا لم تكن هناك أقسام، إرجاع بيانات افتراضية
      if (departments.isEmpty) {
        return {
          'لا توجد بيانات': 0.0,
        };
      }

      for (final department in departments) {
        tasksByDepartment[department.name] = {
          'total': 0,
          'completed': 0,
        };
      }

      for (final task in tasks) {
        final departmentId = task.departmentId;
        final department =
            departments.firstWhereOrNull((d) => d.id == departmentId);
        if (department != null) {
          tasksByDepartment[department.name]!['total'] =
              (tasksByDepartment[department.name]!['total'] ?? 0) + 1;

          if (task.status == TaskStatus.completed.id) {
            tasksByDepartment[department.name]!['completed'] =
                (tasksByDepartment[department.name]!['completed'] ?? 0) + 1;
          }
        }
      }

      // تحويل البيانات إلى الصيغة المطلوبة
      final Map<String, double> completionRates = {};
      tasksByDepartment.forEach((departmentName, counts) {
        if (counts['total']! > 0) {
          completionRates[departmentName] =
              (counts['completed']! / counts['total']!) * 100;
        } else {
          completionRates[departmentName] = 0;
        }
      });

      // إذا لم تكن هناك بيانات، إضافة بيانات افتراضية
      if (completionRates.isEmpty) {
        return {
          'لا توجد بيانات': 0.0,
        };
      }

      return completionRates;
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات مخطط أداء الأقسام: $e');
      // إرجاع بيانات افتراضية في حالة حدوث خطأ
      return {
        'قسم 1': 0.0,
        'قسم 2': 0.0,
        'قسم 3': 0.0,
      };
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // تحديد ألوان حسب السمة - مستوحاة من Monday.com
    final Color mondayBlue = const Color(0xFF00A9FF);
    final Color cardColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF292F4C)
        : Colors.white;
    final Color textColor = Theme.of(context).brightness == Brightness.dark
        ? Colors.white
        : const Color(0xFF323338);

    return Draggable<DashboardWidget>(
      // بيانات العنصر
      data: widget.widget,

      // العنصر أثناء السحب
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: 300,
          height: 200,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: cardColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: mondayBlue,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0x33000000), // 0.2 opacity
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: const Color(0x1A00A9FF), // 0.1 opacity blue
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Icon(
                      _getChartTypeIcon(),
                      color: mondayBlue,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.widget.title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: textColor,
                      ),
                    ),
                  ),
                ],
              ),
              const Expanded(
                child: Center(
                  child: Text(
                    "سحب العنصر",
                    style: TextStyle(
                      color: Color(0xFF00A9FF),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),

      // العنصر عندما يكون قيد السحب
      childWhenDragging: Opacity(
        opacity: 0.3,
        child: _buildChartCard(),
      ),

      // تأثير الرسوم المتحركة عند بدء السحب
      onDragStarted: () {
        HapticFeedback.lightImpact(); // اهتزاز خفيف عند بدء السحب
        if (widget.onDragStarted != null) {
          widget.onDragStarted!(widget.widget);
        }
      },

      // تأثير الرسوم المتحركة عند انتهاء السحب
      onDragEnd: (details) {
        if (details.wasAccepted) {
          HapticFeedback.mediumImpact(); // اهتزاز متوسط عند قبول الإفلات
        }
        if (widget.onDragEnd != null) {
          widget.onDragEnd!(widget.widget, details.offset);
        }
      },

      // العنصر الأصلي
      child: GestureDetector(
        onDoubleTap: _showChartDetailDialog,
        child: _buildChartCard(),
      ),
    );
  }

  /// بناء بطاقة المخطط
  Widget _buildChartCard() {
    // حالة عرض المخطط (مخطط/جدول/كلاهما)
    final viewMode = _settings['viewMode'] ?? 'chart';

    // تحديد ألوان حسب السمة - مستوحاة من Monday.com
    final Color cardColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF292F4C)
        : Colors.white;

    final Color headerColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF2E3559)
        : const Color(0xFFF6F7FB);

    final Color borderColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF363D63)
        : const Color(0xFFE6E9EF);

    final Color textColor = Theme.of(context).brightness == Brightness.dark
        ? Colors.white
        : const Color(0xFF323338);

    final Color iconColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFFD0D4E4)
        : const Color(0xFF676879);

    // تحديد لون المخطط حسب نوعه
    Color chartColor;
    switch (widget.widget.type) {
      case DashboardWidgetType.taskStatusChart:
        chartColor = const Color(0xFF00A9FF); // أزرق Monday.com
        break;
      case DashboardWidgetType.taskProgressChart:
        chartColor = const Color(0xFF00C875); // أخضر Monday.com
        break;
      case DashboardWidgetType.userPerformanceChart:
        chartColor = const Color(0xFFFFCB00); // أصفر Monday.com
        break;
      case DashboardWidgetType.departmentPerformanceChart:
        chartColor = const Color(0xFFFF7575); // أحمر Monday.com
        break;
      default:
        chartColor = const Color(0xFF00A9FF); // أزرق Monday.com
    }

    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: const Color(0x0D000000), // 0.05 opacity
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // عنوان المخطط وأيقونات التحكم
          Container(
            decoration: BoxDecoration(
              color: headerColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(7),
                topRight: Radius.circular(7),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: Row(
              children: [
                // أيقونة نوع المخطط
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: chartColor.withAlpha(38), // 0.15 opacity
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    _getChartTypeIcon(),
                    color: chartColor,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 10),

                // عنوان المخطط
                Expanded(
                  child: Text(
                    widget.widget.title,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: textColor,
                    ),
                  ),
                ),

                // أيقونات التحكم
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // أيقونة تغيير طريقة العرض
                    _buildViewModeToggle(viewMode, iconColor),

                    // أيقونة تغيير نوع المخطط
                    _buildChartTypeToggle(iconColor),

                    // أيقونة تخصيص المخطط (ملء الشاشة)
                    IconButton(
                      icon: Icon(
                        Icons.fullscreen,
                        size: 16,
                        color: iconColor,
                      ),
                      tooltip: 'تخصيص المخطط',
                      onPressed: _showFullscreenChartView,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      style: IconButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),

                    // أيقونة الإعدادات
                    IconButton(
                      icon: Icon(
                        Icons.settings_outlined,
                        size: 16,
                        color: iconColor,
                      ),
                      tooltip: 'إعدادات المخطط',
                      onPressed: _showChartDetailDialog,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      style: IconButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),

                    // قائمة التصدير
                    PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_horiz,
                        size: 16,
                        color: iconColor,
                      ),
                      tooltip: 'المزيد من الخيارات',
                      position: PopupMenuPosition.under,
                      offset: const Offset(0, 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      onSelected: (value) {
                        if (value.startsWith('export_')) {
                          _exportChart(value.split('_')[1]);
                        } else if (value == 'settings') {
                          _showChartDetailDialog();
                        } else if (value == 'fullscreen') {
                          _showFullscreenChartView();
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'fullscreen',
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: const Color(0x1A00A9FF), // 0.1 opacity
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Icon(Icons.fullscreen,
                                    color: Color(0xFF00A9FF), size: 16),
                              ),
                              const SizedBox(width: 10),
                              const Text('عرض بملء الشاشة',
                                  style: TextStyle(fontSize: 13)),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'export_pdf',
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: const Color(0x1AE2445C), // 0.1 opacity
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Icon(Icons.picture_as_pdf,
                                    color: Color(0xFFE2445C), size: 16),
                              ),
                              const SizedBox(width: 10),
                              const Text('تصدير كـ PDF',
                                  style: TextStyle(fontSize: 13)),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'export_excel',
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: const Color(0x1A00C875), // 0.1 opacity
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Icon(Icons.table_chart,
                                    color: Color(0xFF00C875), size: 16),
                              ),
                              const SizedBox(width: 10),
                              const Text('تصدير كـ Excel',
                                  style: TextStyle(fontSize: 13)),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'export_csv',
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: const Color(0x1A00A9FF), // 0.1 opacity
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Icon(Icons.description,
                                    color: Color(0xFF00A9FF), size: 16),
                              ),
                              const SizedBox(width: 10),
                              const Text('تصدير كـ CSV',
                                  style: TextStyle(fontSize: 13)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),

          // محتوى المخطط
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12.0),
              child: _buildChartContentByViewMode(viewMode),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة نوع المخطط
  IconData _getChartTypeIcon() {
    // تحويل نوع العنصر إلى نوع مخطط مناسب
    ChartType chartType;
    switch (widget.widget.type) {
      case DashboardWidgetType.taskStatusChart:
        chartType = ChartType.pie;
        break;
      case DashboardWidgetType.taskProgressChart:
        chartType = ChartType.line;
        break;
      case DashboardWidgetType.userPerformanceChart:
        return Icons.person; // أيقونة خاصة للمستخدمين
      case DashboardWidgetType.departmentPerformanceChart:
        return Icons.groups; // أيقونة خاصة للأقسام
      default:
        chartType = ChartType.bar;
    }

    // استخدام الدالة المشتركة للحصول على الأيقونة
    return ChartTypeUtils.getChartTypeIcon(chartType);
  }

  /// بناء أيقونة تغيير طريقة العرض
  Widget _buildViewModeToggle(String currentViewMode,
      [Color? customIconColor]) {
    // لا نحتاج إلى تحديد لون الأيقونة هنا لأننا نستخدم ألوان محددة لكل نوع عرض

    return PopupMenuButton<String>(
      icon: _getViewModeIcon(currentViewMode),
      tooltip: 'تغيير طريقة العرض',
      position: PopupMenuPosition.under,
      offset: const Offset(0, 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(
        minWidth: 32,
        minHeight: 32,
      ),
      onSelected: (String newViewMode) {
        setState(() {
          _settings['viewMode'] = newViewMode;
        });

        // حفظ الإعدادات
        if (widget.onSettingsUpdated != null) {
          widget.onSettingsUpdated!(widget.widget, _settings);
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'chart',
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: const Color(0x1A784BD1), // 0.1 opacity
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(Icons.pie_chart,
                    color: Color(0xFF784BD1), size: 16),
              ),
              const SizedBox(width: 10),
              const Text('عرض المخطط فقط', style: TextStyle(fontSize: 13)),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'table',
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: const Color(0x1A00A9FF), // 0.1 opacity
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(Icons.table_chart,
                    color: Color(0xFF00A9FF), size: 16),
              ),
              const SizedBox(width: 10),
              const Text('عرض الجدول فقط', style: TextStyle(fontSize: 13)),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'both',
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: const Color(0x1A00C875), // 0.1 opacity
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(Icons.dashboard,
                    color: Color(0xFF00C875), size: 16),
              ),
              const SizedBox(width: 10),
              const Text('عرض المخطط والجدول', style: TextStyle(fontSize: 13)),
            ],
          ),
        ),
      ],
    );
  }

  /// الحصول على أيقونة طريقة العرض
  Widget _getViewModeIcon(String viewMode) {
    IconData iconData;
    Color iconColor;

    switch (viewMode) {
      case 'chart':
        iconData = Icons.pie_chart;
        iconColor = const Color(0xFF784BD1); // بنفسجي Monday.com
        break;
      case 'table':
        iconData = Icons.table_chart;
        iconColor = const Color(0xFF00A9FF); // أزرق Monday.com
        break;
      case 'both':
        iconData = Icons.dashboard;
        iconColor = const Color(0xFF00C875); // أخضر Monday.com
        break;
      default:
        iconData = Icons.pie_chart;
        iconColor = const Color(0xFF784BD1); // بنفسجي Monday.com
    }

    return Icon(
      iconData,
      size: 16,
      color: iconColor,
    );
  }

  /// بناء أيقونة تغيير نوع المخطط
  Widget _buildChartTypeToggle([Color? customIconColor]) {
    // الحصول على نوع المخطط الحالي
    final String chartTypeStr = _settings['chartType'] ?? 'pie';
    final ChartType currentChartType = _getChartTypeFromString(chartTypeStr);

    // تحديد أنواع المخططات المتاحة حسب نوع المخطط
    List<ChartType> availableTypes = _getAvailableChartTypes();

    return IconButton(
      icon: Icon(
        Icons.bar_chart,
        size: 16,
        color: customIconColor ?? const Color(0xFF676879),
      ),
      tooltip: 'تغيير نوع المخطط',
      onPressed: () {
        // عرض مربع حوار لاختيار نوع المخطط
        _showChartTypeDialog(currentChartType, availableTypes);
      },
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(
        minWidth: 32,
        minHeight: 32,
      ),
      style: IconButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }

  /// عرض مربع حوار اختيار نوع المخطط
  void _showChartTypeDialog(
      ChartType currentType, List<ChartType> availableTypes) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر نوع المخطط'),
        content: SizedBox(
          width: 300,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: availableTypes.map((type) {
                final isSelected = currentType == type;
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2.0),
                  child: ChoiceChip(
                    label: Icon(
                      ChartTypeUtils.getChartTypeIcon(type),
                      size: 24,
                      color: isSelected ? Colors.white : Colors.grey.shade700,
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        // تحويل نوع المخطط إلى سلسلة نصية وحفظه في الإعدادات
                        setState(() {
                          _settings['chartType'] =
                              _getStringFromChartType(type);
                        });

                        // حفظ الإعدادات
                        if (widget.onSettingsUpdated != null) {
                          widget.onSettingsUpdated!(widget.widget, _settings);
                        }

                        // إغلاق مربع الحوار
                        Navigator.of(context).pop();
                      }
                    },
                    backgroundColor: Colors.grey.shade200,
                    selectedColor: AppColors.primary,
                    tooltip: ChartTypeUtils.getChartTypeLabel(type),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// الحصول على أنواع المخططات المتاحة حسب نوع المخطط
  List<ChartType> _getAvailableChartTypes() {
    String title = "";

    // تحويل نوع العنصر إلى عنوان مناسب
    switch (widget.widget.type) {
      case DashboardWidgetType.taskStatusChart:
        title = "توزيع المهام حسب الحالة";
        break;
      case DashboardWidgetType.taskProgressChart:
        title = "المهام حسب الشهر";
        break;
      case DashboardWidgetType.userPerformanceChart:
        title = "تحليل أداء المستخدمين";
        break;
      case DashboardWidgetType.departmentPerformanceChart:
        title = "تحليل أداء الأقسام";
        break;
      default:
        title = widget.widget.title;
    }

    return ChartTypeUtils.getAvailableChartTypes(title);
  }

  /// تحويل نوع المخطط من سلسلة نصية إلى ChartType
  ChartType _getChartTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'pie':
        return ChartType.pie;
      case 'donut':
        return ChartType.donut;
      case 'bar':
        return ChartType.bar;
      case 'line':
        return ChartType.line;
      case 'area':
        return ChartType.area;
      case 'scatter':
        return ChartType.scatter;
      case 'radar':
        return ChartType.radar;
      case 'gauge':
        return ChartType.gauge;
      default:
        return ChartType.pie;
    }
  }

  /// تحويل نوع المخطط من ChartType إلى سلسلة نصية
  String _getStringFromChartType(ChartType type) {
    switch (type) {
      case ChartType.pie:
        return 'pie';
      case ChartType.donut:
        return 'donut';
      case ChartType.bar:
        return 'bar';
      case ChartType.line:
        return 'line';
      case ChartType.area:
        return 'area';
      case ChartType.scatter:
        return 'scatter';
      case ChartType.radar:
        return 'radar';
      case ChartType.gauge:
        return 'gauge';
      default:
        return 'pie';
    }
  }

  /// بناء محتوى المخطط حسب طريقة العرض
  Widget _buildChartContentByViewMode(String viewMode) {
    // إضافة معالجة الأخطاء
    try {
      switch (viewMode) {
        case 'chart':
          return _buildChartContent();
        case 'table':
          return _buildTableContent();
        case 'both':
          return Column(
            children: [
              Expanded(
                flex: 3,
                child: _buildChartContent(),
              ),
              const Divider(height: 1),
              Expanded(
                flex: 2,
                child: _buildTableContent(),
              ),
            ],
          );
        default:
          return _buildChartContent();
      }
    } catch (e) {
      debugPrint('خطأ في بناء محتوى المخطط: $e');
      // عرض رسالة خطأ
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ أثناء عرض المخطط: $e',
              style: const TextStyle(
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  // إعادة تحميل البيانات
                  _loadSettings();
                });
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }
  }

  /// بناء محتوى الجدول
  Widget _buildTableContent() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getChartData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('خطأ: ${snapshot.error}'),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Text('لا توجد بيانات'),
          );
        }

        // تحويل البيانات إلى جدول
        return _buildDataTable(snapshot.data!);
      },
    );
  }

  /// بناء جدول البيانات
  Widget _buildDataTable(Map<String, dynamic> data) {
    // تحويل البيانات حسب نوع المخطط
    List<DataRow> rows = [];
    List<DataColumn> columns = [];

    switch (widget.widget.type) {
      case DashboardWidgetType.taskStatusChart:
      case DashboardWidgetType.userPerformanceChart:
      case DashboardWidgetType.departmentPerformanceChart:
        // إعداد الأعمدة
        columns = [
          const DataColumn(label: Text('الاسم')),
          const DataColumn(label: Text('القيمة')),
          const DataColumn(label: Text('النسبة')),
        ];

        // حساب المجموع
        double total = 0;
        data.forEach((key, value) {
          total += value.toDouble();
        });

        // إنشاء الصفوف
        data.forEach((key, value) {
          final percentage =
              total > 0 ? (value / total * 100).toStringAsFixed(1) : '0';
          rows.add(DataRow(
            cells: [
              DataCell(Text(key)),
              DataCell(Text(value.toString())),
              DataCell(Text('$percentage%')),
            ],
          ));
        });
        break;

      case DashboardWidgetType.taskProgressChart:
        if (data.containsKey('series')) {
          // إعداد الأعمدة
          columns = [
            const DataColumn(label: Text('التاريخ')),
          ];

          // إضافة أعمدة للسلاسل
          for (final series in data['series']) {
            columns.add(DataColumn(label: Text(series['name'])));
          }

          // تجميع البيانات حسب التاريخ
          Map<DateTime, Map<String, dynamic>> dateData = {};

          for (final series in data['series']) {
            final seriesName = series['name'];
            final List<Map<String, dynamic>> points = series['data'];

            for (final point in points) {
              final date = DateTime.fromMillisecondsSinceEpoch(point['x']);
              final value = point['y'];

              if (!dateData.containsKey(date)) {
                dateData[date] = {};
              }

              dateData[date]![seriesName] = value;
            }
          }

          // إنشاء الصفوف
          dateData.forEach((date, values) {
            final cells = <DataCell>[];
            cells.add(DataCell(Text('${date.day}/${date.month}/${date.year}')));

            for (final series in data['series']) {
              final seriesName = series['name'];
              cells.add(DataCell(Text('${values[seriesName] ?? 0}')));
            }

            rows.add(DataRow(cells: cells));
          });
        }
        break;

      default:
        return const Center(
          child: Text('نوع مخطط غير مدعوم للعرض كجدول'),
        );
    }

    return SingleChildScrollView(
      child: DataTable(
        columns: columns,
        rows: rows,
        columnSpacing: 16,
        headingRowHeight: 40,
        dataRowMinHeight: 36,
        dataRowMaxHeight: 36,
        horizontalMargin: 8,
      ),
    );
  }

  /// بناء محتوى المخطط
  Widget _buildChartContent() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getChartData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError) {
          debugPrint('خطأ في تحميل بيانات المخطط: ${snapshot.error}');
          // عرض رسالة خطأ مع زر إعادة المحاولة
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  'حدث خطأ أثناء تحميل البيانات: ${snapshot.error}',
                  style: const TextStyle(
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      // إعادة تحميل البيانات
                    });
                  },
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          // عرض رسالة عدم وجود بيانات مع رسم بياني فارغ
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.bar_chart,
                  color: Colors.grey,
                  size: 48,
                ),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد بيانات متاحة',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'أضف بعض المهام لعرض البيانات هنا',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        try {
          // عرض المخطط المناسب حسب النوع
          switch (widget.widget.type) {
            case DashboardWidgetType.taskStatusChart:
              return _buildPieChart(snapshot.data!);
            case DashboardWidgetType.taskProgressChart:
              return _buildLineChart(snapshot.data!);
            case DashboardWidgetType.userPerformanceChart:
            case DashboardWidgetType.departmentPerformanceChart:
              return _buildBarChart(snapshot.data!);
            default:
              return const Center(
                child: Text('نوع مخطط غير مدعوم'),
              );
          }
        } catch (e) {
          debugPrint('خطأ في بناء المخطط: $e');
          // عرض رسالة خطأ
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.orange,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  'حدث خطأ أثناء عرض المخطط: $e',
                  style: const TextStyle(
                    color: Colors.orange,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
      },
    );
  }

  /// بناء مخطط دائري
  Widget _buildPieChart(Map<String, dynamic> data) {
    // TODO: تحويل إلى Syncfusion Charts
    return const Center(
      child: Text(
        'مخطط دائري - قيد التطوير\nسيتم تحويله إلى Syncfusion Charts',
        textAlign: TextAlign.center,
      ),
    );
  }

  // تم حذف جميع الدوال التي تستخدم fl_chart

  /// بناء مخطط خطي
  Widget _buildLineChart(Map<String, dynamic> data) {
    // TODO: تحويل إلى Syncfusion Charts
    return const Center(
      child: Text(
        'مخطط خطي - قيد التطوير\nسيتم تحويله إلى Syncfusion Charts',
        textAlign: TextAlign.center,
      ),
    );
  }

  /// بناء مخطط شريطي
  Widget _buildBarChart(Map<String, dynamic> data) {
    // TODO: تحويل إلى Syncfusion Charts
    return const Center(
      child: Text(
        'مخطط شريطي - قيد التطوير\nسيتم تحويله إلى Syncfusion Charts',
        textAlign: TextAlign.center,
      ),
    );
  }
}
