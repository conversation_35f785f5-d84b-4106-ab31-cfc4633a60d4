import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // إضافة استيراد للاهتزاز - Add import for haptic feedback
import 'package:get/get.dart'; // إضافة استيراد للوصول إلى GetX
import '../../models/task_model.dart';
import '../../models/task_status_enum.dart'; // إضافة استيراد نموذج حالة المهمة

import '../../controllers/task_controller.dart'; // إضافة استيراد للتحكم في المهام
import '../../controllers/user_controller.dart'; // إضافة استيراد للتحكم في المستخدمين

/// A simplified drag and drop board implementation that doesn't rely on the drag_and_drop_lists package
class SimpleDragDropBoard extends StatefulWidget {
  final List<Task> pendingTasks;
  final List<Task> inProgressTasks;
  final List<Task> waitingTasks;
  final List<Task> completedTasks;
  final Function(Task task, TaskStatus newStatus) onTaskStatusChanged;
  final Function(Task task) onTaskTap;
  final VoidCallback onAddTask;
  final Function(TaskStatus status, List<Task> reorderedTasks)? onTaskReordered;

  const SimpleDragDropBoard({
    super.key,
    required this.pendingTasks,
    required this.inProgressTasks,
    required this.waitingTasks,
    required this.completedTasks,
    required this.onTaskStatusChanged,
    required this.onTaskTap,
    required this.onAddTask,
    this.onTaskReordered,
  });

  @override
  State<SimpleDragDropBoard> createState() => _SimpleDragDropBoardState();
}

class _SimpleDragDropBoardState extends State<SimpleDragDropBoard> {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      // تحديد الارتفاع المتاح للوحة
      // Determine available height for the board
      final availableHeight = constraints.maxHeight;

      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: SizedBox(
          // تحديد ارتفاع ثابت للصف بناءً على المساحة المتاحة
          // Set fixed height for the row based on available space
          height: availableHeight,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTaskColumn(
                'قيد الانتظار',
                widget.pendingTasks,
                Colors.grey,
                TaskStatus.pending,
              ),
              _buildTaskColumn(
                'قيد التنفيذ',
                widget.inProgressTasks,
                Colors.blue,
                TaskStatus.inProgress,
              ),
              _buildTaskColumn(
                'في انتظار معلومات',
                widget.waitingTasks,
                Colors.orange,
                TaskStatus.waitingForInfo,
              ),
              _buildTaskColumn(
                'مكتملة',
                widget.completedTasks,
                Colors.green,
                TaskStatus.completed,
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildTaskColumn(
    String title,
    List<Task> tasks,
    Color color,
    TaskStatus status,
  ) {
    // استخدام DragTarget لقبول المهام المسحوبة وتغيير حالتها
    // Use DragTarget to accept dragged tasks and change their status
    return DragTarget<Task>(
      builder: (context, candidateData, rejectedData) {
        // تغيير لون الخلفية عند سحب مهمة فوق العمود
        // Change background color when dragging a task over the column
        final isHovering = candidateData.isNotEmpty;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 280,
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isHovering ? color.withAlpha(15) : Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: isHovering
                    ? color.withAlpha(100)
                    : Colors.black.withAlpha(20),
                blurRadius: isHovering ? 12 : 8,
                spreadRadius: isHovering ? 2 : 0,
              ),
            ],
            border: Border.all(
              color: isHovering ? color : Colors.grey.shade200,
              width: isHovering ? 2 : 1,
            ),
          ),
          // إضافة تأثير وميض للحدود عند السحب فوق العمود
          foregroundDecoration: isHovering
              ? BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: color,
                    width: 2.5,
                  ),
                  // إضافة تأثير وميض للحدود
                  boxShadow: [
                    BoxShadow(
                      color: color.withAlpha(150),
                      blurRadius: 8,
                      spreadRadius: 0,
                    ),
                  ],
                )
              : null,
          // استخدام LayoutBuilder للحصول على القيود المتاحة
          // Use LayoutBuilder to get available constraints
          child: LayoutBuilder(
            builder: (context, constraints) {
              // حساب الارتفاع المتاح بعد طرح ارتفاع الهيدر والفوتر
              // Calculate available height after subtracting header and footer heights
              final headerHeight =
                  47.0; // تقدير ارتفاع الهيدر - Estimated header height
              final footerHeight =
                  44.0; // تقدير ارتفاع الفوتر - Estimated footer height
              final availableHeight =
                  constraints.maxHeight - headerHeight - footerHeight;

              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  Container(
                    decoration: BoxDecoration(
                      color: isHovering
                          ? color.withAlpha(80)
                          : color.withAlpha(50),
                      borderRadius:
                          const BorderRadius.vertical(top: Radius.circular(8)),
                    ),
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '$title (${tasks.length})',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: color,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Task list - استخدام Expanded لضمان عدم تجاوز المساحة المتاحة
                  // Use Expanded to ensure it doesn't exceed available space
                  Flexible(
                    child: Container(
                      constraints: BoxConstraints(
                        minHeight: 50,
                        maxHeight: availableHeight > 0 ? availableHeight : 300,
                      ),
                      // إضافة تأثير بصري عند السحب فوق العمود
                      // Add visual effect when dragging over the column
                      decoration: BoxDecoration(
                        color: isHovering ? color.withAlpha(30) : null,
                        borderRadius: BorderRadius.circular(8),
                        border: isHovering
                            ? Border.all(
                                color: color.withAlpha(180),
                                width: 2,
                                style: BorderStyle.solid,
                              )
                            : null,
                        // إضافة تأثير وميض للحدود عند السحب
                        boxShadow: isHovering
                            ? [
                                BoxShadow(
                                  color: color.withAlpha(100),
                                  blurRadius: 6,
                                  spreadRadius: 1,
                                ),
                              ]
                            : null,
                      ),
                      child: ListView.builder(
                        shrinkWrap: true,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        itemCount: tasks.length,
                        physics: const AlwaysScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          final task = tasks[index];
                          return _buildDraggableTaskItem(task, status);
                        },
                      ),
                    ),
                  ),

                  // Footer - Add task button
                  InkWell(
                    onTap: widget.onAddTask,
                    child: Container(
                      decoration: BoxDecoration(
                        color: isHovering
                            ? color.withAlpha(30)
                            : Colors.grey.shade100,
                        borderRadius: const BorderRadius.vertical(
                            bottom: Radius.circular(8)),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      child: Row(
                        children: [
                          Icon(Icons.add,
                              size: 16, color: Colors.grey.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'إضافة مهمة',
                            style: TextStyle(
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
      // قبول المهام فقط إذا كانت الحالة مختلفة عن الحالة الحالية
      // Accept tasks only if the status is different from the current status
      onWillAcceptWithDetails: (details) {
        // لا نقبل المهمة إذا كانت بنفس الحالة
        // Don't accept the task if it has the same status
        final task = details.data;
        return task.status != status.id;
      },
      // عند قبول المهمة، نقوم بتغيير حالتها
      // When accepting the task, change its status
      onAcceptWithDetails: (details) {
        // استدعاء الدالة لتغيير حالة المهمة
        // Call the function to change the task status
        final task = details.data;

        // إضافة تأثير اهتزاز عند قبول المهمة
        // Add haptic feedback when accepting the task
        HapticFeedback.mediumImpact();

        // عرض مؤشر تحميل
        // Show loading indicator
        // Get.dialog(
        //   const Center(
        //     child: CircularProgressIndicator(),
        //   ),
        //   barrierDismissible: false,
        // );

        // إضافة تأخير قصير قبل استدعاء الدالة لتحسين تجربة المستخدم
        // Add a short delay before calling the function to improve user experience
        Future.delayed(const Duration(milliseconds: 300), () async {
          // تغيير حالة المهمة
          // Change task status
          await widget.onTaskStatusChanged(task, status);

          // إغلاق مؤشر التحميل
          // Close loading indicator
          Get.back();

          // عرض رسالة نجاح
          // Show success message
          Get.snackbar(
            'تم بنجاح',
            'تم تغيير حالة المهمة',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green.shade100,
            colorText: Colors.green.shade800,
            duration: const Duration(seconds: 2),
          );
        });
      },
    );
  }

  Widget _buildDraggableTaskItem(Task task, TaskStatus currentStatus) {
    // تحسين مظهر المهمة عند سحبها
    // Improve the appearance of the task when dragging
    return Draggable<Task>(
      data: task,
      // تحسين مظهر المهمة أثناء السحب
      // Improve the appearance of the task during dragging
      feedback: Material(
        elevation: 12,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 260, // عرض مناسب - Appropriate width
          padding: const EdgeInsets.all(12), // حشو مناسب - Appropriate padding
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getStatusColor(TaskStatus.fromId(task.status)),
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: _getStatusColor(TaskStatus.fromId(task.status)).withAlpha(150),
                blurRadius: 12,
                spreadRadius: 2,
                offset: const Offset(0, 4),
              ),
              // إضافة ظل ثانوي لتأثير أكثر عمقاً
              BoxShadow(
                color: Colors.black.withAlpha(50),
                blurRadius: 8,
                spreadRadius: 1,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          // إضافة تأثير تدوير خفيف للمهمة أثناء السحب
          transform: Matrix4.rotationZ(0.02),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getPriorityColor(TaskPriority.fromLevel(task.priority)),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      task.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              if (task.description?.isNotEmpty == true) ...[
                const SizedBox(height: 4),
                Text(
                  task.description!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade700,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
      // تقليل شفافية المهمة الأصلية أثناء السحب
      // Reduce the opacity of the original task during dragging
      childWhenDragging: Opacity(
        opacity: 0.3,
        child: _buildTaskCard(task),
      ),
      // إضافة تأثير عند بدء السحب
      // Add effect when starting to drag
      onDragStarted: () {
        // إضافة تأثير اهتزاز خفيف عند بدء السحب
        // Add light haptic feedback when starting to drag
        HapticFeedback.lightImpact();
      },
      // إضافة تأثير عند انتهاء السحب
      // Add effect when drag ends
      onDragEnd: (details) {
        if (details.wasAccepted) {
          // تم قبول المهمة في مكان آخر - إضافة اهتزاز متوسط
          // Task was accepted elsewhere - add medium haptic feedback
          HapticFeedback.mediumImpact();
        } else {
          // لم يتم قبول المهمة - إضافة اهتزاز خطأ
          // Task was not accepted - add error haptic feedback
          HapticFeedback.vibrate();
        }
      },
      // المهمة الأصلية
      // Original task
      child: DragTarget<Task>(
        builder: (context, candidateData, rejectedData) {
          final isHovering = candidateData.isNotEmpty;
          // إضافة تأثير عند سحب مهمة فوق مهمة أخرى
          // Add effect when dragging a task over another task
          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: isHovering
                  ? Border.all(
                      color: Colors.blue,
                      width: 2.5,
                      style: BorderStyle.solid,
                    )
                  : null,
              boxShadow: isHovering
                  ? [
                      BoxShadow(
                        color: Colors.blue.withAlpha(120),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ]
                  : null,
            ),
            // إضافة تأثير تكبير خفيف عند السحب فوق المهمة
            clipBehavior: Clip.hardEdge,
            child: _buildTaskCard(task),
          );
        },
        onWillAcceptWithDetails: (details) {
          // قبول المهام فقط إذا كانت من نفس العمود
          // Accept tasks only if they are from the same column
          final incomingTask = details.data;
          return incomingTask.status == currentStatus.id &&
              incomingTask.id != task.id;
        },
        onAcceptWithDetails: (details) {
          // تنفيذ تبديل المهام هنا
          // Implement task swapping here
          final incomingTask = details.data;

          // إيجاد المهمتين في القائمة المناسبة
          List<Task> columnTasks = [];
          switch (currentStatus) {
            case TaskStatus.pending:
              columnTasks = widget.pendingTasks;
              break;
            case TaskStatus.inProgress:
              columnTasks = widget.inProgressTasks;
              break;
            case TaskStatus.waitingForInfo:
              columnTasks = widget.waitingTasks;
              break;
            case TaskStatus.completed:
              columnTasks = widget.completedTasks;
              break;
            default:
              return;
          }

          // إيجاد مؤشرات المهام
          final incomingTaskIndex =
              columnTasks.indexWhere((t) => t.id == incomingTask.id);
          final targetTaskIndex =
              columnTasks.indexWhere((t) => t.id == task.id);

          if (incomingTaskIndex != -1 && targetTaskIndex != -1) {
            // تأثير اهتزاز عند تبديل المهام
            HapticFeedback.mediumImpact();

            // استدعاء دالة إعادة ترتيب المهام
            _reorderTasks(currentStatus, incomingTaskIndex, targetTaskIndex);
          }
        },
      ),
    );
  }

  Widget _buildTaskCard(Task task) {
    // الحصول على مرجع للتحكم في المهام
    final taskController = Get.find<TaskController>();
    // الحصول على مرجع للتحكم في المستخدمين
    final userController = Get.find<UserController>();

    return Card(
      margin: const EdgeInsets.symmetric(
          vertical: 2), // تقليل الهامش - Reduce margin
      child: InkWell(
        onTap: () => widget.onTaskTap(task),
        child: Padding(
          padding: const EdgeInsets.all(8), // تقليل الحشو - Reduce padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize
                .min, // استخدام الحد الأدنى من المساحة - Use minimum space
            children: [
              // الصف الأول: عنوان المهمة والأولوية والمهام الفرعية
              Row(
                children: [
                  // أيقونة الأولوية
                  Container(
                    width: 12, // زيادة الحجم قليلاً
                    height: 12, // زيادة الحجم قليلاً
                    decoration: BoxDecoration(
                      color: _getPriorityColor(TaskPriority.fromLevel(task.priority)),
                      borderRadius: BorderRadius.circular(2),
                      boxShadow: [
                        BoxShadow(
                          color:
                              _getPriorityColor(TaskPriority.fromLevel(task.priority)).withAlpha(128),
                          blurRadius: 2,
                          spreadRadius: 0.5,
                        ),
                      ],
                    ),
                    child: Center(
                      child: Icon(
                        _getPriorityIcon(TaskPriority.fromLevel(task.priority)),
                        size: 8,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 6), // تقليل المسافة - Reduce spacing
                  Expanded(
                    child: Text(
                      task.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 13, // تقليل حجم الخط - Reduce font size
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // عرض عدد المهام الفرعية
                  FutureBuilder<int>(
                    future: taskController.getSubtasksCount(task.id.toString()),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting ||
                          (snapshot.data ?? 0) == 0) {
                        return const SizedBox.shrink();
                      }

                      final subtasksCount = snapshot.data ?? 0;
                      return FutureBuilder<int>(
                        future:
                            taskController.getCompletedSubtasksCount(task.id.toString()),
                        builder: (context, completedSnapshot) {
                          final completedCount = completedSnapshot.data ?? 0;

                          return Container(
                            margin: const EdgeInsets.only(right: 4),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 4, vertical: 1),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: Colors.blue.shade200,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.checklist,
                                  size: 8,
                                  color: Colors.blue.shade700,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '$completedCount/$subtasksCount',
                                  style: TextStyle(
                                    fontSize: 8,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  ),
                ],
              ),

              // الصف الثاني: وصف المهمة
              if (task.description?.isNotEmpty == true) ...[
                const SizedBox(height: 4), // تقليل المسافة - Reduce spacing
                Text(
                  task.description!,
                  style: TextStyle(
                    fontSize: 11, // تقليل حجم الخط - Reduce font size
                    color: Colors.grey.shade700,
                  ),
                  maxLines: 1, // تقليل عدد الأسطر - Reduce number of lines
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              // الصف الثالث: منشئ المهمة
              const SizedBox(height: 4),
              Builder(
                builder: (context) {
                  final creator = userController.getUserById(task.creatorId);
                  if (creator == null) {
                    return const SizedBox(height: 12);
                  }

                  return Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 10,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 2),
                      Expanded(
                        child: Text(
                          creator.name,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  );
                },
              ),

              // الصف الرابع: المرفقات والمحادثات والتاريخ
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // تاريخ الاستحقاق
                  Flexible(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.calendar_today,
                          size: 10, // تقليل الحجم - Reduce size
                          color: Colors.grey,
                        ),
                        const SizedBox(
                            width: 2), // تقليل المسافة - Reduce spacing
                        Flexible(
                          child: Text(
                            task.dueDate != null
                                ? _formatDate(DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000))
                                : 'No due date',
                            style: const TextStyle(
                              fontSize: 10, // تقليل حجم الخط - Reduce font size
                              color: Colors.grey,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // المرفقات - سيتم إضافتها لاحقاً عندما تصبح متاحة في النموذج
                  // TODO: إضافة عرض المرفقات عندما تصبح متاحة في Task model

                  // المحادثات - سيتم إضافتها لاحقاً عندما تصبح متاحة
                  // TODO: إضافة عرض عدد الرسائل غير المقروءة عندما تصبح متاحة

                  // نسبة الإكمال
                  if (task.completionPercentage > 0)
                    Text(
                      '${task.completionPercentage.toInt()}%',
                      style: TextStyle(
                        fontSize: 10, // تقليل حجم الخط - Reduce font size
                        color: _getStatusColor(TaskStatus.fromId(task.status)),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.high:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getPriorityIcon(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Icons.arrow_downward;
      case TaskPriority.medium:
        return Icons.remove;
      case TaskPriority.high:
        return Icons.arrow_upward;
      default:
        return Icons.remove;
    }
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.waitingForInfo:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
      case TaskStatus.news:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// إعادة ترتيب المهام داخل نفس العمود
  /// @param status حالة المهام
  /// @param fromIndex المؤشر الأصلي للمهمة
  /// @param toIndex المؤشر الجديد للمهمة
  void _reorderTasks(TaskStatus status, int fromIndex, int toIndex) {
    // الحصول على قائمة المهام المناسبة
    List<Task> columnTasks = [];
    switch (status) {
      case TaskStatus.pending:
        columnTasks = widget.pendingTasks;
        break;
      case TaskStatus.inProgress:
        columnTasks = widget.inProgressTasks;
        break;
      case TaskStatus.waitingForInfo:
        columnTasks = widget.waitingTasks;
        break;
      case TaskStatus.completed:
        columnTasks = widget.completedTasks;
        break;
      default:
        return;
    }

    // التأكد من أن المؤشرات صالحة
    if (fromIndex < 0 ||
        fromIndex >= columnTasks.length ||
        toIndex < 0 ||
        toIndex >= columnTasks.length) {
      return;
    }

    // إعادة ترتيب المهام
    final task = columnTasks[fromIndex];

    // إنشاء نسخة من القائمة لتجنب تعديل القائمة الأصلية مباشرة
    final updatedTasks = List<Task>.from(columnTasks);

    // إزالة المهمة من موقعها الأصلي وإضافتها إلى الموقع الجديد
    updatedTasks.removeAt(fromIndex);
    updatedTasks.insert(toIndex, task);

    // استدعاء دالة إعادة ترتيب المهام في الكنترولر
    widget.onTaskReordered?.call(status, updatedTasks);
  }
}
