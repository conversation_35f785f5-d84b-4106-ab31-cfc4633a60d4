import 'dart:convert';
import 'package:flutter/material.dart';

import '../models/dashboard_models.dart' as api_model;
import '../models/dashboard_widget_model.dart' as ui_model;
import '../models/dashboard_model.dart' as simple_model;
import 'package:uuid/uuid.dart';

/// محول عناصر لوحة المعلومات
///
/// يستخدم لتحويل بين نماذج ASP.NET Core API ونماذج واجهة المستخدم
/// متوافق مع بنية الباك إند في webapi/Models/DashboardWidget.cs
class DashboardWidgetAdapter {
  /// تحويل من نموذج API إلى نموذج واجهة المستخدم
  /// متوافق مع ASP.NET Core API DashboardWidget model
  static ui_model.DashboardWidget convertToUIModel(
      api_model.DashboardWidget apiWidget) {
    // تحويل نوع العنصر
    final widgetType = _convertWidgetTypeFromString(apiWidget.type);

    // استخراج الإعدادات من JSON
    Map<String, dynamic> settingsMap = {};
    if (apiWidget.config != null) {
      try {
        settingsMap = jsonDecode(apiWidget.config!);
      } catch (e) {
        debugPrint('خطأ في تحليل إعدادات العنصر: $e');
      }
    }

    // تحديد مصدر البيانات والاستعلام حسب نوع العنصر
    final dataSource = 'api_database';
    String query = _generateQueryForWidgetType(apiWidget.type);

    // تحديد حقول المحاور حسب نوع العنصر
    final axisFields = _getAxisFieldsForWidgetType(apiWidget.type);

    // تحويل الموقع والحجم من API إلى UI
    final position = Offset(
      apiWidget.positionX.toDouble(),
      apiWidget.positionY.toDouble()
    );
    final size = Size(
      apiWidget.width.toDouble(),
      apiWidget.height.toDouble()
    );

    // إنشاء عنصر واجهة المستخدم
    return ui_model.DashboardWidget(
      id: apiWidget.id.toString(),
      dashboardId: apiWidget.dashboardId.toString(),
      title: apiWidget.title,
      type: widgetType,
      dataSource: dataSource,
      query: query,
      position: position,
      size: size,
      headerColor: _getHeaderColorFromSettings(settingsMap),
      xAxisField: axisFields['xAxisField'],
      xAxisLabel: axisFields['xAxisLabel'],
      yAxisField: axisFields['yAxisField'],
      yAxisLabel: axisFields['yAxisLabel'],
      labelField: axisFields['labelField'],
      valueField: axisFields['valueField'],
      orientation: ui_model.ChartOrientation.vertical,
      showValues: settingsMap['showValues'] ?? true,
      showLabels: settingsMap['showLabels'] ?? true,
      showGrid: settingsMap['showGrid'] ?? true,
      showLegend: settingsMap['showLegend'] ?? true,
      createdAt: DateTime.fromMillisecondsSinceEpoch(apiWidget.createdAt * 1000),
      updatedAt: apiWidget.updatedAt != null
          ? DateTime.fromMillisecondsSinceEpoch(apiWidget.updatedAt! * 1000)
          : DateTime.now(),
      createdById: 'api_user',
    );
  }

  /// تحويل من نموذج واجهة المستخدم إلى نموذج API
  static api_model.CreateDashboardWidgetRequest convertToAPIRequest(
      ui_model.DashboardWidget uiWidget) {
    // تحويل نوع العنصر إلى string
    final widgetTypeString = _convertWidgetTypeToString(uiWidget.type);

    // تحويل الإعدادات إلى JSON
    final config = jsonEncode({
      'showValues': uiWidget.showValues,
      'showLabels': uiWidget.showLabels,
      'showGrid': uiWidget.showGrid,
      'showLegend': uiWidget.showLegend,
      'headerColor': uiWidget.headerColor?.toARGB32().toRadixString(16),
      'orientation': uiWidget.orientation.toString(),
    });

    return api_model.CreateDashboardWidgetRequest(
      dashboardId: int.parse(uiWidget.dashboardId),
      type: widgetTypeString,
      title: uiWidget.title,
      config: config,
      positionX: uiWidget.position.dx.round(),
      positionY: uiWidget.position.dy.round(),
      width: uiWidget.size.width.round(),
      height: uiWidget.size.height.round(),
    );
  }

  /// تحويل قائمة من نماذج API إلى نماذج واجهة المستخدم
  static List<ui_model.DashboardWidget> convertListToUIModel(
      List<api_model.DashboardWidget> apiWidgets) {
    return apiWidgets.map((widget) => convertToUIModel(widget)).toList();
  }

  /// تحويل نوع العنصر من String إلى نموذج واجهة المستخدم
  static ui_model.DashboardWidgetType _convertWidgetTypeFromString(String type) {
    switch (type) {
      case 'taskStatusChart':
        return ui_model.DashboardWidgetType.pieChart;
      case 'taskProgressChart':
        return ui_model.DashboardWidgetType.lineChart;
      case 'userPerformanceChart':
        return ui_model.DashboardWidgetType.barChart;
      case 'departmentPerformanceChart':
        return ui_model.DashboardWidgetType.barChart;
      case 'timeTrackingChart':
        return ui_model.DashboardWidgetType.lineChart;
      case 'taskList':
        return ui_model.DashboardWidgetType.table;
      case 'kpi':
        return ui_model.DashboardWidgetType.kpiCard;
      default:
        return ui_model.DashboardWidgetType.custom;
    }
  }

  /// تحويل نوع العنصر من نموذج واجهة المستخدم إلى String
  static String _convertWidgetTypeToString(ui_model.DashboardWidgetType type) {
    switch (type) {
      case ui_model.DashboardWidgetType.pieChart:
        return 'taskStatusChart';
      case ui_model.DashboardWidgetType.lineChart:
        return 'taskProgressChart';
      case ui_model.DashboardWidgetType.barChart:
        return 'userPerformanceChart';
      case ui_model.DashboardWidgetType.table:
        return 'taskList';
      case ui_model.DashboardWidgetType.kpiCard:
        return 'kpi';
      default:
        return 'custom';
    }
  }

  /// توليد استعلام SQL حسب نوع العنصر
  static String _generateQueryForWidgetType(String type) {
    switch (type) {
      case 'taskStatusChart':
        return 'SELECT status, COUNT(*) as count FROM tasks GROUP BY status';
      case 'taskProgressChart':
        return 'SELECT date(created_at) as date, COUNT(*) as count FROM tasks GROUP BY date(created_at)';
      case 'userPerformanceChart':
        return 'SELECT u.name as userName, COUNT(t.id) as taskCount FROM users u LEFT JOIN tasks t ON u.id = t.assigned_to GROUP BY u.id';
      case 'departmentPerformanceChart':
        return 'SELECT d.name as departmentName, COUNT(t.id) as taskCount FROM departments d LEFT JOIN tasks t ON d.id = t.department_id GROUP BY d.id';
      case 'timeTrackingChart':
        return 'SELECT date(start_time) as date, SUM((end_time - start_time) / 3600000.0) as hours FROM time_tracking_entries GROUP BY date(start_time)';
      case 'taskList':
        return 'SELECT id, title, status, priority, due_date FROM tasks ORDER BY due_date LIMIT 10';
      case 'kpi':
        return 'SELECT COUNT(*) as total, SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed FROM tasks';
      default:
        return 'SELECT * FROM tasks LIMIT 10';
    }
  }

  /// الحصول على حقول المحاور حسب نوع العنصر
  static Map<String, String?> _getAxisFieldsForWidgetType(String type) {
    switch (type) {
      case 'taskStatusChart':
        return {
          'labelField': 'status',
          'valueField': 'count',
          'xAxisField': null,
          'yAxisField': null,
          'xAxisLabel': null,
          'yAxisLabel': null,
        };
      case 'taskProgressChart':
        return {
          'xAxisField': 'date',
          'yAxisField': 'count',
          'xAxisLabel': 'التاريخ',
          'yAxisLabel': 'عدد المهام',
          'labelField': null,
          'valueField': null,
        };
      case 'userPerformanceChart':
        return {
          'xAxisField': 'userName',
          'yAxisField': 'taskCount',
          'xAxisLabel': 'المستخدم',
          'yAxisLabel': 'عدد المهام',
          'labelField': null,
          'valueField': null,
        };
      case 'departmentPerformanceChart':
        return {
          'xAxisField': 'departmentName',
          'yAxisField': 'taskCount',
          'xAxisLabel': 'القسم',
          'yAxisLabel': 'عدد المهام',
          'labelField': null,
          'valueField': null,
        };
      case 'timeTrackingChart':
        return {
          'xAxisField': 'date',
          'yAxisField': 'hours',
          'xAxisLabel': 'التاريخ',
          'yAxisLabel': 'الساعات',
          'labelField': null,
          'valueField': null,
        };
      case 'kpi':
        return {
          'valueField': 'completed',
          'labelField': null,
          'xAxisField': null,
          'yAxisField': null,
          'xAxisLabel': null,
          'yAxisLabel': null,
        };
      default:
        return {
          'xAxisField': null,
          'yAxisField': null,
          'labelField': null,
          'valueField': null,
          'xAxisLabel': null,
          'yAxisLabel': null,
        };
    }
  }

  /// الحصول على لون الرأس من الإعدادات
  static Color _getHeaderColorFromSettings(Map<String, dynamic> settings) {
    if (settings['headerColor'] != null) {
      try {
        return Color(int.parse(settings['headerColor']));
      } catch (e) {
        debugPrint('خطأ في تحليل لون الرأس: $e');
      }
    }
    return Colors.blue;
  }

  /// إنشاء عنصر جديد لواجهة المستخدم
  static ui_model.DashboardWidget createNewUIWidget({
    required String dashboardId,
    required String title,
    required ui_model.DashboardWidgetType type,
    required Offset position,
    required Size size,
  }) {
    return ui_model.DashboardWidget(
      id: const Uuid().v4(),
      dashboardId: dashboardId,
      title: title,
      type: type,
      dataSource: 'api_database',
      query: _generateQueryForWidgetType(_convertWidgetTypeToString(type)),
      position: position,
      size: size,
      headerColor: Colors.blue,
      orientation: ui_model.ChartOrientation.vertical,
      showValues: true,
      showLabels: true,
      showGrid: true,
      showLegend: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdById: 'current_user',
    );
  }

  /// تحويل من نموذج dashboard_widget_model.dart إلى نموذج dashboard_model.dart
  static simple_model.SimpleDashboardWidget convertToDashboardModel(ui_model.DashboardWidget uiWidget) {
    return simple_model.SimpleDashboardWidget(
      id: uiWidget.id,
      title: uiWidget.title,
      type: _convertWidgetTypeToString(uiWidget.type),
      row: uiWidget.position.dy.round(),
      column: uiWidget.position.dx.round(),
      width: uiWidget.size.width.round(),
      height: uiWidget.size.height.round(),
      settings: {
        'showValues': uiWidget.showValues,
        'showLabels': uiWidget.showLabels,
        'showGrid': uiWidget.showGrid,
        'showLegend': uiWidget.showLegend,
        'headerColor': uiWidget.headerColor?.toARGB32().toRadixString(16),
        'orientation': uiWidget.orientation.toString(),
        'xAxisField': uiWidget.xAxisField,
        'yAxisField': uiWidget.yAxisField,
        'labelField': uiWidget.labelField,
        'valueField': uiWidget.valueField,
      },
      isExpandable: true,
      isExpanded: false,
      isRefreshable: true,
    );
  }

  /// تحويل من نموذج dashboard_model.dart إلى نموذج dashboard_widget_model.dart
  static ui_model.DashboardWidget convertToWidgetModel(simple_model.SimpleDashboardWidget simpleWidget, String dashboardId) {
    return ui_model.DashboardWidget(
      id: simpleWidget.id,
      dashboardId: dashboardId,
      title: simpleWidget.title,
      type: _convertWidgetTypeFromString(simpleWidget.type),
      dataSource: 'api_database',
      query: _generateQueryForWidgetType(simpleWidget.type),
      position: Offset(simpleWidget.column.toDouble(), simpleWidget.row.toDouble()),
      size: Size(simpleWidget.width.toDouble(), simpleWidget.height.toDouble()),
      headerColor: _getHeaderColorFromSettings(simpleWidget.settings),
      xAxisField: simpleWidget.settings['xAxisField'],
      yAxisField: simpleWidget.settings['yAxisField'],
      labelField: simpleWidget.settings['labelField'],
      valueField: simpleWidget.settings['valueField'],
      orientation: ui_model.ChartOrientation.vertical,
      showValues: simpleWidget.settings['showValues'] ?? true,
      showLabels: simpleWidget.settings['showLabels'] ?? true,
      showGrid: simpleWidget.settings['showGrid'] ?? true,
      showLegend: simpleWidget.settings['showLegend'] ?? true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdById: 'current_user',
    );
  }

}
