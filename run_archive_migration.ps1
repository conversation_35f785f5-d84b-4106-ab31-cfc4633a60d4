# PowerShell script to run Archive Models Migration
# سكريبت لتشغيل ترحيل نماذج الأرشيف

param(
    [string]$ServerInstance = ".\sqlexpress",
    [string]$Database = "databasetasks",
    [string]$SqlFile = "webApi\webApi\SQL_Scripts\UpdateArchiveModels.sql"
)

Write-Host "=== تشغيل ترحيل نماذج الأرشيف ===" -ForegroundColor Green
Write-Host "الخادم: $ServerInstance" -ForegroundColor Yellow
Write-Host "قاعدة البيانات: $Database" -ForegroundColor Yellow
Write-Host "ملف SQL: $SqlFile" -ForegroundColor Yellow
Write-Host ""

try {
    # التحقق من وجود ملف SQL
    if (-not (Test-Path $SqlFile)) {
        Write-Host "خطأ: ملف SQL غير موجود: $SqlFile" -ForegroundColor Red
        exit 1
    }

    # التحقق من وجود sqlcmd
    $sqlcmdPath = Get-Command sqlcmd -ErrorAction SilentlyContinue
    if (-not $sqlcmdPath) {
        Write-Host "خطأ: sqlcmd غير موجود. يرجى تثبيت SQL Server Command Line Utilities" -ForegroundColor Red
        Write-Host "أو استخدم SQL Server Management Studio لتشغيل الملف يدوياً" -ForegroundColor Yellow
        exit 1
    }

    Write-Host "تشغيل سكريبت ترحيل الأرشيف..." -ForegroundColor Cyan
    
    # تشغيل سكريبت SQL
    $result = sqlcmd -S $ServerInstance -d $Database -i $SqlFile -E
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "تم تشغيل الترحيل بنجاح!" -ForegroundColor Green
        Write-Host ""
        Write-Host "=== التغييرات المطبقة ===" -ForegroundColor Green
        Write-Host "✓ إضافة عمود color إلى جدول archive_categories" -ForegroundColor White
        Write-Host "✓ إضافة عمود icon إلى جدول archive_categories" -ForegroundColor White
        Write-Host "✓ إضافة عمود is_active إلى جدول archive_categories" -ForegroundColor White
        Write-Host "✓ إضافة عمود content إلى جدول archive_documents" -ForegroundColor White
        Write-Host "✓ إضافة عمود created_by إلى جدول archive_documents" -ForegroundColor White
        Write-Host "✓ إضافة عمود created_at إلى جدول archive_documents" -ForegroundColor White
        Write-Host "✓ إضافة عمود description إلى جدول archive_tags" -ForegroundColor White
        Write-Host "✓ إضافة عمود is_active إلى جدول archive_tags" -ForegroundColor White
        Write-Host "✓ إنشاء جدول archive_document_tags" -ForegroundColor White
        Write-Host ""
        Write-Host "يمكنك الآن تشغيل API بدون أخطاء في الأعمدة المفقودة" -ForegroundColor Green
    } else {
        Write-Host "فشل في تشغيل الترحيل" -ForegroundColor Red
        Write-Host "رمز الخطأ: $LASTEXITCODE" -ForegroundColor Red
    }
    
    # عرض النتيجة
    if ($result) {
        Write-Host ""
        Write-Host "=== نتيجة الترحيل ===" -ForegroundColor Cyan
        Write-Host $result
    }
    
} catch {
    Write-Host "خطأ في تشغيل الترحيل: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "=== البديل اليدوي ===" -ForegroundColor Yellow
    Write-Host "1. افتح SQL Server Management Studio" -ForegroundColor White
    Write-Host "2. اتصل بالخادم: $ServerInstance" -ForegroundColor White
    Write-Host "3. اختر قاعدة البيانات: $Database" -ForegroundColor White
    Write-Host "4. افتح الملف: $SqlFile" -ForegroundColor White
    Write-Host "5. شغل السكريبت" -ForegroundColor White
    exit 1
}

Write-Host ""
Write-Host "انتهى الترحيل" -ForegroundColor Green
