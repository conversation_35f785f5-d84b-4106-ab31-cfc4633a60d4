import 'dart:io' if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:syncfusion_flutter_charts/charts.dart' as charts; // استخدام Syncfusion Charts بدلاً من fl_chart
import '../../controllers/task_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/contribution_report_controller.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../utils/date_formatter.dart';
import '../../utils/responsive_helper.dart';
import '../../models/task_model.dart';
import '../../models/task_progress_models.dart';

import '../../screens/reports/contribution_reports_screen.dart';

/// كلاس لتمثيل نقطة بيانات في المخطط
class _ChartData {
  final String x;
  final double y;
  final Color color;

  _ChartData(this.x, this.y, this.color);
}



/// علامة تبويب تفاصيل تقدم المهمة
/// تعرض تفاصيل تقدم المهمة ومساهمة كل مستخدم
///
/// تم تنفيذ:
/// - رسم بياني لتتبع تقدم المهمة عبر الزمن
/// - إمكانية تصدير بيانات التقدم بتنسيق CSV
/// - إمكانية تحميل صور أو ملفات كدليل على إنجاز المهمة
/// - تتبع الوقت المستغرق في المهام
/// - تحسين الرسوم البيانية وإضافة المزيد من التحليلات
/// - إضافة تقارير أداء المستخدمين
/// - إضافة إمكانية مقارنة تقدم المهمة مع المهام المشابهة
///
/// جميع المهام المطلوبة تم تنفيذها بنجاح!
class TaskProgressTab extends StatefulWidget {
  const TaskProgressTab({super.key});

  @override
  State<TaskProgressTab> createState() => _TaskProgressTabState();
}

class _TaskProgressTabState extends State<TaskProgressTab> {
  final TaskController _taskController = Get.find<TaskController>();
  final AuthController _authController = Get.find<AuthController>();
  final UserController _userController = Get.find<UserController>();
  final TextEditingController _progressController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // تحميل بيانات تتبع الوقت والمهام المشابهة عند تهيئة الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTimeTrackingData();
      _loadSimilarTasksData();
    });
  }

  @override
  void dispose() {
    _progressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تحميل بيانات تتبع الوقت
  Future<void> _loadTimeTrackingData() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      await _taskController.loadTaskTimeTracking(task.id);
      await _taskController.loadTaskTimeTrackingSummary(task.id);
    } catch (e) {
      // تجاهل الأخطاء هنا لأن هذه ميزة إضافية
    }
  }

  /// تحميل بيانات المهام المشابهة
  Future<void> _loadSimilarTasksData() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      await _taskController.loadSimilarTasks(task.id);
    } catch (e) {
      // تجاهل الأخطاء هنا لأن هذه ميزة إضافية
    }
  }

  /// بدء تتبع الوقت
  Future<void> _startTimeTracking() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      await _taskController.startTimeTracking(
        task.id,
        _authController.currentUser.value!.id,
        description: 'العمل على المهمة: ${task.title}',
      );

      Get.snackbar(
        'تم بدء تتبع الوقت'.tr,
        'تم بدء تتبع الوقت للمهمة بنجاح'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء بدء تتبع الوقت'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// إيقاف تتبع الوقت
  Future<void> _stopTimeTracking() async {
    try {
      // البحث عن أول سجل نشط
      final activeEntries = await _taskController.getActiveTimeTrackingEntries();
      if (activeEntries.isNotEmpty) {
        // عرض مربع حوار لإدخال وصف للوقت المستغرق
        final description = await _showTimeTrackingDescriptionDialog();

        // إذا ضغط المستخدم على إلغاء، نعود بدون إيقاف التتبع
        if (description == null) return;

        // تحديث وصف السجل النشط
        final activeEntry = activeEntries.first;
        // تحديث الوصف في السجل (يمكن إضافة هذه الوظيفة لاحقًا)

        // إنهاء تتبع الوقت
        await _taskController.endTimeTracking(activeEntry.id);

        Get.snackbar(
          'تم إيقاف تتبع الوقت'.tr,
          'تم إيقاف تتبع الوقت للمهمة بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      } else {
        Get.snackbar(
          'تنبيه'.tr,
          'لا يوجد تتبع وقت نشط حاليًا'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.amber,
          colorText: Colors.black,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء إيقاف تتبع الوقت'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// عرض مربع حوار لإدخال وصف للوقت المستغرق
  Future<String?> _showTimeTrackingDescriptionDialog() async {
    final TextEditingController descriptionController = TextEditingController();

    try {
      return await Get.dialog<String>(
        AlertDialog(
          title: Text('وصف العمل المنجز'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'يرجى إدخال وصف للعمل الذي قمت به خلال هذه الفترة'.tr,
                style: AppStyles.bodyMedium,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: InputDecoration(
                  labelText: 'الوصف'.tr,
                  hintText: 'مثال: إصلاح مشكلة في واجهة المستخدم'.tr,
                  border: const OutlineInputBorder(),
                ),
                maxLines: 3,
                textDirection: ui.TextDirection.rtl,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('إلغاء'.tr),
            ),
            ElevatedButton(
              onPressed: () => Get.back(result: descriptionController.text),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: Text('حفظ'.tr),
            ),
          ],
        ),
      );
    } finally {
      descriptionController.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final task = _taskController.currentTask;
      if (task == null) {
        return const Center(child: Text('لم يتم تحميل المهمة'));
      }

      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان القسم
              Text(
                'تفاصيل تقدم المهمة',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: 16),

              // ملخص التقدم
              _buildProgressSummary(),
              const SizedBox(height: 24),

              // رسم بياني لتتبع تقدم المهمة عبر الزمن
              Text(
                'تطور نسبة الإنجاز عبر الزمن',
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 16),
              // استخدام ارتفاع متغير بناءً على حجم الشاشة
              SizedBox(
                height: ResponsiveHelper.isMobile(context) ? 180 : 220,
                child: _buildProgressChart(),
              ),
              const SizedBox(height: 24),

              // أزرار التحكم
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildUpdateProgressButton(),
                    const SizedBox(width: 8),
                    _buildExportButton(),
                    const SizedBox(width: 8),
                    _buildFileUploadButton(),
                    const SizedBox(width: 8),
                    _buildContributionReportButton(),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // مساهمات المستخدمين
              Text(
                'مساهمات المستخدمين',
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 8),
              _buildUserContributions(),
              const SizedBox(height: 24),

              // تتبع الوقت المستغرق
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'تتبع الوقت المستغرق',
                    style: AppStyles.titleMedium,
                  ),
                  _buildTimeTrackingButton(),
                ],
              ),
              const SizedBox(height: 8),
              _buildTimeTrackingSection(),
              const SizedBox(height: 24),

              // تقارير أداء المستخدمين
              _buildUserPerformanceReports(),
              const SizedBox(height: 24),

              // مقارنة مع مهام مشابهة
              _buildTaskComparisonChart3(),
              const SizedBox(height: 24),

              // سجل تحديثات التقدم
              Text(
                'سجل تحديثات التقدم',
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 8),
              // استخدام ارتفاع متغير بدلاً من ثابت
              SizedBox(
                height: ResponsiveHelper.isMobile(context) ? 200 : 300,
                child: SingleChildScrollView(
                  child: _buildProgressHistory(),
                ),
              ),
              const SizedBox(height: 16), // Bottom padding
            ],
          ),
        ),
      );
    });
  }

  /// بناء ملخص التقدم
  Widget _buildProgressSummary() {
    final task = _taskController.currentTask;
    final progressSummary = _taskController.progressSummary.value;

    if (task == null) {
      return const SizedBox.shrink();
    }

    // استخدام ResponsiveHelper للتحقق من حجم الشاشة
    final isSmallScreen = ResponsiveHelper.isMobile(context);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: isSmallScreen
            ? _buildProgressSummaryColumn(task, progressSummary)
            : _buildProgressSummaryRow(task, progressSummary),
      ),
    );
  }

  /// بناء ملخص التقدم في عمود (للشاشات الصغيرة)
  Widget _buildProgressSummaryColumn(Task task, TaskProgressSummary? progressSummary) {
    final statusColor = _getStatusColor(task.status);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان ونسبة الإكمال
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'نسبة الإكمال الحالية:',
              style: AppStyles.bodyLarge,
            ),
            Text(
              '${task.completionPercentage.toInt()}%',
              style: AppStyles.titleLarge.copyWith(
                color: statusColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // مؤشر دائري للتقدم
        Center(
          child: CircularPercentIndicator(
            radius: 60.0,
            lineWidth: 12.0,
            percent: task.completionPercentage / 100,
            center: Text(
              '${task.completionPercentage.toInt()}%',
              style: AppStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            progressColor: statusColor,
            backgroundColor: Colors.grey.shade200,
            circularStrokeCap: CircularStrokeCap.round,
            animation: true,
            animationDuration: 1000,
          ),
        ),
        const SizedBox(height: 16),

        // معلومات إضافية
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'آخر تحديث:',
              style: AppStyles.bodyMedium,
            ),
            Text(
              progressSummary != null
                  ? DateFormatter.formatDateTime(progressSummary.lastUpdated)
                  : 'غير متوفر',
              style: AppStyles.bodyMedium,
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'عدد المساهمين:',
              style: AppStyles.bodyMedium,
            ),
            Text(
              progressSummary != null
                  ? '${progressSummary.userContributions.length}'
                  : '0',
              style: AppStyles.bodyMedium,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء ملخص التقدم في صف (للشاشات المتوسطة والكبيرة)
  Widget _buildProgressSummaryRow(Task task, TaskProgressSummary? progressSummary) {
    final statusColor = _getStatusColor(task.status);

    // استخدام Row مباشرة بدون SingleChildScrollView وConstrainedBox
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // مؤشر دائري للتقدم
        CircularPercentIndicator(
          radius: 70.0,
          lineWidth: 15.0,
          percent: task.completionPercentage / 100,
          center: Text(
            '${task.completionPercentage.toInt()}%',
            style: AppStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          progressColor: statusColor,
          backgroundColor: Colors.grey.shade200,
          circularStrokeCap: CircularStrokeCap.round,
          animation: true,
          animationDuration: 1000,
        ),
        const SizedBox(width: 24),

        // معلومات إضافية
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // إضافة هذا لمنع الامتداد الزائد
            children: [
              Text(
                'نسبة الإكمال الحالية:',
                style: AppStyles.bodyLarge,
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: task.completionPercentage / 100,
                backgroundColor: Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                minHeight: 10,
                borderRadius: BorderRadius.circular(5),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'آخر تحديث:',
                    style: AppStyles.bodyMedium,
                  ),
                  Flexible(
                    child: Text(
                      progressSummary != null
                          ? DateFormatter.formatDateTime(progressSummary.lastUpdated)
                          : 'غير متوفر',
                      style: AppStyles.bodyMedium,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'عدد المساهمين:',
                    style: AppStyles.bodyMedium,
                  ),
                  Text(
                    progressSummary != null
                        ? '${progressSummary.userContributions.length}'
                        : '0',
                    style: AppStyles.bodyMedium,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء زر تحديث التقدم
  Widget _buildUpdateProgressButton() {
    return ElevatedButton.icon(
      onPressed: _showProgressUpdateDialog,
      icon: const Icon(Icons.update),
      label: Text('تحديث التقدم'.tr),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  /// بناء مساهمات المستخدمين
  Widget _buildUserContributions() {
    final progressSummary = _taskController.progressSummary.value;

    if (progressSummary == null || progressSummary.userContributions.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: Text('لا توجد مساهمات حتى الآن'),
          ),
        ),
      );
    }

    // تحديد ما إذا كان يجب عرض الرسم البياني الدائري أو لا
    final showPieChart = progressSummary.userContributions.length > 1;

    // تحديد الارتفاع الأقصى بناءً على حجم الشاشة
    final maxHeight = ResponsiveHelper.isMobile(context)
        ? 150.0
        : ResponsiveHelper.isTablet(context)
            ? 200.0
            : 250.0;

    final contributionsCount = progressSummary.userContributions.length;
    // حساب الارتفاع المطلوب (كل مساهمة تحتاج تقريبًا 50 بكسل)
    final requiredHeight = contributionsCount * 50.0;

    // استخدام الارتفاع المطلوب إذا كان أقل من الحد الأقصى
    final containerHeight = requiredHeight < maxHeight ? requiredHeight : maxHeight;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // عنوان القسم
            Text(
              'مساهمات المستخدمين',
              style: AppStyles.titleSmall,
            ),
            const SizedBox(height: 16),

            // عرض الرسم البياني الدائري والقائمة جنبًا إلى جنب في الشاشات الكبيرة
            if (ResponsiveHelper.isDesktop(context) && showPieChart)
              SizedBox(
                height: containerHeight + 32, // تحديد ارتفاع ثابت للصف
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // الرسم البياني الدائري
                    Expanded(
                      flex: 2,
                      child: _buildContributionPieChart(progressSummary),
                    ),
                    const SizedBox(width: 24),
                    // قائمة المساهمات
                    Expanded(
                      flex: 3,
                      child: contributionsCount > 3
                          ? SingleChildScrollView(
                              child: _buildContributionsList(progressSummary),
                            )
                          : _buildContributionsList(progressSummary),
                    ),
                  ],
                ),
              )
            // عرض الرسم البياني الدائري فوق القائمة في الشاشات الصغيرة
            else
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // الرسم البياني الدائري (إذا كان هناك أكثر من مستخدم)
                  if (showPieChart) ...[
                    SizedBox(
                      height: 200,
                      child: _buildContributionPieChart(progressSummary),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // قائمة المساهمات
                  SizedBox(
                    height: containerHeight + 32,
                    child: contributionsCount > 3
                        ? SingleChildScrollView(
                            child: _buildContributionsList(progressSummary),
                          )
                        : _buildContributionsList(progressSummary),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  /// بناء الرسم البياني الدائري لمساهمات المستخدمين
  Widget _buildContributionPieChart(TaskProgressSummary progressSummary) {
    // إنشاء قائمة بالألوان للرسم البياني
    final List<Color> colors = [
      AppColors.primary,
      AppColors.accent,
      Colors.orange,
      Colors.green,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.amber,
    ];

    // إنشاء قائمة بالمساهمات
    final List<MapEntry<String, double>> sortedContributions =
        progressSummary.userContributions.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    // تحويل البيانات إلى تنسيق Syncfusion
    final List<_ChartData> chartData = [];

    // إنشاء قائمة بأسماء المستخدمين
    final Map<String, String> userNames = {};

    // تحويل البيانات إلى تنسيق Syncfusion
    for (int i = 0; i < sortedContributions.length; i++) {
      final entry = sortedContributions[i];
      final color = colors[i % colors.length];

      chartData.add(_ChartData(entry.key, entry.value, color));

      // حفظ معرف المستخدم للحصول على اسمه لاحقًا
      userNames[entry.key] = '';
    }

    return FutureBuilder<void>(
      future: Future.wait(
        userNames.keys.map((userId) async {
          userNames[userId] = await _userController.getUserNameById(userId);
        }),
      ),
      builder: (context, snapshot) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // الرسم البياني الدائري باستخدام Syncfusion
            SizedBox(
              height: 80,
              child: charts.SfCircularChart(
                // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
                enableMultiSelection: true,
                selectionGesture: charts.ActivationMode.singleTap,

                // تفعيل Tooltip المتقدم
                tooltipBehavior: charts.TooltipBehavior(
                  enable: true,
                  format: 'point.x: point.y%',
                  header: '',
                  canShowMarker: false,
                  activationMode: charts.ActivationMode.singleTap,
                  animationDuration: 500,
                ),

                // إعدادات المفتاح
                legend: charts.Legend(isVisible: false),

                series: <charts.CircularSeries>[
                  charts.PieSeries<_ChartData, String>(
                    dataSource: chartData,
                    xValueMapper: (_ChartData data, _) => data.x,
                    yValueMapper: (_ChartData data, _) => data.y,
                    pointColorMapper: (_ChartData data, _) => data.color,

                    // إعدادات التسميات
                    dataLabelSettings: const charts.DataLabelSettings(
                      isVisible: true,
                      labelPosition: charts.ChartDataLabelPosition.inside,
                      textStyle: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),

                    // إعدادات التفاعل
                    enableTooltip: true,
                    animationDuration: 1000,

                    // تفعيل التحديد
                    selectionBehavior: charts.SelectionBehavior(
                      enable: true,
                    ),
                  ),
                ],
              ),
            ),

            // مفتاح الرسم البياني
            const SizedBox(height: 2), // تقليل المسافة أكثر
            SizedBox(
              height: 20, // تقليل ارتفاع المفتاح
              child: SingleChildScrollView(
                child: Wrap(
                  spacing: 4, // تقليل المسافة بين العناصر
                  runSpacing: 2, // تقليل المسافة بين الصفوف
                  alignment: WrapAlignment.center,
                  children: List.generate(
                    sortedContributions.length,
                    (index) {
                      final entry = sortedContributions[index];
                      final color = colors[index % colors.length];
                      // No necesitamos el nombre de usuario para esta versión simplificada

                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Flexible(
                            child: Text(
                              '${entry.value.toInt()}%',
                              style: AppStyles.bodySmall.copyWith(fontSize: 9),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Eliminamos la función _buildPieChartLegendItem ya que ahora implementamos directamente
  // la lógica en el widget Column

  /// بناء شريحة لعرض نوع المساهمة
  Widget _buildContributionTypeChip(String? evidenceType) {
    // تحديد النص واللون بناءً على نوع المساهمة
    String text = 'تحديث تقدم';
    Color color = AppColors.primary;
    IconData icon = Icons.update;

    if (evidenceType != null) {
      switch (evidenceType) {
        case 'transfer':
          text = 'تحويل مهمة';
          color = Colors.orange;
          icon = Icons.swap_horiz;
          break;
        case 'file':
          text = 'إرفاق ملف';
          color = Colors.green;
          icon = Icons.attach_file;
          break;
        case 'comment':
          text = 'تعليق';
          color = Colors.blue;
          icon = Icons.comment;
          break;
        case 'manual':
          text = 'تحديث يدوي';
          color = Colors.purple;
          icon = Icons.edit;
          break;
        case 'auto':
          text = 'حساب تلقائي';
          color = Colors.teal;
          icon = Icons.auto_awesome;
          break;
        case 'activity':
          text = 'نشاط متعدد';
          color = Colors.indigo;
          icon = Icons.diversity_3;
          break;
        default:
          text = 'تحديث تقدم';
          color = AppColors.primary;
          icon = Icons.update;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha(25), // 0.1 * 255 = ~25
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(50), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: AppStyles.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المساهمات
  Widget _buildContributionsList(TaskProgressSummary progressSummary) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: progressSummary.userContributions.entries.map((entry) {
        final userId = entry.key;
        final contribution = entry.value;

        return FutureBuilder<String>(
          future: _userController.getUserNameById(userId),
          builder: (context, snapshot) {
            final userName = snapshot.data ?? 'مستخدم غير معروف';

            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: AppColors.primary,
                    child: Text(
                      userName.isNotEmpty ? userName[0] : '?',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          userName,
                          style: AppStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: contribution / 100,
                          backgroundColor: Colors.grey.shade200,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            AppColors.accent,
                          ),
                          minHeight: 6,
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${contribution.toInt()}%',
                    style: AppStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            );
          }
        );
      }).toList(),
    );
  }

  /// بناء سجل تحديثات التقدم
  Widget _buildProgressHistory() {
    final progressTrackers = _taskController.progressTrackers;

    if (progressTrackers.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: Text('لا توجد تحديثات حتى الآن'),
          ),
        ),
      );
    }

    return SizedBox(
      height: 300, // تحديد ارتفاع ثابت للقائمة
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: progressTrackers.length,
        itemBuilder: (context, index) {
          final tracker = progressTrackers[index];

          return FutureBuilder<String>(
            future: _userController.getUserNameById(tracker['userId']?.toString() ?? '0'),
            builder: (context, snapshot) {
              final userName = snapshot.data ?? 'مستخدم غير معروف';

              return Card(
                margin: const EdgeInsets.only(bottom: 8.0),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 14,
                                backgroundColor: AppColors.primary,
                                child: Text(
                                  userName.isNotEmpty ? userName[0] : '?',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                userName,
                                style: AppStyles.bodyMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          Text(
                            tracker['updatedAt'] != null
                                ? DateFormatter.formatDateTime(DateTime.fromMillisecondsSinceEpoch((tracker['updatedAt'] as int) * 1000))
                                : 'غير متوفر',
                            style: AppStyles.bodySmall.copyWith(
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            'المساهمة:',
                            style: AppStyles.bodyMedium,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${(tracker['contributionPercentage'] as num?)?.toInt() ?? 0}%',
                            style: AppStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                      // عرض نوع المساهمة (تحويل، تحديث تقدم، إلخ)
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            'نوع المساهمة:',
                            style: AppStyles.bodyMedium,
                          ),
                          const SizedBox(width: 8),
                          _buildContributionTypeChip(tracker['evidenceType'] as String?),
                        ],
                      ),

                      // عرض الملاحظات إذا كانت موجودة
                      if (tracker['notes'] != null && (tracker['notes'] as String).isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          'ملاحظات:',
                          style: AppStyles.bodyMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          tracker['notes'] as String,
                          style: AppStyles.bodyMedium,
                        ),
                      ],

                      // عرض وصف الدليل إذا كان موجودًا
                      if (tracker['evidenceDescription'] != null && (tracker['evidenceDescription'] as String).isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          'تفاصيل إضافية:',
                          style: AppStyles.bodyMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          tracker['evidenceDescription'] as String,
                          style: AppStyles.bodyMedium.copyWith(
                            color: Colors.grey.shade700,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            }
          );
        },
      ),
    );
  }

  /// عرض مربع حوار تحديث التقدم
  void _showProgressUpdateDialog({String? initialNote}) {
    final task = _taskController.currentTask;
    if (task == null) return;

    // تعيين القيمة الأولية
    _progressController.text = task.completionPercentage.toInt().toString();
    _notesController.text = initialNote ?? '';

    Get.dialog(
      AlertDialog(
        title: Text('تحديث التقدم'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _progressController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'نسبة الإكمال'.tr,
                suffixText: '%',
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'ملاحظات'.tr,
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'.tr),
          ),
          ElevatedButton(
            onPressed: () => _updateProgress(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: Text('تحديث'.tr),
          ),
        ],
      ),
    );
  }

  /// تحديث تقدم المهمة
  Future<void> _updateProgress() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    // التحقق من صحة القيمة المدخلة
    final progressText = _progressController.text.trim();
    if (progressText.isEmpty) {
      Get.snackbar(
        'خطأ'.tr,
        'يرجى إدخال نسبة الإكمال'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    final percentage = double.tryParse(progressText);
    if (percentage == null || percentage < 0 || percentage > 100) {
      Get.snackbar(
        'خطأ'.tr,
        'يرجى إدخال نسبة إكمال صحيحة بين 0 و 100'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // إغلاق مربع الحوار
    Get.back();

    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    // تحديث تقدم المهمة
    try {
      // استخدام الواجهة المحسنة لتحديث التقدم مع دعم المرفقات
      final result = await _taskController.updateTaskProgress(
        task.id,
        _authController.currentUser.value!.id,
        percentage,
        notes: _notesController.text.trim(),
        // يمكن إضافة معرف المرفق وتفاصيل الدليل هنا في المستقبل
      );

      // إغلاق مؤشر التحميل
      Get.back();

      if (result) {
        Get.snackbar(
          'نجاح'.tr,
          'تم تحديث تقدم المهمة بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );

        // بدء تتبع الوقت تلقائياً إذا كانت نسبة التقدم أقل من 100%
        if (percentage < 100 && !_taskController.isTrackingTime.value) {
          _startTimeTrackingAuto();
        }
        // إيقاف تتبع الوقت تلقائياً إذا كانت نسبة التقدم 100%
        else if (percentage >= 100 && _taskController.isTrackingTime.value) {
          _stopTimeTrackingAuto();
        }
      } else {
        Get.snackbar(
          'خطأ'.tr,
          _taskController.error,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      Get.snackbar(
        'خطأ'.tr,
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// بدء تتبع الوقت تلقائياً عند تحديث التقدم
  Future<void> _startTimeTrackingAuto() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      await _taskController.startTimeTracking(
        task.id,
        _authController.currentUser.value!.id,
        description: 'العمل على المهمة: ${task.title}',
      );

      Get.snackbar(
        'تنبيه'.tr,
        'تم بدء تتبع الوقت تلقائياً'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue.shade100,
        colorText: Colors.blue.shade800,
      );
    } catch (e) {
      // تجاهل الأخطاء هنا لأن هذه ميزة إضافية
    }
  }

  /// إيقاف تتبع الوقت تلقائياً عند اكتمال المهمة
  Future<void> _stopTimeTrackingAuto() async {
    try {
      // البحث عن أول سجل نشط
      final activeEntries = await _taskController.getActiveTimeTrackingEntries();
      if (activeEntries.isNotEmpty) {
        await _taskController.endTimeTracking(activeEntries.first.id);

        Get.snackbar(
          'تنبيه'.tr,
          'تم إيقاف تتبع الوقت تلقائياً'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.blue.shade100,
          colorText: Colors.blue.shade800,
        );
      }
    } catch (e) {
      // تجاهل الأخطاء هنا لأن هذه ميزة إضافية
    }
  }

  /// بناء زر تصدير البيانات
  Widget _buildExportButton() {
    return ElevatedButton.icon(
      onPressed: _exportProgressData,
      icon: const Icon(Icons.download),
      label: Text('تصدير البيانات'.tr),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.accent,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  /// بناء زر تقارير المساهمات
  Widget _buildContributionReportButton() {
    final task = _taskController.currentTask;
    if (task == null) return const SizedBox.shrink();

    return ElevatedButton.icon(
      onPressed: () => _showContributionReportOptions(task.id.toString()),
      icon: const Icon(Icons.analytics),
      label: Text('تقارير المساهمات'.tr),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  /// عرض خيارات تقارير المساهمات
  void _showContributionReportOptions(String taskId) {
    Get.dialog(
      AlertDialog(
        title: const Text('تقارير المساهمات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.add_chart, color: Colors.indigo),
              title: const Text('إنشاء تقرير جديد'),
              subtitle: const Text('إنشاء تقرير مساهمات جديد لهذه المهمة'),
              onTap: () {
                Get.back();
                _createNewContributionReport(taskId);
              },
            ),
            ListTile(
              leading: const Icon(Icons.list_alt, color: Colors.blue),
              title: const Text('عرض التقارير الحالية'),
              subtitle: const Text('عرض جميع تقارير المساهمات المتاحة'),
              onTap: () {
                Get.back();
                _openContributionReportsScreen();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// إنشاء تقرير مساهمات جديد
  Future<void> _createNewContributionReport(String taskId) async {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    int periodDays = 30;

    // تعيين عنوان افتراضي
    final task = _taskController.currentTask;
    if (task != null) {
      titleController.text = 'تقرير مساهمات: ${task.title}';
    }

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('إنشاء تقرير مساهمات جديد'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان التقرير
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان التقرير *',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // وصف التقرير
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف التقرير',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),

              // الفترة الزمنية
              const Text('الفترة الزمنية:'),
              const SizedBox(height: 8),
              StatefulBuilder(
                builder: (context, setState) {
                  return DropdownButtonFormField<int>(
                    value: periodDays,
                    decoration: const InputDecoration(
                      labelText: 'الفترة الزمنية',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem<int>(
                        value: 7,
                        child: Text('آخر 7 أيام'),
                      ),
                      const DropdownMenuItem<int>(
                        value: 30,
                        child: Text('آخر 30 يوم'),
                      ),
                      const DropdownMenuItem<int>(
                        value: 90,
                        child: Text('آخر 3 أشهر'),
                      ),
                      const DropdownMenuItem<int>(
                        value: 180,
                        child: Text('آخر 6 أشهر'),
                      ),
                      const DropdownMenuItem<int>(
                        value: 365,
                        child: Text('آخر سنة'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        periodDays = value!;
                      });
                    },
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (titleController.text.trim().isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يرجى إدخال عنوان للتقرير',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
                return;
              }
              Get.back(result: true);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );

    if (result == true) {
      // عرض مؤشر التحميل
      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      try {
        // إنشاء وحدة تحكم تقارير المساهمات إذا لم تكن موجودة
        final ContributionReportController reportController;
        if (!Get.isRegistered<ContributionReportController>()) {
          reportController = Get.put(ContributionReportController());
        } else {
          reportController = Get.find<ContributionReportController>();
        }

        final endDate = DateTime.now();
        final startDate = endDate.subtract(Duration(days: periodDays));

        final report = await reportController.createReport(
          title: titleController.text.trim(),
          description: descriptionController.text.trim(),
          taskId: taskId,
          periodDays: periodDays,
          startDate: startDate,
          endDate: endDate,
        );

        // إغلاق مؤشر التحميل
        Get.back();

        if (report != null) {
          Get.snackbar(
            'نجاح',
            'تم إنشاء تقرير المساهمات بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green.shade100,
            colorText: Colors.green.shade800,
          );

          // فتح شاشة تقارير المساهمات
          Get.to(() => const ContributionReportsScreen());
        } else {
          Get.snackbar(
            'خطأ',
            'فشل إنشاء التقرير: ${reportController.error.value}',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        }
      } catch (e) {
        // إغلاق مؤشر التحميل
        Get.back();

        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء إنشاء التقرير: ${e.toString()}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      } finally {
        titleController.dispose();
        descriptionController.dispose();
      }
    }
  }

  /// فتح شاشة تقارير المساهمات
  void _openContributionReportsScreen() {
    Get.to(() => const ContributionReportsScreen());
  }

  /// تصدير بيانات التقدم بتنسيق CSV
  void _exportProgressData() {
    final task = _taskController.currentTask;
    if (task == null) return;

    final progressTrackers = _taskController.progressTrackers;
    if (progressTrackers.isEmpty) {
      Get.snackbar(
        'تنبيه'.tr,
        'لا توجد بيانات للتصدير'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.amber.shade100,
        colorText: Colors.amber.shade800,
      );
      return;
    }

    // عرض مربع حوار التأكيد
    Get.dialog(
      AlertDialog(
        title: Text('تصدير البيانات'.tr),
        content: Text('هل تريد تصدير بيانات تقدم المهمة بتنسيق CSV؟'.tr),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'.tr),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _generateAndDownloadCSV(task, progressTrackers.map((tracker) => TaskProgressTracker.fromJson(tracker)).toList());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: Text('تصدير'.tr),
          ),
        ],
      ),
    );
  }

  /// إنشاء وتنزيل ملف CSV
  Future<void> _generateAndDownloadCSV(Task task, List<TaskProgressTracker> progressTrackers) async {
    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      // إنشاء محتوى ملف CSV
      final csvContent = StringBuffer();

      // إضافة رأس الجدول
      csvContent.writeln('المعرف,المستخدم,نسبة المساهمة,التاريخ,الملاحظات');

      // إضافة بيانات التقدم
      for (final tracker in progressTrackers) {
        final userName = await _userController.getUserNameById(tracker.userId.toString());
        final date = DateFormatter.formatDateTime(DateTime.fromMillisecondsSinceEpoch((tracker.updatedAt) * 1000));
        final notes = tracker.notes?.replaceAll(',', ' ') ?? '';

        csvContent.writeln('${tracker.id},$userName,${tracker.contributionPercentage},$date,$notes');
      }

      // سيتم تنفيذ تنزيل الملف في المرحلة القادمة
      // - في الويب، سيتم استخدام html.AnchorElement
      // - في التطبيقات المحلية، سيتم حفظ الملف في مجلد التنزيلات

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة نجاح
      Get.snackbar(
        'نجاح'.tr,
        'تم تصدير البيانات بنجاح'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء تصدير البيانات: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// بناء رسم بياني لتتبع تقدم المهمة عبر الزمن
  Widget _buildProgressChart() {
    final progressTrackers = _taskController.progressTrackers;
    final progressSummary = _taskController.progressSummary.value;

    if (progressTrackers.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: Text('لا توجد بيانات كافية لعرض الرسم البياني'),
          ),
        ),
      );
    }

    // تحويل Map إلى TaskProgressTracker objects
    final trackerObjects = progressTrackers.map((tracker) {
      return TaskProgressTracker.fromJson(tracker);
    }).toList();

    // ترتيب سجلات التقدم حسب التاريخ (من الأقدم إلى الأحدث)
    final sortedTrackers = List<TaskProgressTracker>.from(trackerObjects)
      ..sort((a, b) => a.updatedAt.compareTo(b.updatedAt));

    // حساب التقدم الإجمالي (مؤقت - بدلاً من المخطط المعطل)
    double totalProgress = 0;

    for (int i = 0; i < sortedTrackers.length; i++) {
      final tracker = sortedTrackers[i];

      // تراكم نسبة التقدم
      totalProgress += tracker.contributionPercentage;
      if (totalProgress > 100) totalProgress = 100;

      // ملاحظة: تم إزالة إضافة نقطة البيانات لأن المخطط معطل مؤقتاً
    }

    // إضافة معلومات إضافية للرسم البياني
    String chartTitle = 'تطور نسبة الإنجاز عبر الزمن';
    String? subtitle;

    // إضافة معلومات معدل التقدم اليومي والوقت المتوقع للإكمال
    if (progressSummary != null && progressSummary.dailyProgressRate != null) {
      final dailyRate = progressSummary.dailyProgressRate!.toStringAsFixed(1);
      subtitle = 'معدل التقدم: $dailyRate% يومياً';

      if (progressSummary.estimatedDaysToCompletion != null && progressSummary.totalPercentage < 100) {
        final daysLeft = progressSummary.estimatedDaysToCompletion!.toStringAsFixed(1);
        subtitle += ' | الوقت المتوقع للإكمال: $daysLeft يوم';
      }
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الرسم البياني
              Text(
                chartTitle,
                style: AppStyles.titleSmall,
              ),

              // معلومات إضافية (إذا كانت متوفرة)
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppStyles.bodySmall.copyWith(color: Colors.grey.shade700),
                ),
              ],

              const SizedBox(height: 16),

              // Placeholder للرسم البياني (مؤقت)
              Container(
                height: ResponsiveHelper.isMobile(context) ? 180 : 220,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade50,
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.show_chart,
                        color: AppColors.primary,
                        size: 48,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'مخطط تقدم المهمة',
                        style: AppStyles.titleMedium.copyWith(
                          color: AppColors.primary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'التقدم الإجمالي: ${totalProgress.toStringAsFixed(1)}%',
                        style: AppStyles.bodyLarge.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              // معلومات إضافية عن التقدم
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'ملخص التقدم',
                        style: AppStyles.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'عدد التحديثات: ${sortedTrackers.length}',
                        style: AppStyles.bodyMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'آخر تحديث: ${DateFormat('yyyy/MM/dd').format(DateTime.fromMillisecondsSinceEpoch(sortedTrackers.last.updatedAt * 1000))}',
                        style: AppStyles.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء مقارنة تقدم المهمة مع المهام المشابهة (مؤقت)
  Widget _buildTaskComparisonChart3() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade50,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics,
              color: AppColors.primary,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              'مقارنة المهام',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              'سيتم تفعيل المخطط قريباً',
              style: AppStyles.bodySmall.copyWith(
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }



  /// بناء زر بدء/إيقاف تتبع الوقت
  Widget _buildTimeTrackingButton() {
    return Obx(() {
      final isTracking = _taskController.isTrackingTime.value;
      final isLoading = _taskController.isLoadingTimeTracking.value;

      if (isLoading) {
        return const SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
          ),
        );
      }

      return ElevatedButton.icon(
        onPressed: isTracking ? _stopTimeTracking : _startTimeTracking,
        icon: Icon(isTracking ? Icons.stop : Icons.play_arrow),
        label: Text(isTracking ? 'إيقاف التتبع'.tr : 'بدء التتبع'.tr),
        style: ElevatedButton.styleFrom(
          backgroundColor: isTracking ? Colors.red : AppColors.accent,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
      );
    });
  }

  /// بناء قسم تتبع الوقت
  Widget _buildTimeTrackingSection() {
    return Obx(() {
      final timeEntries = _taskController.timeEntries;
      final timeTrackingSummary = _taskController.timeTrackingSummary.value;
      final isTracking = _taskController.isTrackingTime.value;

      // إذا لم تكن هناك سجلات تتبع وقت، نعرض رسالة
      if (timeEntries.isEmpty && !isTracking) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.timer_off,
                    size: 48,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لم يتم تسجيل أي وقت لهذه المهمة بعد'.tr,
                    style: AppStyles.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اضغط على زر "بدء التتبع" لبدء تسجيل الوقت المستغرق في العمل على هذه المهمة'.tr,
                    style: AppStyles.bodySmall.copyWith(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      }

      // تحديد الارتفاع الأقصى بناءً على حجم الشاشة
      final maxHeight = ResponsiveHelper.isMobile(context)
          ? 200.0
          : ResponsiveHelper.isTablet(context)
              ? 250.0
              : 300.0;

      return Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ملخص تتبع الوقت
              if (timeTrackingSummary != null) ...[
                _buildTimeTrackingSummary(timeTrackingSummary),
                const Divider(height: 24),
              ],

              // عنوان سجلات تتبع الوقت
              Text(
                'سجلات تتبع الوقت'.tr,
                style: AppStyles.titleSmall,
              ),
              const SizedBox(height: 8),

              // قائمة سجلات تتبع الوقت
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: maxHeight,
                ),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: timeEntries.length,
                  itemBuilder: (context, index) {
                    return _buildTimeEntryItem(timeEntries[index]);
                  },
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  /// بناء ملخص تتبع الوقت
  Widget _buildTimeTrackingSummary(TaskTimeTrackingSummary summary) {
    // تحويل الدقائق إلى ساعات ودقائق
    final hours = (summary.totalMinutes / 60).floor();
    final minutes = (summary.totalMinutes % 60).round();

    // تنسيق الوقت الإجمالي
    final totalTimeText = hours > 0
        ? '$hours ساعة و $minutes دقيقة'.tr
        : '$minutes دقيقة'.tr;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إجمالي الوقت المستغرق
        Row(
          children: [
            const Icon(Icons.access_time, color: AppColors.primary),
            const SizedBox(width: 8),
            Text(
              'إجمالي الوقت المستغرق:'.tr,
              style: AppStyles.bodyMedium,
            ),
            const SizedBox(width: 8),
            Text(
              totalTimeText,
              style: AppStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // آخر تحديث
        Row(
          children: [
            const Icon(Icons.update, color: Colors.grey, size: 20),
            const SizedBox(width: 8),
            Text(
              'آخر تحديث:'.tr,
              style: AppStyles.bodySmall,
            ),
            const SizedBox(width: 8),
            Text(
              summary.lastUpdated != null
                  ? DateFormatter.formatDateTime(summary.lastUpdated!)
                  : 'غير متوفر',
              style: AppStyles.bodySmall.copyWith(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء عنصر سجل تتبع الوقت
  Widget _buildTimeEntryItem(TimeTrackingEntry entry) {
    // حساب المدة
    final duration = entry.endTime != null
        ? entry.endTime!.difference(entry.startTime)
        : DateTime.now().difference(entry.startTime);

    // تنسيق المدة
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final durationText = hours > 0
        ? '$hours ساعة و $minutes دقيقة'.tr
        : '$minutes دقيقة'.tr;

    // تحديد لون الحالة
    final statusColor = entry.endTime != null
        ? Colors.green
        : AppColors.accent;

    return FutureBuilder<String>(
      future: _userController.getUserNameById(entry.userId.toString()),
      builder: (context, snapshot) {
        final userName = snapshot.data ?? 'مستخدم غير معروف'.tr;

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الصف العلوي: المستخدم والحالة
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 14,
                          backgroundColor: AppColors.primary,
                          child: Text(
                            userName.isNotEmpty ? userName[0] : '?',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          userName,
                          style: AppStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withAlpha(25), // 0.1 * 255 = ~25
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            entry.endTime != null ? Icons.check_circle : Icons.access_time,
                            size: 14,
                            color: statusColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            entry.endTime != null ? 'مكتمل'.tr : 'جاري'.tr,
                            style: AppStyles.bodySmall.copyWith(
                              color: statusColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // الصف الثاني: وقت البدء والانتهاء
                Row(
                  children: [
                    const Icon(Icons.play_circle_outline, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      'بدأ:'.tr,
                      style: AppStyles.bodySmall,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      DateFormatter.formatDateTime(entry.startTime),
                      style: AppStyles.bodySmall,
                    ),
                    const SizedBox(width: 16),
                    if (entry.endTime != null) ...[
                      const Icon(Icons.stop_circle_outlined, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        'انتهى:'.tr,
                        style: AppStyles.bodySmall,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        DateFormatter.formatDateTime(entry.endTime!),
                        style: AppStyles.bodySmall,
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 8),

                // الصف الثالث: المدة والوصف
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.timer, size: 16, color: AppColors.primary),
                    const SizedBox(width: 4),
                    Text(
                      'المدة:'.tr,
                      style: AppStyles.bodySmall,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      durationText,
                      style: AppStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),

                // الوصف (إذا كان موجودًا)
                if (entry.description != null && entry.description!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    'الوصف:'.tr,
                    style: AppStyles.bodySmall,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    entry.description!,
                    style: AppStyles.bodySmall.copyWith(
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء قسم تقارير أداء المستخدمين
  Widget _buildUserPerformanceReports() {
    final progressSummary = _taskController.progressSummary.value;
    final timeTrackingSummary = _taskController.timeTrackingSummary.value;

    if (progressSummary == null || progressSummary.userContributions.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: Text('لا توجد بيانات كافية لعرض تقارير الأداء'),
          ),
        ),
      );
    }

    // إنشاء قائمة بالمستخدمين المساهمين
    final List<MapEntry<String, double>> sortedContributions =
        progressSummary.userContributions.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'تقارير أداء المستخدمين',
                  style: AppStyles.titleSmall,
                ),
                ElevatedButton.icon(
                  onPressed: _exportUserPerformanceReport,
                  icon: const Icon(Icons.download, size: 16),
                  label: Text('تصدير التقرير'.tr),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.accent,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // جدول تقارير الأداء
            _buildPerformanceTable(sortedContributions, timeTrackingSummary),
          ],
        ),
      ),
    );
  }

  /// بناء جدول تقارير الأداء
  Widget _buildPerformanceTable(
    List<MapEntry<String, double>> contributions,
    TaskTimeTrackingSummary? timeTrackingSummary,
  ) {
    // إنشاء قائمة بأعمدة الجدول
    final List<DataColumn> columns = [
      DataColumn(
        label: Text('المستخدم'.tr, style: AppStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold)),
      ),
      DataColumn(
        label: Text('نسبة المساهمة'.tr, style: AppStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold)),
        numeric: true,
      ),
      if (timeTrackingSummary != null)
        DataColumn(
          label: Text('الوقت المستغرق'.tr, style: AppStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold)),
          numeric: true,
        ),
      DataColumn(
        label: Text('الكفاءة'.tr, style: AppStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold)),
        numeric: true,
      ),
    ];

    return FutureBuilder<List<DataRow>>(
      future: _buildPerformanceTableRows(contributions, timeTrackingSummary),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final rows = snapshot.data ?? [];

        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            columnSpacing: 24,
            horizontalMargin: 12,
            headingRowHeight: 40,
            dataRowMinHeight: 48,
            dataRowMaxHeight: 64,
            columns: columns,
            rows: rows,
          ),
        );
      },
    );
  }

  /// بناء صفوف جدول الأداء
  Future<List<DataRow>> _buildPerformanceTableRows(
    List<MapEntry<String, double>> contributions,
    TaskTimeTrackingSummary? timeTrackingSummary,
  ) async {
    // إنشاء قائمة بصفوف الجدول
    final List<DataRow> rows = [];

    for (int i = 0; i < contributions.length; i++) {
      final entry = contributions[i];
      final userId = entry.key;
      final contribution = entry.value;

      // الحصول على اسم المستخدم
      final userName = await _userController.getUserNameById(userId);

      // حساب الوقت المستغرق (إذا كان متاحًا)
      String timeSpent = '-';
      double efficiency = 0;

      if (timeTrackingSummary != null && timeTrackingSummary.userMinutes.containsKey(userId)) {
        final minutes = timeTrackingSummary.userMinutes[userId]!;

        // تنسيق الوقت المستغرق
        final hours = (minutes / 60).floor();
        final mins = (minutes % 60).round();

        if (hours > 0) {
          timeSpent = '$hours ساعة و $mins دقيقة';
        } else {
          timeSpent = '$mins دقيقة';
        }

        // حساب الكفاءة (نسبة المساهمة / الوقت المستغرق بالساعات)
        if (minutes > 0) {
          efficiency = contribution / (minutes / 60);
        }
      }

      // إضافة صف للجدول
      rows.add(
        DataRow(
          cells: [
            DataCell(Text(userName)),
            DataCell(Text('${contribution.toInt()}%')),
            if (timeTrackingSummary != null)
              DataCell(Text(timeSpent)),
            DataCell(
              efficiency > 0
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text('${efficiency.toStringAsFixed(1)}%/ساعة'),
                        const SizedBox(width: 4),
                        Icon(
                          efficiency > 20 ? Icons.trending_up : Icons.trending_flat,
                          color: efficiency > 20 ? Colors.green : Colors.orange,
                          size: 16,
                        ),
                      ],
                    )
                  : const Text('-'),
            ),
          ],
        ),
      );
    }

    return rows;
  }

  /// تصدير تقرير أداء المستخدمين
  void _exportUserPerformanceReport() {
    final task = _taskController.currentTask;
    if (task == null) return;

    final progressSummary = _taskController.progressSummary.value;
    final timeTrackingSummary = _taskController.timeTrackingSummary.value;

    if (progressSummary == null || progressSummary.userContributions.isEmpty) {
      Get.snackbar(
        'تنبيه'.tr,
        'لا توجد بيانات كافية لتصدير التقرير'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.amber.shade100,
        colorText: Colors.amber.shade800,
      );
      return;
    }

    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    // إنشاء محتوى ملف CSV
    final csvContent = StringBuffer();

    // إضافة معلومات المهمة
    csvContent.writeln('تقرير أداء المستخدمين');
    csvContent.writeln('المهمة: ${task.title}');
    csvContent.writeln('نسبة الإكمال: ${task.completionPercentage.toInt()}%');
    csvContent.writeln('تاريخ التقرير: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}');
    csvContent.writeln('');

    // إضافة رأس الجدول
    if (timeTrackingSummary != null) {
      csvContent.writeln('المستخدم,نسبة المساهمة,الوقت المستغرق (دقائق),الكفاءة (نسبة/ساعة)');
    } else {
      csvContent.writeln('المستخدم,نسبة المساهمة');
    }

    // إضافة بيانات المستخدمين
    Future.wait(
      progressSummary.userContributions.entries.map((entry) async {
        final userId = entry.key;
        final contribution = entry.value;
        final userName = await _userController.getUserNameById(userId);

        if (timeTrackingSummary != null && timeTrackingSummary.userMinutes.containsKey(userId)) {
          final minutes = timeTrackingSummary.userMinutes[userId]!;
          final efficiency = minutes > 0 ? contribution / (minutes / 60) : 0;

          csvContent.writeln('$userName,${contribution.toInt()},$minutes,${efficiency.toStringAsFixed(1)}');
        } else {
          csvContent.writeln('$userName,${contribution.toInt()}');
        }
      }),
    ).then((_) {
      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة نجاح
      Get.snackbar(
        'نجاح'.tr,
        'تم تصدير تقرير أداء المستخدمين بنجاح'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );

      // في تطبيق حقيقي، هنا سيتم حفظ الملف أو تنزيله
      // يمكن استخدام نفس الآلية المستخدمة في _generateAndDownloadCSV
    }).catchError((e) {
      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء تصدير التقرير: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    });
  }







  /// إضافة زر تحميل الملفات إلى واجهة المستخدم
  Widget _buildFileUploadButton() {
    return ElevatedButton.icon(
      onPressed: _showFileUploadDialog,
      icon: const Icon(Icons.upload_file),
      label: Text('إرفاق ملف'.tr),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.accent,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  /// عرض مربع حوار تحميل الملفات
  void _showFileUploadDialog() {
    final task = _taskController.currentTask;
    if (task == null) return;

    Get.dialog(
      AlertDialog(
        title: Text('إرفاق ملف'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.image),
              title: Text('اختيار صورة'.tr),
              onTap: () {
                Get.back();
                _pickImage();
              },
            ),
            ListTile(
              leading: const Icon(Icons.upload_file),
              title: Text('اختيار ملف'.tr),
              onTap: () {
                Get.back();
                _pickFile();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'.tr),
          ),
        ],
      ),
    );
  }

  /// اختيار صورة من المعرض أو الكاميرا
  Future<void> _pickImage() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    // عرض خيارات اختيار الصورة
    Get.dialog(
      AlertDialog(
        title: Text('اختيار صورة'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: Text('من المعرض'.tr),
              onTap: () async {
                Get.back();

                try {
                  final ImagePicker picker = ImagePicker();
                  final XFile? image = await picker.pickImage(source: ImageSource.gallery);

                  if (image != null) {
                    _uploadProgressEvidence(image.path, 'image/${image.name.split('.').last}', image.name);
                  }
                } catch (e) {
                  Get.snackbar(
                    'خطأ'.tr,
                    'حدث خطأ أثناء اختيار الصورة: ${e.toString()}'.tr,
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red.shade100,
                    colorText: Colors.red.shade800,
                  );
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: Text('من الكاميرا'.tr),
              onTap: () async {
                Get.back();

                try {
                  final ImagePicker picker = ImagePicker();
                  final XFile? image = await picker.pickImage(source: ImageSource.camera);

                  if (image != null) {
                    _uploadProgressEvidence(image.path, 'image/${image.name.split('.').last}', image.name);
                  }
                } catch (e) {
                  Get.snackbar(
                    'خطأ'.tr,
                    'حدث خطأ أثناء التقاط الصورة: ${e.toString()}'.tr,
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red.shade100,
                    colorText: Colors.red.shade800,
                  );
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'.tr),
          ),
        ],
      ),
    );
  }

  /// اختيار ملف من نظام الملفات
  Future<void> _pickFile() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      final result = await FilePicker.platform.pickFiles();

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.path != null) {
          _uploadProgressEvidence(file.path!, file.extension != null ? 'application/${file.extension}' : 'application/octet-stream', file.name);
        } else {
          // في حالة الويب، قد لا يكون هناك مسار للملف
          Get.snackbar(
            'تنبيه'.tr,
            'لا يمكن الوصول إلى مسار الملف. قد يكون هذا بسبب قيود المتصفح.'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.amber.shade100,
            colorText: Colors.amber.shade800,
          );
        }
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء اختيار الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تحميل ملف كدليل على إنجاز المهمة
  Future<void> _uploadProgressEvidence(String filePath, String fileType, String fileName) async {
    final task = _taskController.currentTask;
    if (task == null) return;

    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      // تحميل الملف
      final file = File(filePath);

      // إضافة المرفق إلى المهمة
      await _taskController.addAttachment(
        task.id,
        _authController.currentUser.value!.id,
        file,
        description: 'دليل إنجاز: $fileName ($fileType)',
      );

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة نجاح
      Get.snackbar(
        'نجاح'.tr,
        'تم تحميل الملف بنجاح'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );

      // تحديث تقدم المهمة مع إضافة ملاحظة حول المرفق
      _showProgressUpdateDialog(
        initialNote: 'تم إرفاق ملف: $fileName كدليل على الإنجاز',
      );
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء تحميل الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(int status) {
    switch (status) {
      case 1: // pending
        return AppColors.warning;
      case 2: // in progress
        return AppColors.info;
      case 3: // waiting for info
        return AppColors.warning;
      case 4: // completed
        return AppColors.success;
      case 5: // cancelled
        return AppColors.error;
      default:
        return AppColors.warning;
    }
  }


}
