import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/task_priority_models.dart';
import 'package:get/get.dart';
import '../services/api/task_priority_api_service.dart';

/// متحكم أولويات المهام
class TaskPriorityController extends GetxController {
  final TaskPriorityApiService _apiService = TaskPriorityApiService();

  // قوائم أولويات المهام
  final RxList<TaskPriority> _allPriorities = <TaskPriority>[].obs;
  final RxList<TaskPriority> _filteredPriorities = <TaskPriority>[].obs;

  // أولوية المهمة الحالية
  final Rx<TaskPriority?> _currentPriority = Rx<TaskPriority?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxString _errorMessage = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<TaskPriority> get allPriorities => _allPriorities;
  List<TaskPriority> get taskPriorities => _filteredPriorities;
  List<TaskPriority> get filteredPriorities => _filteredPriorities;
  TaskPriority? get currentPriority => _currentPriority.value;
  RxBool get isLoading => _isLoading;
  RxString get errorMessage => _errorMessage;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllPriorities();
  }

  /// تحميل جميع أولويات المهام
  Future<void> loadAllPriorities() async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final priorities = await _apiService.getAllPriorities();
      _allPriorities.assignAll(priorities);
      _applyFilters();
      debugPrint('تم تحميل ${priorities.length} أولوية مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل أولويات المهام: $e';
      _errorMessage.value = 'خطأ في تحميل أولويات المهام: $e';
      debugPrint('خطأ في تحميل أولويات المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل أولويات المهام مع خيار تضمين غير النشطة
  Future<void> loadTaskPriorities({bool includeInactive = false}) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final priorities = await _apiService.getAllPriorities();
      _allPriorities.assignAll(priorities);

      // تطبيق مرشح النشط/غير النشط
      if (!includeInactive) {
        _filteredPriorities.assignAll(priorities.where((p) => p.isActive).toList());
      } else {
        _filteredPriorities.assignAll(priorities);
      }

      debugPrint('تم تحميل ${priorities.length} أولوية مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل أولويات المهام: $e';
      _errorMessage.value = 'خطأ في تحميل أولويات المهام: $e';
      debugPrint('خطأ في تحميل أولويات المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على أولوية مهمة بالمعرف
  Future<void> getPriorityById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final priority = await _apiService.getPriorityById(id);
      if (priority != null) {
        _currentPriority.value = priority;
        debugPrint('تم تحميل أولوية المهمة: ${priority.name}');
      } else {
        _error.value = 'أولوية المهمة غير موجودة';
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل أولوية المهمة: $e';
      debugPrint('خطأ في تحميل أولوية المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء أولوية مهمة جديدة
  Future<bool> createPriority(TaskPriority priority) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newPriority = await _apiService.createPriority(priority);
      _allPriorities.add(newPriority);
      _applyFilters();
      debugPrint('تم إنشاء أولوية مهمة جديدة: ${newPriority.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء أولوية المهمة: $e';
      debugPrint('خطأ في إنشاء أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث أولوية مهمة
  Future<bool> updatePriority(int id, TaskPriority priority) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updatePriority(id, priority);
      final index = _allPriorities.indexWhere((p) => p.id == id);
      if (index != -1) {
        _allPriorities[index] = priority;
        _applyFilters();
      }
      debugPrint('تم تحديث أولوية المهمة: ${priority.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث أولوية المهمة: $e';
      debugPrint('خطأ في تحديث أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء أولوية مهمة جديدة بمعاملات مسماة
  Future<bool> createTaskPriority({
    required String name,
    required String description,
    required int level,
    String? color,
    String? icon,
    bool isDefault = false,
  }) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      // إنشاء كائن TaskPriority جديد
      final newPriority = TaskPriority(
        id: 0, // سيتم تعيينه من قبل الخادم
        name: name,
        description: description,
        level: level,
        color: color,
        icon: icon,
        isDefault: isDefault,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        isActive: true,
      );

      final createdPriority = await _apiService.createPriority(newPriority);

      // إضافة الأولوية الجديدة إلى القائمة
      _allPriorities.add(createdPriority);
      _applyFilters();
      debugPrint('تم إنشاء أولوية المهمة بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء أولوية المهمة: $e';
      _errorMessage.value = 'خطأ في إنشاء أولوية المهمة: $e';
      debugPrint('خطأ في إنشاء أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث أولوية مهمة
  Future<bool> updateTaskPriority(TaskPriority taskPriority) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final updatedPriority = await _apiService.updatePriority(
        taskPriority.id,
        taskPriority,
      );

      // تحديث القائمة المحلية
      final index = _allPriorities.indexWhere((p) => p.id == taskPriority.id);
      if (index != -1) {
        _allPriorities[index] = updatedPriority;
        _applyFilters();
      }
      debugPrint('تم تحديث أولوية المهمة بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث أولوية المهمة: $e';
      _errorMessage.value = 'خطأ في تحديث أولوية المهمة: $e';
      debugPrint('خطأ في تحديث أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف أولوية مهمة
  Future<bool> deleteTaskPriority(int id) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final success = await _apiService.deletePriority(id);
      if (success) {
        _allPriorities.removeWhere((p) => p.id == id);
        _applyFilters();
        debugPrint('تم حذف أولوية المهمة');
        return true;
      } else {
        _errorMessage.value = 'فشل في حذف أولوية المهمة';
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في حذف أولوية المهمة: $e';
      _errorMessage.value = 'خطأ في حذف أولوية المهمة: $e';
      debugPrint('خطأ في حذف أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف أولوية مهمة (الطريقة القديمة للتوافق)
  Future<bool> deletePriority(int id) async {
    return await deleteTaskPriority(id);
  }

  /// الحصول على الأولويات النشطة فقط
  List<TaskPriority> get activePriorities {
    return _allPriorities.where((priority) => priority.isActive).toList();
  }

  /// الحصول على أولوية افتراضية
  TaskPriority? get defaultPriority {
    return _allPriorities.firstWhereOrNull((priority) => priority.isDefault);
  }

  /// ترتيب الأولويات حسب المستوى
  List<TaskPriority> get prioritiesByLevel {
    var sorted = List<TaskPriority>.from(_allPriorities);
    sorted.sort((a, b) => a.level.compareTo(b.level));
    return sorted;
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allPriorities.where((priority) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!priority.name.toLowerCase().contains(query) &&
            !(priority.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !priority.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredPriorities.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllPriorities();
  }
}
