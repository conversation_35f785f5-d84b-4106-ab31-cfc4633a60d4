import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_progress_models.dart';
import '../services/api/task_progress_trackers_api_service.dart';

/// متحكم متتبعات تقدم المهام
class TaskProgressTrackersController extends GetxController {
  final TaskProgressTrackersApiService _apiService = TaskProgressTrackersApiService();

  // قوائم المتتبعات
  final RxList<TaskProgressTracker> _allTrackers = <TaskProgressTracker>[].obs;
  final RxList<TaskProgressTracker> _filteredTrackers = <TaskProgressTracker>[].obs;
  final RxList<TaskProgressTracker> _taskTrackers = <TaskProgressTracker>[].obs;

  // المتتبع الحالي
  final Rx<TaskProgressTracker?> _currentTracker = Rx<TaskProgressTracker?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _taskFilter = Rx<int?>(null);
  final Rx<int?> _userFilter = Rx<int?>(null);
  final Rx<DateTime?> _dateFromFilter = Rx<DateTime?>(null);
  final Rx<DateTime?> _dateToFilter = Rx<DateTime?>(null);

  // إحصائيات التقدم
  final RxDouble _averageProgress = 0.0.obs;
  final RxInt _completedTasks = 0.obs;
  final RxInt _inProgressTasks = 0.obs;
  final RxInt _pendingTasks = 0.obs;

  // Getters
  List<TaskProgressTracker> get allTrackers => _allTrackers;
  List<TaskProgressTracker> get filteredTrackers => _filteredTrackers;
  List<TaskProgressTracker> get taskTrackers => _taskTrackers;
  TaskProgressTracker? get currentTracker => _currentTracker.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get taskFilter => _taskFilter.value;
  int? get userFilter => _userFilter.value;
  DateTime? get dateFromFilter => _dateFromFilter.value;
  DateTime? get dateToFilter => _dateToFilter.value;
  double get averageProgress => _averageProgress.value;
  int get completedTasks => _completedTasks.value;
  int get inProgressTasks => _inProgressTasks.value;
  int get pendingTasks => _pendingTasks.value;

  @override
  void onInit() {
    super.onInit();
    loadAllTrackers();
    loadStatistics();
  }

  /// تحميل جميع متتبعات تقدم المهام
  Future<void> loadAllTrackers() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final trackers = await _apiService.getAllTrackers();
      _allTrackers.assignAll(trackers);
      _applyFilters();
      debugPrint('تم تحميل ${trackers.length} متتبع تقدم مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل متتبعات تقدم المهام: $e';
      debugPrint('خطأ في تحميل متتبعات تقدم المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الإحصائيات
  Future<void> loadStatistics() async {
    try {
      final stats = await _apiService.getStatistics();
      _averageProgress.value = stats['averageProgress'] ?? 0.0;
      _completedTasks.value = stats['completedTasks'] ?? 0;
      _inProgressTasks.value = stats['inProgressTasks'] ?? 0;
      _pendingTasks.value = stats['pendingTasks'] ?? 0;
      debugPrint('تم تحميل إحصائيات تقدم المهام');
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات تقدم المهام: $e');
    }
  }

  /// الحصول على متتبع تقدم مهمة بالمعرف
  Future<void> getTrackerById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tracker = await _apiService.getTrackerById(id);
      _currentTracker.value = tracker;
      debugPrint('تم تحميل متتبع تقدم المهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل متتبع تقدم المهمة: $e';
      debugPrint('خطأ في تحميل متتبع تقدم المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء متتبع تقدم مهمة جديد
  Future<bool> createTracker(TaskProgressTracker tracker) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newTracker = await _apiService.createTracker(tracker);
      _allTrackers.add(newTracker);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم إنشاء متتبع تقدم مهمة جديد');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء متتبع تقدم المهمة: $e';
      debugPrint('خطأ في إنشاء متتبع تقدم المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث متتبع تقدم مهمة
  Future<bool> updateTracker(int id, TaskProgressTracker tracker) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateTracker(id, tracker);
      final index = _allTrackers.indexWhere((t) => t.id == id);
      if (index != -1) {
        _allTrackers[index] = tracker;
        _applyFilters();
      }
      await loadStatistics();
      debugPrint('تم تحديث متتبع تقدم المهمة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث متتبع تقدم المهمة: $e';
      debugPrint('خطأ في تحديث متتبع تقدم المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف متتبع تقدم مهمة
  Future<bool> deleteTracker(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteTracker(id);
      _allTrackers.removeWhere((t) => t.id == id);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم حذف متتبع تقدم المهمة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف متتبع تقدم المهمة: $e';
      debugPrint('خطأ في حذف متتبع تقدم المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على متتبعات تقدم مهمة محددة
  Future<void> getTrackersByTask(int taskId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final trackers = await _apiService.getTrackersByTask(taskId);
      _taskTrackers.assignAll(trackers);
      debugPrint('تم تحميل ${trackers.length} متتبع تقدم للمهمة $taskId');
    } catch (e) {
      _error.value = 'خطأ في تحميل متتبعات تقدم المهمة: $e';
      debugPrint('خطأ في تحميل متتبعات تقدم المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث تقدم المهمة
  Future<bool> updateTaskProgress(int taskId, double progressPercentage, String notes) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tracker = await _apiService.updateTaskProgress(taskId, progressPercentage, notes);
      final index = _allTrackers.indexWhere((t) => t.taskId == taskId);
      if (index != -1) {
        _allTrackers[index] = tracker;
      } else {
        _allTrackers.add(tracker);
      }
      _applyFilters();
      await loadStatistics();
      debugPrint('تم تحديث تقدم المهمة: $progressPercentage%');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث تقدم المهمة: $e';
      debugPrint('خطأ في تحديث تقدم المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على تقدم المهمة الحالي
  Future<double?> getCurrentTaskProgress(int taskId) async {
    try {
      final progress = await _apiService.getCurrentTaskProgress(taskId);
      debugPrint('تقدم المهمة الحالي: $progress%');
      return progress;
    } catch (e) {
      debugPrint('خطأ في الحصول على تقدم المهمة: $e');
      return null;
    }
  }

  /// الحصول على تقرير تقدم المهام
  Future<Map<String, dynamic>?> getProgressReport(DateTime? from, DateTime? to) async {
    try {
      final report = await _apiService.getProgressReport(from, to);
      debugPrint('تم تحميل تقرير تقدم المهام');
      return report;
    } catch (e) {
      debugPrint('خطأ في تحميل تقرير تقدم المهام: $e');
      return null;
    }
  }

  /// الحصول على المهام المتأخرة
  Future<List<Map<String, dynamic>>?> getOverdueTasks() async {
    try {
      final overdueTasks = await _apiService.getOverdueTasks();
      debugPrint('تم تحميل ${overdueTasks.length} مهمة متأخرة');
      return overdueTasks;
    } catch (e) {
      debugPrint('خطأ في تحميل المهام المتأخرة: $e');
      return null;
    }
  }

  /// الحصول على المهام المعرضة للخطر
  Future<List<Map<String, dynamic>>?> getAtRiskTasks() async {
    try {
      final atRiskTasks = await _apiService.getAtRiskTasks();
      debugPrint('تم تحميل ${atRiskTasks.length} مهمة معرضة للخطر');
      return atRiskTasks;
    } catch (e) {
      debugPrint('خطأ في تحميل المهام المعرضة للخطر: $e');
      return null;
    }
  }

  /// تصدير تقرير التقدم
  Future<String?> exportProgressReport(String format, DateTime? from, DateTime? to) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final filePath = await _apiService.exportProgressReport(format, from, to);
      debugPrint('تم تصدير تقرير التقدم: $filePath');
      return filePath;
    } catch (e) {
      _error.value = 'خطأ في تصدير تقرير التقدم: $e';
      debugPrint('خطأ في تصدير تقرير التقدم: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allTrackers.where((tracker) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (tracker.notes?.toLowerCase().contains(query) != true) {
          return false;
        }
      }

      // مرشح المهمة
      if (_taskFilter.value != null && tracker.taskId != _taskFilter.value) {
        return false;
      }

      // مرشح المستخدم
      if (_userFilter.value != null && tracker.updatedBy != _userFilter.value) {
        return false;
      }

      // مرشح التاريخ من
      if (_dateFromFilter.value != null) {
        final trackerDate = DateTime.fromMillisecondsSinceEpoch(tracker.updatedAt * 1000);
        if (trackerDate.isBefore(_dateFromFilter.value!)) {
          return false;
        }
      }

      // مرشح التاريخ إلى
      if (_dateToFilter.value != null) {
        final trackerDate = DateTime.fromMillisecondsSinceEpoch(tracker.updatedAt * 1000);
        if (trackerDate.isAfter(_dateToFilter.value!)) {
          return false;
        }
      }

      return true;
    }).toList();

    _filteredTrackers.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المهمة
  void setTaskFilter(int? taskId) {
    _taskFilter.value = taskId;
    _applyFilters();
  }

  /// تعيين مرشح المستخدم
  void setUserFilter(int? userId) {
    _userFilter.value = userId;
    _applyFilters();
  }

  /// تعيين مرشح التاريخ من
  void setDateFromFilter(DateTime? date) {
    _dateFromFilter.value = date;
    _applyFilters();
  }

  /// تعيين مرشح التاريخ إلى
  void setDateToFilter(DateTime? date) {
    _dateToFilter.value = date;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _taskFilter.value = null;
    _userFilter.value = null;
    _dateFromFilter.value = null;
    _dateToFilter.value = null;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllTrackers(),
      loadStatistics(),
    ]);
  }
}
